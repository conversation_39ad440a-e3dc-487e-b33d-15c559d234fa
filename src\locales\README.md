# Système de Traduction Scholarify

Ce document explique comment utiliser le système de traduction dans l'application Scholarify.

## Structure des Fichiers

```
src/
├── locales/
│   ├── fr/
│   │   └── common.json
│   ├── en/
│   │   └── common.json
│   └── README.md
├── hooks/
│   ├── useTranslation.ts
│   └── useNotifications.ts
└── components/
    ├── LanguageSelector.tsx
    ├── TranslatedNotification.tsx
    ├── NotificationContainer.tsx
    └── examples/
        └── TranslationExample.tsx
```

## Structure des Traductions

Les traductions sont organisées par dashboard et par page :

```json
{
  "common": {
    // Éléments communs à toute l'application
  },
  "navigation": {
    // Éléments de navigation
  },
  "dashboard": {
    "super-admin": {
      "pages": {
        "dashboard": { /* traductions pour la page dashboard */ },
        "schools": { /* traductions pour la page schools */ },
        "users": { /* traductions pour la page users */ }
      }
    },
    "school-admin": {
      "pages": {
        "dashboard": { /* traductions pour la page dashboard */ },
        "students": { /* traductions pour la page students */ }
      }
    },
    "teacher-dashboard": {
      "pages": {
        "dashboard": { /* traductions pour la page dashboard */ },
        "classes": { /* traductions pour la page classes */ }
      }
    }
  },
  "forms": {
    // Validation et placeholders de formulaires
  },
  "messages": {
    // Messages de succès, erreur, confirmation
  },
  "notifications": {
    // Notifications système
  },
  "language": {
    // Sélection de langue
  }
}
```

## Utilisation du Hook useTranslation

### Import et utilisation de base

```tsx
import { useTranslation } from '@/hooks/useTranslation';

function MyComponent() {
  const { t, currentLocale, setLocale } = useTranslation();

  return (
    <div>
      <h1>{t('common.title')}</h1>
      <p>{t('navigation.dashboard')}</p>
    </div>
  );
}
```

### Traductions avec paramètres

```tsx
const { t } = useTranslation();

// Avec paramètres
const welcomeMessage = t('dashboard.super-admin.pages.dashboard.welcome', { name: 'John' });
// Résultat: "Bienvenue, John" (en français) ou "Welcome, John" (en anglais)
```

### Fonction utilitaire pour les dashboards

```tsx
const { tDashboard } = useTranslation();

// Plus simple pour les traductions de dashboard
const title = tDashboard('super-admin', 'dashboard', 'title');
// Équivalent à: t('dashboard.super-admin.pages.dashboard.title')
```

### Changement de langue

```tsx
const { setLocale, availableLanguages } = useTranslation();

// Changer vers le français
setLocale('fr');

// Changer vers l'anglais
setLocale('en');

// Obtenir les langues disponibles
console.log(availableLanguages); // [{ code: 'fr', name: 'Français', flag: '🇫🇷' }, ...]
```

## Composants de Traduction

### LanguageSelector

Composant pour permettre aux utilisateurs de changer de langue :

```tsx
import LanguageSelector from '@/components/LanguageSelector';

// Variante dropdown complète
<LanguageSelector variant="dropdown" showLabel={true} />

// Variante compacte (juste le drapeau)
<LanguageSelector variant="compact" />

// Variante toggle simple
<LanguageSelector variant="toggle" />
```

### TranslatedNotification

Composant pour afficher des notifications traduites :

```tsx
import TranslatedNotification from '@/components/TranslatedNotification';

<TranslatedNotification
  type="success"
  messageKey="messages.success.saved"
  params={{ item: 'École' }}
  onClose={() => {}}
/>
```

### Hook useNotifications

Hook pour gérer les notifications traduites :

```tsx
import { useNotifications } from '@/hooks/useNotifications';

function MyComponent() {
  const { showSuccess, showError, showWarning, showInfo } = useNotifications();

  const handleSave = async () => {
    try {
      await saveData();
      showSuccess('messages.success.saved');
    } catch (error) {
      showError('messages.error.generic');
    }
  };

  return <button onClick={handleSave}>Sauvegarder</button>;
}
```

## Intégration dans les Layouts

### SuperLayout (exemple)

```tsx
import { useTranslation } from '@/hooks/useTranslation';

const SuperLayout = ({ children }) => {
  const { t } = useTranslation();

  const sidebarNav = [
    { icon: LayoutDashboard, name: t('navigation.dashboard'), href: '/super-admin/dashboard' },
    { icon: School, name: t('navigation.schools'), href: '/super-admin/schools' },
    // ...
  ];

  return (
    <div>
      {/* Navigation avec LanguageSelector */}
      <nav>
        <LanguageSelector variant="compact" />
      </nav>
      {/* Sidebar avec navigation traduite */}
      <aside>
        {sidebarNav.map(item => (
          <a key={item.href} href={item.href}>{item.name}</a>
        ))}
      </aside>
      {children}
    </div>
  );
};
```

## Bonnes Pratiques

1. **Clés descriptives** : Utilisez des clés descriptives plutôt que des abréviations
   ```tsx
   // ✅ Bon
   t('dashboard.super-admin.pages.schools.add_school')
   
   // ❌ Éviter
   t('dash.sa.sch.add')
   ```

2. **Paramètres** : Utilisez des paramètres pour les valeurs dynamiques
   ```tsx
   // ✅ Bon
   t('messages.success.item_saved', { item: 'École' })
   
   // ❌ Éviter
   t('messages.success.school_saved')
   ```

3. **Fallback** : Le système utilise automatiquement l'autre langue comme fallback

4. **Organisation** : Gardez les traductions organisées par contexte et par page

## Ajout de Nouvelles Traductions

1. Ajoutez la clé dans `src/locales/fr/common.json`
2. Ajoutez la traduction correspondante dans `src/locales/en/common.json`
3. Utilisez la clé dans votre composant avec `t('votre.nouvelle.cle')`

## Exemple Complet

Voir `src/components/examples/TranslationExample.tsx` pour un exemple complet d'utilisation du système de traduction.
