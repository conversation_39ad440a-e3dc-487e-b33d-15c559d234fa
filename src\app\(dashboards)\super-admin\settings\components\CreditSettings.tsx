// components/CreditSettings.tsx
"use client";

import React from "react";
import { CreditCard, Check, ArrowUp, ArrowDown } from "lucide-react"; // Ensure these are imported
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { useTranslation } from "@/hooks/useTranslation";

interface CreditSettingsProps {
  creditForm: {
    resell_price_per_credit: number;
    buy_price_per_credit: number;
  };
  setCreditForm: React.Dispatch<React.SetStateAction<{
    resell_price_per_credit: number;
    buy_price_per_credit: number;
  }>>;
  handleCreditSubmit: () => Promise<void>;
}

const CreditSettings: React.FC<CreditSettingsProps> = ({
  creditForm,
  setCreditForm,
  handleCreditSubmit,
}) => {
  const { tDashboard } = useTranslation();

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-foreground mb-4">{tDashboard('super-admin', 'settings', 'credit_title')}</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> {/* Grid for credit inputs */}
        <div>
          <label htmlFor="resell-price-input" className="block text-sm font-medium text-foreground mb-1 flex items-center space-x-1">
            <ArrowUp className="h-4 w-4 text-red-500" /> {/* Resell Icon */}
            <span>{tDashboard('super-admin', 'settings', 'resell_price_per_credit')}</span>
          </label>
          <Input
            id="resell-price-input"
            type="number"
            value={creditForm.resell_price_per_credit}
            onChange={(e: { target: { value: string; }; }) =>
              setCreditForm({
                ...creditForm,
                resell_price_per_credit: parseFloat(e.target.value),
              })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder={tDashboard('super-admin', 'settings', 'resell_price_per_credit')}
          />
        </div>
        <div>
          <label htmlFor="buy-price-input" className="block text-sm font-medium text-foreground mb-1 flex items-center space-x-1">
            <ArrowDown className="h-4 w-4 text-teal" /> {/* Buy Icon */}
            <span>{tDashboard('super-admin', 'settings', 'buy_price_per_credit')}</span>
          </label>
          <Input
            id="buy-price-input"
            type="number"
            value={creditForm.buy_price_per_credit}
            onChange={(e: { target: { value: string; }; }) =>
              setCreditForm({
                ...creditForm,
                buy_price_per_credit: parseFloat(e.target.value),
              })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder={tDashboard('super-admin', 'settings', 'buy_price_per_credit')}
          />
        </div>
      </div>
      <Button
        onClick={handleCreditSubmit}
        className="w-full py-3 bg-teal hover:opacity-90 text-white font-semibold rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <Check className="h-5 w-5" /> {/* Save/Check Icon */}
        <span>{tDashboard('super-admin', 'settings', 'update_credit_settings')}</span>
      </Button>
    </div>
  );
};

export default CreditSettings;
