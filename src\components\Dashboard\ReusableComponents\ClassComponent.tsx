"use client";

import React, { useEffect, useState, Suspense, useMemo } from "react";
import { useRouter } from "next/navigation";
import CircularLoader from "@/components/widgets/CircularLoader";
import NotificationCard from "@/components/NotificationCard";
import { motion } from "framer-motion";

import {
  getClasses,
  createClass,
  deleteClass,
  deleteMultipleClasses,
  deleteAllClasses,
  getClassesBySchool
} from "@/app/services/ClassServices";
import {
  createClassLevel,
  deleteClassLevel,
  getClassLevels,
  getClassLevelsBySchoolId
} from "@/app/services/ClassLevels";
import { getSchoolBy_id, getSchools } from "@/app/services/SchoolServices";
import { verifyPassword } from "@/app/services/UserServices";

import BulkDeleteModal from "@/components/modals/BulkDeleteModal";
import CreateLevelModal from "@/app/(dashboards)/super-admin/classes/components/CreateLevelModal";
import DeleteClassLevelModal from "@/app/(dashboards)/super-admin/classes/components/DeleteLevelModal";
import CreateClassModal from "@/app/(dashboards)/super-admin/classes/components/CreateClassModal";
import DeleteClassModal from "@/app/(dashboards)/super-admin/classes/components/DeleteClassModal";
import ClassLevelsTableWithBulkActions from "@/app/(dashboards)/super-admin/classes/components/ClassLevelsTableWithBulkActions";
import DataTableFix from "@/components/utils/TableFix";

import type { ClassSchema, ClassCreateSchema } from "@/app/models/ClassModel";
import type { ClassLevelSchema, ClassLevelCreateSchema } from "@/app/models/ClassLevel";
import type { SchoolSchema } from "@/app/models/SchoolModel";
import type { UserSchema } from "@/app/models/UserModel";
import type { NotificationType } from "@/components/NotificationCard";
import { useTranslation } from "@/hooks/useTranslation";

interface ManageClassesComponentProps {
  user: UserSchema;
}

const ManageClassesComponent: React.FC<ManageClassesComponentProps> = ({ user }) => {
  const router = useRouter();
  const { t, tDashboard } = useTranslation();

const schoolId = user.school_ids?.[0] ?? null;

  const [classes, setClasses] = useState<ClassSchema[]>([]);
  const [classLevels, setClassLevels] = useState<ClassLevelSchema[]>([]);
  const [school, setSchool] = useState<SchoolSchema | null>(null);

  const [classToDelete, setClassToDelete] = useState<ClassSchema | null>(null);
  const [levelToDelete, setLevelToDelete] = useState<ClassLevelSchema | null>(null);
  const [editingClass, setEditingClass] = useState<ClassSchema | undefined>(undefined);

  const [selectedClassLevelId, setSelectedClassLevelId] = useState("all");
  const [selectedClassIds, setSelectedClassIds] = useState<string[]>([]);
  const [bulkDeleteType, setBulkDeleteType] = useState<"selected" | "all">("selected");

  const [isClassModalOpen, setIsClassModalOpen] = useState(false);
  const [isLevelModalOpen, setIsLevelModalOpen] = useState(false);
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);

  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState<NotificationType>("success");
  const [isNotificationCard, setIsNotificationCard] = useState(false);

  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
  const [tableKey, setTableKey] = useState(0);

  useEffect(() => {
    const fetchAll = async () => {
      if (!schoolId) return;

      setLoading(true);
      try {
        const [allClasses, allLevels, school] = await Promise.all([
          getClassesBySchool(schoolId as string),
          getClassLevelsBySchoolId(schoolId as string), // Fetch levels for the school
          getSchoolBy_id(schoolId as string)
        ]);

        setClasses(allClasses.filter(cls => cls.school_id === schoolId));
        setClassLevels(allLevels.filter(lvl => lvl.school_id === schoolId));
        setSchool(school);
      } catch (error) {
        console.error("Error loading data", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAll();
  }, []);

const filteredClasses = React.useMemo(() => {
  return selectedClassLevelId === "all"
    ? classes
    : classes.filter((cls) => cls.class_level === selectedClassLevelId);
}, [classes, selectedClassLevelId]);

  const columns = [
    {
      header: tDashboard('school-admin', 'classes', 'class_level'),
      accessor: (row: ClassSchema) =>
        classLevels.find((lvl) => lvl._id === row.class_level)?.name || t('common.unknown')
    },
    { header: tDashboard('school-admin', 'classes', 'class_name'), accessor: (row: ClassSchema) => row.name },
    { header: tDashboard('school-admin', 'classes', 'class_code'), accessor: (row: ClassSchema) => row.class_code }
  ];

  const actions = [
    {
      label: tDashboard('school-admin', 'classes', 'view'),
      onClick: (cls: ClassSchema) =>
        router.push(`/school-admin/classes/view?classId=${cls.class_id}&schoolId=${schoolId}`)
    },
    {
      label: tDashboard('school-admin', 'classes', 'delete'),
      onClick: (cls: ClassSchema) => setClassToDelete(cls)
    }
  ];

  const handleSaveClass = async (classData: ClassSchema) => {
    if (!schoolId) return;

    setIsSubmitting(true);
    try {
      const payload: ClassCreateSchema = {
        name: classData.name,
        class_code: classData.class_code,
        class_level: classData.class_level,
        school_id: schoolId
      };

      const created = await createClass(payload);
      setClasses((prev) => [...prev, created]);
      showNotification(tDashboard('school-admin', 'classes', 'class_created_success'), "success");
    } catch (error) {
      showNotification(tDashboard('school-admin', 'classes', 'class_creation_failed'), "error");
    } finally {
      setIsClassModalOpen(false);
      setIsSubmitting(false);
    }
  };

  const handleDeleteClass = async (password: string) => {
    if (!classToDelete || !user) return;
    setIsSubmitting(true);
    const valid = await verifyPassword(password, user.email);
    if (!valid) return showNotification(tDashboard('school-admin', 'classes', 'invalid_password'), "error");

    try {
      await deleteClass(classToDelete._id);
      setClasses((prev) => prev.filter((c) => c._id !== classToDelete._id));
      showNotification(tDashboard('school-admin', 'classes', 'class_deleted'), "success");
    } catch {
      showNotification(tDashboard('school-admin', 'classes', 'class_deletion_failed'), "error");
    } finally {
      setIsSubmitting(false);
      setClassToDelete(null);
    }
  };

  const handleBulkDelete = async (password: string) => {
    if (!user) return;
    const valid = await verifyPassword(password, user.email);
    if (!valid) return showNotification(tDashboard('school-admin', 'classes', 'invalid_password'), "error");

    try {
      if (bulkDeleteType === "all") {
        await deleteAllClasses();
        setClasses([]);
      } else {
        await deleteMultipleClasses(selectedClassIds);
        setClasses((prev) => prev.filter(c => !selectedClassIds.includes(c._id)));
      }
      showNotification(tDashboard('school-admin', 'classes', 'classes_deleted'), "success");
      setTableKey(prev => prev + 1);
    } catch {
      showNotification(tDashboard('school-admin', 'classes', 'bulk_deletion_failed'), "error");
    } finally {
      setIsBulkDeleteModalOpen(false);
    }
  };
const handleSelectionChange = React.useCallback((selected: ClassSchema[]) => {
  setSelectedClassIds(selected.map(s => s._id));
}, []);

  const showNotification = (message: string, type: NotificationType) => {
    setNotificationMessage(message);
    setNotificationType(type);
    setIsNotificationCard(true);
  };

  if (!schoolId) {
    return (
      <div className="text-red-600 mt-6 text-center">
        {tDashboard('school-admin', 'classes', 'no_school_id')}
      </div>
    );
  }

  return (
    <div>
      {isNotificationCard && (
        <NotificationCard
          title={tDashboard('school-admin', 'classes', 'notification')}
          message={notificationMessage}
          onClose={() => setIsNotificationCard(false)}
          type={notificationType}
          isVisible={isNotificationCard}
          isFixed={true}
        />
      )}

      <div className="flex flex-col md:flex-row gap-6 py-4">
        {/* Class Section */}
        <div className="w-full md:w-1/2">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              onClick={() => setIsClassModalOpen(true)}
              className="px-4 py-2 w-full bg-teal text-white rounded-md hover:bg-teal-600"
            >
              {tDashboard('school-admin', 'classes', 'add_new_class')}
            </motion.button>
            <select
              className="px-3 py-2 border rounded-md text-sm bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-teal w-full"
              value={selectedClassLevelId}
              onChange={(e) => setSelectedClassLevelId(e.target.value)}

            >
              <option value="all">{tDashboard('school-admin', 'classes', 'all_levels')}</option>
              {classLevels.map(level => (
                <option key={level._id} value={level._id}>{level.name}</option>
              ))}
            </select>
          </div>

          <DataTableFix
            key={tableKey}
            columns={columns}
            actions={actions}
            data={filteredClasses}
            onSelectionChange={handleSelectionChange}
            enableBulkActions={true}
            handleDeleteMultiple={() => {
              setBulkDeleteType("selected");
              setIsBulkDeleteModalOpen(true);
            }}
            handleDeleteAll={() => {
              setBulkDeleteType("all");
              setIsBulkDeleteModalOpen(true);
            }}
            idAccessor="_id"
            defaultItemsPerPage={5}
            loading={loading}
            onLoadingChange={setLoading}
          />
        </div>

        {/* Level Section */}
        <div className="w-full md:w-1/2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            onClick={() => setIsLevelModalOpen(true)}
            className="mb-4 bg-teal text-white px-4 py-2 rounded-md"
          >
            {tDashboard('school-admin', 'classes', 'add_new_level')}
          </motion.button>

          <ClassLevelsTableWithBulkActions
            classLevels={classLevels}
            setClassLevels={setClassLevels}
            loading={loading}
            onSuccess={(msg) => showNotification(msg, "success")}
            onError={(msg) => showNotification(msg, "error")}
            actions={[{ label: tDashboard('school-admin', 'classes', 'delete'), onClick: (lvl: React.SetStateAction<ClassLevelSchema | null>) => setLevelToDelete(lvl) }]}
            columns={[{ header: tDashboard('school-admin', 'classes', 'class_level_name'), accessor: (row: { name: any; }) => row.name }]}
          />
        </div>
      </div>

      {/* Modals */}
      {isClassModalOpen && schoolId && (
        <CreateClassModal
          onClose={() => setIsClassModalOpen(false)}
          onSave={handleSaveClass}
          schoolId={schoolId}
          classLevels={classLevels}
          initialData={editingClass}
          submitStatus={submitStatus}
          isSubmitting={isSubmitting}
        />
      )}

      {classToDelete && (
        <DeleteClassModal
          className={classToDelete.name}
          onClose={() => setClassToDelete(null)}
          onDelete={handleDeleteClass}
          isSubmitting={isSubmitting}
          submitStatus={submitStatus}
        />
      )}

      {isLevelModalOpen && schoolId && (
        <CreateLevelModal
          onClose={() => setIsLevelModalOpen(false)}
          onSave={async (levelData) => {
            const created = await createClassLevel({ ...levelData, school_id: schoolId });
            setClassLevels((prev) => [...prev, created]);
            setIsLevelModalOpen(false);
            showNotification(tDashboard('school-admin', 'classes', 'level_created_success'), "success");
          }}
          schoolId={schoolId}
          isSubmitting={isSubmitting}
          submitStatus={submitStatus}
        />
      )}

      {levelToDelete && (
        <DeleteClassLevelModal
          levelName={levelToDelete.name}
          onClose={() => setLevelToDelete(null)}
          onDelete={async (password) => {
            const valid = await verifyPassword(password, user.email);
            if (!valid) return showNotification(tDashboard('school-admin', 'classes', 'invalid_password'), "error");

            await deleteClassLevel(levelToDelete._id);
            setClassLevels(prev => prev.filter(lvl => lvl._id !== levelToDelete._id));
            showNotification(tDashboard('school-admin', 'classes', 'level_deleted'), "success");
            setLevelToDelete(null);
          }}
          isSubmitting={isSubmitting}
          submitStatus={submitStatus}
        />
      )}

      {isBulkDeleteModalOpen && (
        <BulkDeleteModal
          isOpen={isBulkDeleteModalOpen}
          onClose={() => setIsBulkDeleteModalOpen(false)}
          onConfirm={handleBulkDelete}
          title={bulkDeleteType === "all" ? tDashboard('school-admin', 'classes', 'delete_all_classes') : tDashboard('school-admin', 'classes', 'delete_selected_classes')}
          message={t('dashboard.school-admin.classes.delete_confirmation', {
            count: bulkDeleteType === "all" ? filteredClasses.length : selectedClassIds.length
          })}
          itemCount={
            bulkDeleteType === "all" ? filteredClasses.length : selectedClassIds.length
          }
          itemType={tDashboard('school-admin', 'classes', 'classes')}
          isDeleteAll={bulkDeleteType === "all"}
          isSubmitting={isSubmitting}
          submitStatus={submitStatus}
          requirePassword={true}
        />
      )}
    </div>
  );
};

export default function PageWrapper({ user }: { user: UserSchema }) {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-screen"><CircularLoader size={32} color="teal" /></div>}>
      <ManageClassesComponent user={user} />
    </Suspense>
  );
}
