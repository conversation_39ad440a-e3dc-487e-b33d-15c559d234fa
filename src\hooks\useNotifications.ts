import { useState, useCallback } from 'react';

export interface NotificationItem {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  messageKey: string;
  params?: Record<string, string | number>;
  duration?: number;
  autoClose?: boolean;
}

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  const addNotification = useCallback((notification: Omit<NotificationItem, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification: NotificationItem = {
      id,
      autoClose: true,
      duration: 5000,
      ...notification,
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto remove if autoClose is enabled
    if (newNotification.autoClose) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Helper functions for different types of notifications
  const showSuccess = useCallback((messageKey: string, params?: Record<string, string | number>) => {
    return addNotification({ type: 'success', messageKey, params });
  }, [addNotification]);

  const showError = useCallback((messageKey: string, params?: Record<string, string | number>) => {
    return addNotification({ type: 'error', messageKey, params });
  }, [addNotification]);

  const showWarning = useCallback((messageKey: string, params?: Record<string, string | number>) => {
    return addNotification({ type: 'warning', messageKey, params });
  }, [addNotification]);

  const showInfo = useCallback((messageKey: string, params?: Record<string, string | number>) => {
    return addNotification({ type: 'info', messageKey, params });
  }, [addNotification]);

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
};
