// utils/feeUtils.ts or wherever you define it
import { FeeSchema } from "@/app/models/FeesModel";

export const getSelectedFeesDetails = (
  selectedFeeIds: string[],
  feeList: FeeSchema[]
): FeeSchema[] => {
  const SCHOLARIFY_FEE_ID = "507f1f77bcf86cd799439011";
  const SCHOLARIFY_FEE = 3000;

  const scholarifyFee: FeeSchema = {
    _id: SCHOLARIFY_FEE_ID,
    fee_type: "Scholarify Fee",
    amount: SCHOLARIFY_FEE,
    school_id: "",
  };

  const selected = feeList.filter(fee => selectedFeeIds.includes(fee._id));

  if (selectedFeeIds.includes(SCHOLARIFY_FEE_ID)) {
    selected.push(scholarifyFee);
  }

  return selected;
};
