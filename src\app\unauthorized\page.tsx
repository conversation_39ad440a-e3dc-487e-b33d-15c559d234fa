// Unauthorized.tsx
"use client" // Add this directive at the very top of the file
import React from 'react';
import { useRouter } from 'next/navigation'; // Import useRouter from next/navigation

const Unauthorized: React.FC = () => {
  const router = useRouter(); // Initialize useRouter from next/navigation

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4 sm:p-6 md:p-8 font-sans">
      <div className="bg-background shadow-xl rounded-2xl p-6 sm:p-8 md:p-10 max-w-2xl w-full text-center border border-gray-200">
        {/* Lock Icon */}
        <div className="mb-6 flex justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-20 w-20 text-teal" // Changed to text-teal
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="1.5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 15v2m-2 4h4m6-10V7a4 4 0 00-4-4H8a4 0 00-4 4v3m6 10h2a2 2 0 002-2v-2a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2zm9-11V7a5 5 0 00-5-5H8a5 5 0 00-5 5v3"
            />
          </svg>
        </div>

        {/* Heading */}
        <h1 className="text-3xl sm:text-4xl font-extrabold text-foreground mb-4 leading-tight"> {/* Changed to text-foreground */}
          Uh Oh! You've Hit a Restricted Area.
        </h1>

        {/* Main Message */}
        <p className="text-base sm:text-lg text-foreground mb-6 leading-relaxed"> {/* Changed to text-foreground */}
          It seems you don't have the necessary permissions to access the page you requested. This could be because:
        </p>

        {/* Reasons List */}
        <ul className="list-disc list-inside text-left text-foreground mb-8 space-y-2 max-w-md mx-auto"> {/* Changed to text-foreground */}
          <li>You're not logged in.</li>
          <li>Your account doesn't have the required access level.</li>
          <li>The page you're trying to reach is private.</li>
        </ul>

        {/* Action Heading */}
        <h2 className="text-xl sm:text-2xl font-bold text-foreground mb-6"> {/* Changed to text-foreground */}
          What Can You Do?
        </h2>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mb-8">
          <button
            onClick={() => router.push('/login')} // Navigate to /login page using router.push
            className="w-full sm:w-auto px-8 py-3 bg-teal text-white font-semibold rounded-lg shadow-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition duration-200 ease-in-out transform hover:scale-105" // Changed to bg-teal, hover:bg-teal-700, focus:ring-teal-500
          >
            Log In to Your Account
          </button>
          <button
            onClick={() => router.push('/')} // Navigate to homepage using router.push
            className="w-full sm:w-auto px-8 py-3 bg-background text-foreground font-semibold rounded-lg shadow-md border border-teal hover:bg-teal-50 focus:outline-none focus:ring-2 focus:ring-teal-400 focus:ring-offset-2 transition duration-200 ease-in-out transform hover:scale-105" // Changed to bg-background, text-foreground, border-teal, hover:bg-teal-50, focus:ring-teal-400
          >
            Return to Our Homepage
          </button>
        </div>

        {/* Support Message */}
        <p className="text-sm sm:text-base text-foreground mb-4"> {/* Changed to text-foreground */}
          If you believe this is an error, please contact support.
        </p>

        {/* Contact Support Button */}
        <button
          onClick={() => window.location.href = 'mailto:<EMAIL>'} // Open email client
          className="px-6 py-2 text-teal font-semibold rounded-lg border border-teal hover:bg-teal-50 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition duration-200 ease-in-out" // Changed to text-teal, border-teal, hover:bg-teal-50, focus:ring-teal-500
        >
          Contact Support
        </button>
      </div>
    </div>
  );
};

export default Unauthorized;