const Settings = require('../models/Settings');

// Get the current settings document (there should only be one)
async function getSettings(req, res) {
  try {
    let settings = await Settings.findOne();
    if (!settings) {
      // If no settings found, create defaults
      settings = new Settings({
        general: {
          platform_name: 'Scholarify',
          support_email: '<EMAIL>',
          default_language: 'en',
          maintenance_mode: false,
          maintenance_message: '',
        },
        credit: {
          resell_price_per_credit: 0,
          buy_price_per_credit: 0,
        }
      });
      await settings.save();
    }
    res.json(settings);
  } catch (err) {
    console.error('Error fetching settings:', err);
    res.status(500).json({ message: 'Server error' });
  }
}

// Update general settings
async function updateGeneralSettings(req, res) {
  const { platform_name, support_email, default_language, maintenance_mode, maintenance_message } = req.body;

  try {
    const settings = await Settings.findOne();
    if (!settings) {
      return res.status(404).json({ message: 'Settings not found' });
    }

    if (platform_name !== undefined) settings.general.platform_name = platform_name;
    if (support_email !== undefined) settings.general.support_email = support_email;
    if (default_language !== undefined) settings.general.default_language = default_language;
    if (maintenance_mode !== undefined) settings.general.maintenance_mode = maintenance_mode;
    if (maintenance_message !== undefined) settings.general.maintenance_message = maintenance_message;

    await settings.save();
    res.json(settings);
  } catch (err) {
    console.error('Error updating general settings:', err);
    res.status(500).json({ message: 'Server error' });
  }
}

// Update credit settings
async function updateCreditSettings(req, res) {
  const { resell_price_per_credit, buy_price_per_credit } = req.body;

  try {
    const settings = await Settings.findOne();
    if (!settings) {
      return res.status(404).json({ message: 'Settings not found' });
    }

    if (resell_price_per_credit !== undefined) settings.credit.resell_price_per_credit = resell_price_per_credit;
    if (buy_price_per_credit !== undefined) settings.credit.buy_price_per_credit = buy_price_per_credit;

    await settings.save();
    res.json(settings);
  } catch (err) {
    console.error('Error updating credit settings:', err);
    res.status(500).json({ message: 'Server error' });
  }
}

module.exports = {
  getSettings,
  updateGeneralSettings,
  updateCreditSettings,
};
