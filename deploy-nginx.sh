#!/bin/bash

# Script de déploiement Nginx pour Scholarify
set -e

echo "🚀 Déploiement de la configuration Nginx pour Scholarify..."

# Variables
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
SITE_NAME="scholarifyltd.com"
PROJECT_DIR="/var/www/scholarifyltd.com"

# 1. Créer le répertoire du projet si nécessaire
echo "📁 Création du répertoire du projet..."
sudo mkdir -p $PROJECT_DIR
sudo chown -R $USER:$USER $PROJECT_DIR

# 2. Copier la configuration Nginx
echo "📋 Copie de la configuration Nginx..."
sudo cp nginx-static.conf $NGINX_SITES_AVAILABLE/$SITE_NAME

# 3. Créer le lien symbolique si nécessaire
if [ ! -L "$NGINX_SITES_ENABLED/$SITE_NAME" ]; then
    echo "🔗 Création du lien symbolique..."
    sudo ln -s $NGINX_SITES_AVAILABLE/$SITE_NAME $NGINX_SITES_ENABLED/$SITE_NAME
fi

# 4. Supprimer la configuration par défaut si elle existe
if [ -L "$NGINX_SITES_ENABLED/default" ]; then
    echo "🗑️ Suppression de la configuration par défaut..."
    sudo rm $NGINX_SITES_ENABLED/default
fi

# 5. Tester la configuration Nginx
echo "🧪 Test de la configuration Nginx..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Configuration Nginx valide"
    
    # 6. Recharger Nginx
    echo "🔄 Rechargement de Nginx..."
    sudo systemctl reload nginx
    
    echo "✅ Nginx rechargé avec succès"
    
    # 7. Vérifier le statut
    echo "📊 Statut de Nginx:"
    sudo systemctl status nginx --no-pager -l
    
else
    echo "❌ Erreur dans la configuration Nginx"
    exit 1
fi

echo "🎉 Configuration Nginx déployée avec succès!"
echo ""
echo "📝 Informations importantes:"
echo "  - Site: https://scholarifyltd.com"
echo "  - Configuration: $NGINX_SITES_AVAILABLE/$SITE_NAME"
echo "  - Logs d'accès: /var/log/nginx/scholarify_access.log"
echo "  - Logs d'erreur: /var/log/nginx/scholarify_error.log"
echo ""
echo "🔧 Commandes utiles:"
echo "  - Voir les logs: sudo tail -f /var/log/nginx/scholarify_*.log"
echo "  - Recharger Nginx: sudo systemctl reload nginx"
echo "  - Tester la config: sudo nginx -t"
