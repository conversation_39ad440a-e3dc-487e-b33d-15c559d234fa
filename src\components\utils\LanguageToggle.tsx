'use client';

import { useEffect, useState } from 'react';
import Script from 'next/script';

declare global {
  interface Window {
    googleTranslateElementInit: () => void;
    google: any;
  }
}

export default function LanguageToggle() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Init function required by Google
    window.googleTranslateElementInit = () => {
      new window.google.translate.TranslateElement(
        {
          pageLanguage: 'en',
          includedLanguages: 'en,fr',
          layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
          autoDisplay: false,
        },
        'google_translate_element'
      );

      // Wait for iframe to load
      const interval = setInterval(() => {
        const iframe = document.querySelector('.goog-te-menu-frame') as HTMLIFrameElement;
        if (iframe && iframe.contentDocument?.body?.innerHTML.includes('Français')) {
          clearInterval(interval);
          setIsLoaded(true);
        }
      }, 500);
    };
  }, []);

  const triggerTranslate = (lang: 'en' | 'fr') => {
    const iframe = document.querySelector('.goog-te-menu-frame') as HTMLIFrameElement;
    if (!iframe) return;

    const innerDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!innerDoc) return;

    const langText = lang === 'fr' ? 'Français' : 'English';
    const langLink = Array.from(innerDoc.querySelectorAll('a')).find(a =>
      a.textContent?.includes(langText)
    );

    if (langLink) {
      (langLink as HTMLElement).click();
    } else {
      alert('Language option not loaded yet. Please wait.');
    }
  };

  return (
    <>
      <div className="flex items-center gap-4">
        <button
          onClick={() => triggerTranslate('en')}
          disabled={!isLoaded}
          className="px-4 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
        >
          🇬🇧 EN
        </button>
        <button
          onClick={() => triggerTranslate('fr')}
          disabled={!isLoaded}
          className="px-4 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
        >
          🇫🇷 FR
        </button>
      </div>

      {/* Hidden widget container */}
      <div id="google_translate_element" className="hidden" />

      {/* Load Google Translate script */}
      <Script
        strategy="afterInteractive"
        src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
      />
    </>
  );
}
