"use client";

import React from "react";
import { useTranslation } from '@/hooks/useTranslation';

interface ConsentDeclarationProps {
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  name?: string;
  id?: string;
}

const ConsentDeclaration: React.FC<ConsentDeclarationProps> = ({
  checked,
  onChange,
  name = "guardian_agreed_to_terms",
  id = "guardian_agreed_to_terms",
}) => {
  const { t } = useTranslation();
  return (
    <div className="mt-6 space-y-4 p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-800 text-sm text-gray-800 dark:text-gray-200">
      <div className="max-h-64 overflow-y-auto px-2 py-3 border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900">
        <h2 className="font-semibold mb-2 text-base">{t('registration.consent.title')}</h2>
        <p>
          {t('registration.consent.guardian_confirmation')}
        </p>
        <p className="mt-2">{t('registration.consent.understand_accept')}:</p>
        <ul className="list-disc list-inside ml-4 space-y-1">
          <li>
            {t('registration.consent.data_processing')}
          </li>
          <li>
            {t('registration.consent.financial_responsibility')}
          </li>
          <li>
            {t('registration.consent.installment_agreement')}
          </li>
          <li>
            {t('registration.consent.policy_amendments')}
          </li>
          <li>
            {t('registration.consent.emergency_treatment')}
          </li>
          <li>
            They have read and agree to follow the school’s academic, behavioral, and safety policies.
          </li>
        </ul>
        <p className="mt-2">
          {t('registration.consent.admin_confirmation')}
        </p>
      </div>

      <label className="flex items-start gap-3 cursor-pointer">
        <input
          type="checkbox"
          id={id}
          name={name}
          checked={checked}
          onChange={onChange}
          className="mt-1 accent-teal-600"
          required
        />
        <span className="text-sm">
          {t('registration.consent.verbal_consent_confirmation')}
        </span>
      </label>
    </div>
  );
};

export default ConsentDeclaration;
