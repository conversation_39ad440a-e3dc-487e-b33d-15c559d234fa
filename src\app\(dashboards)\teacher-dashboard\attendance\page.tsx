"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { ClipboardList, Users, Calendar, TrendingUp, BarChart3, Eye, ArrowRight } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import TeacherAttendanceSkeleton from "@/components/skeletons/TeacherAttendanceSkeleton";
import { motion } from "framer-motion";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface ClassInfo {
  _id: string;
  name: string;
  level: string;
  section: string;
  student_count: number;
}

interface AttendanceStats {
  total_students: number;
  present_today: number;
  absent_today: number;
  late_today: number;
  excused_today: number;
  attendance_rate: number;
}

interface ClassAttendanceData {
  class_info: ClassInfo;
  stats: AttendanceStats;
  recent_records: any[];
}

const navigation = {
  icon: ClipboardList,
  baseHref: "/teacher-dashboard/attendance",
  title: "Attendance Overview"
};

export default function TeacherAttendancePage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [classesData, setClassesData] = useState<ClassAttendanceData[]>([]);
  const [overallStats, setOverallStats] = useState<AttendanceStats>({
    total_students: 0,
    present_today: 0,
    absent_today: 0,
    late_today: 0,
    excused_today: 0,
    attendance_rate: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        fetchAttendanceData(school.school_id);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router]);

  const fetchAttendanceData = async (schoolId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Import services
      const { getTeacherPermissions } = await import("@/app/services/TeacherPermissionServices");
      const { getStudentsBySchool } = await import("@/app/services/StudentServices");
      const { getAttendanceRecords } = await import("@/app/services/AttendanceServices");

      // Fetch teacher's permissions and assigned classes
      const teacherData = await getTeacherPermissions(schoolId);
      console.log("Teacher data:", teacherData);

      if (!teacherData.assigned_classes || teacherData.assigned_classes.length === 0) {
        console.error("No classes assigned to this teacher");
        throw new Error("No classes assigned to this teacher");
      }

      // Fetch all students and attendance records
      const [studentsData, attendanceResponse] = await Promise.all([
        getStudentsBySchool(schoolId),
        getAttendanceRecords(schoolId)
      ]);

      console.log("Students data:", studentsData);
      console.log("Attendance response:", attendanceResponse);

      // Extract attendance records from response
      const attendanceData = attendanceResponse.attendance_records || [];
      console.log("Attendance data:", attendanceData);


      // Get teacher's class IDs from assigned classes
      const teacherClassIds = teacherData.assigned_classes.map((cls: any) => cls._id);

      // Process data for each class
      const classesAttendanceData: ClassAttendanceData[] = [];
      let totalStudents = 0;
      let totalPresent = 0;
      let totalAbsent = 0;
      let totalLate = 0;
      let totalExcused = 0;

      const today = new Date().toISOString().split('T')[0];

      for (const classInfo of teacherData.assigned_classes) {

        // Get students for this class
        const classStudents = studentsData.filter((student: any) => 
          student.class_id === classInfo._id
        );

        // Get today's attendance for this class
        const todayAttendance = attendanceData.filter((record: any) => 
          record.class_name === classInfo.name && 
          record.date.startsWith(today)
        );

        // Calculate stats for this class
        const presentCount = todayAttendance.filter((r: any) => r.status === 'Present').length;
        const absentCount = todayAttendance.filter((r: any) => r.status === 'Absent').length;
        const lateCount = todayAttendance.filter((r: any) => r.status === 'Late').length;
        const excusedCount = todayAttendance.filter((r: any) => r.status === 'Excused').length;

        const classStats: AttendanceStats = {
          total_students: classStudents.length,
          present_today: presentCount,
          absent_today: absentCount,
          late_today: lateCount,
          excused_today: excusedCount,
          attendance_rate: classStudents.length > 0 ? (presentCount / classStudents.length) * 100 : 0
        };

        // Get recent attendance records for this class (last 5)
        const recentRecords = attendanceData
          .filter((record: any) => record.class_name === classInfo.name)
          .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
          .slice(0, 5);

        classesAttendanceData.push({
          class_info: {
            ...classInfo,
            student_count: classStudents.length,
            section: ""
          },
          stats: classStats,
          recent_records: recentRecords
        });

        // Add to overall stats
        totalStudents += classStudents.length;
        totalPresent += presentCount;
        totalAbsent += absentCount;
        totalLate += lateCount;
        totalExcused += excusedCount;
      }

      // Calculate overall stats
      const overallAttendanceRate = totalStudents > 0 ? (totalPresent / totalStudents) * 100 : 0;

      setOverallStats({
        total_students: totalStudents,
        present_today: totalPresent,
        absent_today: totalAbsent,
        late_today: totalLate,
        excused_today: totalExcused,
        attendance_rate: overallAttendanceRate
      });

      setClassesData(classesAttendanceData);

    } catch (error) {
      console.error("Error fetching attendance data:", error);
      setError(error instanceof Error ? error.message : "Failed to load attendance data");
    } finally {
      setLoading(false);
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };  
  
  const handleViewClassAttendance = (classId: string) => {
    router.push(`/teacher-dashboard/attendance/subjects?classId=${classId}`);
  };

  if (loading) {
    return (
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <TeacherAttendanceSkeleton />
        </TeacherLayout>
    );
  }

  if (error) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <div className="text-center py-12">
            <ClipboardList className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">Error Loading Attendance</h3>
            <p className="text-foreground/60 mb-6">{error}</p>
            <button
              onClick={() => fetchAttendanceData(selectedSchool?.school_id as string)}
              className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
            >
              Try Again
            </button>
          </div>
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  return (
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Attendance Overview</h1>
              <p className="text-foreground/60">
                Monitor attendance across all your classes
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-foreground/60" />
              <span className="text-sm text-foreground/60">
                {new Date().toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </span>
            </div>
          </div>

          {/* Overall Stats */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-widget rounded-lg border border-stroke p-4"
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-foreground">
                    {overallStats.total_students}
                  </div>
                  <div className="text-sm text-foreground/60">Total Students</div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-widget rounded-lg border border-stroke p-4"
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {overallStats.present_today}
                  </div>
                  <div className="text-sm text-foreground/60">Present Today</div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-widget rounded-lg border border-stroke p-4"
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                    {overallStats.absent_today}
                  </div>
                  <div className="text-sm text-foreground/60">Absent Today</div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-widget rounded-lg border border-stroke p-4"
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                  <Calendar className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                    {overallStats.late_today}
                  </div>
                  <div className="text-sm text-foreground/60">Late Today</div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-widget rounded-lg border border-stroke p-4"
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
                    {overallStats.attendance_rate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-foreground/60">Attendance Rate</div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Classes List */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-foreground">Your Classes</h2>
            
            {classesData.length === 0 ? (
              <div className="text-center py-12 bg-widget rounded-lg border border-stroke">
                <ClipboardList className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No Classes Found</h3>
                <p className="text-foreground/60">
                  You don't have any classes assigned yet.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {classesData.map((classData, index) => (
                  <motion.div
                    key={classData.class_info._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 * index }}
                    className="bg-widget rounded-lg border border-stroke p-6 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-foreground">
                          {classData.class_info.name}
                        </h3>
                        <p className="text-sm text-foreground/60">
                          {classData.class_info.level} • {classData.class_info.student_count} students
                        </p>
                      </div>
                      
                      <button
                        onClick={() => handleViewClassAttendance(classData.class_info._id)}
                        className="flex items-center space-x-2 px-3 py-2 text-teal hover:text-teal-600 hover:bg-teal/10 rounded-md transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                        <span className="text-sm font-medium">View</span>
                        <ArrowRight className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Class Stats */}
                    <div className="grid grid-cols-4 gap-3 mb-4">
                      <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">
                          {classData.stats.present_today}
                        </div>
                        <div className="text-xs text-foreground/60">Present</div>
                      </div>
                      
                      <div className="text-center p-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <div className="text-lg font-bold text-red-600 dark:text-red-400">
                          {classData.stats.absent_today}
                        </div>
                        <div className="text-xs text-foreground/60">Absent</div>
                      </div>
                      
                      <div className="text-center p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                          {classData.stats.late_today}
                        </div>
                        <div className="text-xs text-foreground/60">Late</div>
                      </div>
                      
                      <div className="text-center p-2 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
                        <div className="text-lg font-bold text-teal-600 dark:text-teal-400">
                          {classData.stats.attendance_rate.toFixed(0)}%
                        </div>
                        <div className="text-xs text-foreground/60">Rate</div>
                      </div>
                    </div>

                    {/* Recent Activity */}
                    {classData.recent_records.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-foreground/80 mb-2">Recent Activity</h4>
                        <div className="space-y-1">
                          {classData.recent_records.slice(0, 3).map((record: any, recordIndex: number) => (
                            <div key={recordIndex} className="flex items-center justify-between text-xs">
                              <span className="text-foreground/60">
                                {record.student_name}
                              </span>
                              <div className="flex items-center space-x-2">
                                <span
                                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    record.status === 'Present'
                                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                      : record.status === 'Absent'
                                      ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                                      : record.status === 'Late'
                                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                      : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                                  }`}
                                >
                                  {record.status}
                                </span>
                                <span className="text-foreground/40">
                                  {new Date(record.date).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </div>
      </TeacherLayout>
  );
}
