// "use client";

// import {
//     Settings,
//     User,
//     Shield,
//     Database,
//     Bell,
//     Palette,
//     Globe,
//     Mail,
//     Key,
//     Server,
//     CreditCard,
// } from "lucide-react";
// import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
// import CircularLoader from "@/components/widgets/CircularLoader";
// import React, { Suspense, useEffect, useState } from "react";
// import useAuth from "@/app/hooks/useAuth";
// import { getCurrentUser, updateUser } from "@/app/services/UserServices";
// import { UserSchema, UserUpdateSchema } from "@/app/models/UserModel";
// import NotificationCard from "@/components/NotificationCard";
// import {
//     createSuccessNotification,
//     createErrorNotification,
//     NotificationState,
// } from "@/app/types/notification";
// import {
//     SystemSettings,
//     SecuritySettings,
//     CreditSettings,
//     getSystemSettings,
//     updateSystemSettings,
//     getSecuritySettings,
//     updateSecuritySettings,
//     getNotificationSettings,
//     updateNotificationSettings,
//     getCreditSettings,
//     updateCreditSettings,
// } from "@/app/services/SystemSettingsService";

// const BASE_URL = "/super-admin";

// const navigation = {
//     icon: Settings,
//     baseHref: `${BASE_URL}/settings`,
//     title: "Settings",
// };

// function SettingsContent() {
//     const [activeTab, setActiveTab] = useState("profile");
//     const [user, setUser] = useState<UserSchema | null>(null);
//     const [loading, setLoading] = useState(true);
//     const [isSubmitting, setIsSubmitting] = useState(false);
//     const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(
//         null
//     );
//     const { user: authUser } = useAuth();

//     // Profile form data
//     const [profileData, setProfileData] = useState<UserUpdateSchema>({
//         user_id: "",
//         name: "",
//         email: "",
//         phone: "",
//         address: "",
//         avatar: "",
//     });

//     // System settings form data
//     const [systemSettings, setSystemSettings] = useState<SystemSettings>({
//         platformName: "Scholarify",
//         platformDescription: "Comprehensive School Management System",
//         supportEmail: "<EMAIL>",
//         maintenanceMode: false,
//         maintenanceMessage:
//             "We are currently performing scheduled maintenance. Please check back soon.",
//         allowNewRegistrations: true,
//         maxSchoolsPerSubscription: 5,
//         defaultSubscriptionDuration: 12,
//         emailNotifications: true,
//         smsNotifications: true,
//         systemBackupFrequency: "daily",
//         dataRetentionPeriod: 365,
//     });

//     // Security settings form data
//     const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
//         passwordMinLength: 8,
//         requireSpecialCharacters: true,
//         sessionTimeout: 30,
//         maxLoginAttempts: 5,
//         twoFactorRequired: false,
//         ipWhitelist: [],
//     });

//     // Credit settings form data
//     const [creditSettings, setCreditSettings] = useState<CreditSettings>({
//         pricePerCredit: 10.0,
//         paymentGateway: "stripe",
//         latePaymentFee: 5.0,
//         paymentDuePeriod: 30,
//     });

//     // Load all data on component mount
//     useEffect(() => {
//         const fetchAllData = async () => {
//             try {
//                 setLoading(true);

//                 // Fetch user data
//                 if (authUser) {
//                     const userData = await getCurrentUser();
//                     setUser(userData);

//                     // Initialize profile form with user data
//                     if (userData) {
//                         setProfileData({
//                             user_id: userData.user_id,
//                             name: userData.name || "",
//                             email: userData.email || "",
//                             phone: userData.phone || "",
//                             address: userData.address || "",
//                             avatar: userData.avatar || "",
//                         });
//                     }
//                 }

//                 // Fetch system settings
//                 try {
//                     const systemData = await getSystemSettings();
//                     setSystemSettings(systemData);
//                 } catch (error) {
//                     console.warn("Using default system settings:", error);
//                 }

//                 // Fetch security settings
//                 try {
//                     const securityData = await getSecuritySettings();
//                     setSecuritySettings(securityData);
//                 } catch (error) {
//                     console.warn("Using default security settings:", error);
//                 }

//                 // Fetch credit settings
//                 try {
//                     const creditData = await getCreditSettings();
//                     setCreditSettings(creditData);
//                 } catch (error) {
//                     console.warn("Using default credit settings:", error);
//                 }
//             } catch (error) {
//                 console.error("Error fetching data:", error);
//                 setSubmitStatus(
//                     createErrorNotification("Failed to fetch settings data")
//                 );
//             } finally {
//                 setLoading(false);
//             }
//         };

//         fetchAllData();
//     }, [authUser]);

//     // Handle profile form changes
//     const handleProfileChange = (
//         e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
//     ) => {
//         const { name, value } = e.target;
//         setProfileData((prev) => ({
//             ...prev,
//             [name]: value,
//         }));
//     };

//     // Handle system settings changes
//     const handleSystemChange = (
//         e: React.ChangeEvent<
//             HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
//         >
//     ) => {
//         const { name, value, type } = e.target;
//         setSystemSettings((prev) => ({
//             ...prev,
//             [name]:
//                 type === "checkbox"
//                     ? (e.target as HTMLInputElement).checked
//                     : type === "number"
//                     ? parseInt(value)
//                     : value,
//         }));
//     };

//     // Handle security settings changes
//     const handleSecurityChange = (
//         e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
//     ) => {
//         const { name, value, type } = e.target;
//         setSecuritySettings((prev) => ({
//             ...prev,
//             [name]:
//                 type === "checkbox"
//                     ? (e.target as HTMLInputElement).checked
//                     : type === "number"
//                     ? parseInt(value)
//                     : value,
//         }));
//     };

//     // Handle credit settings changes
//     const handleCreditChange = (
//         e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
//     ) => {
//         const { name, value, type } = e.target;
//         setCreditSettings((prev) => ({
//             ...prev,
//             [name]: type === "number" ? parseFloat(value) || 0 : value,
//         }));
//     };

//     // Handle profile form submission
//     const handleProfileSubmit = async (e: React.FormEvent) => {
//         e.preventDefault();
//         setIsSubmitting(true);

//         try {
//             await updateUser(profileData.user_id, profileData);
//             setSubmitStatus(
//                 createSuccessNotification("Profile updated successfully")
//             );
//         } catch (error) {
//             console.error("Error updating profile:", error);
//             setSubmitStatus(
//                 createErrorNotification("Failed to update profile")
//             );
//         } finally {
//             setIsSubmitting(false);
//         }
//     };

//     // Handle system settings submission
//     const handleSystemSubmit = async (e: React.FormEvent) => {
//         e.preventDefault();
//         setIsSubmitting(true);

//         try {
//             const updatedSettings = await updateSystemSettings(systemSettings);
//             setSystemSettings(updatedSettings);

//             // Check if this is development mode
//             const isDevelopment = process.env.NODE_ENV === "development";
//             const message = isDevelopment
//                 ? "System settings updated successfully (using local state - API implementation pending)"
//                 : "System settings updated successfully";

//             setSubmitStatus(createSuccessNotification(message));
//         } catch (error) {
//             console.error("Error updating system settings:", error);
//             setSubmitStatus(
//                 createErrorNotification(
//                     "Failed to update system settings. Please try again."
//                 )
//             );
//         } finally {
//             setIsSubmitting(false);
//         }
//     };

//     // Handle security settings submission
//     const handleSecuritySubmit = async (e: React.FormEvent) => {
//         e.preventDefault();
//         setIsSubmitting(true);

//         try {
//             const updatedSettings = await updateSecuritySettings(
//                 securitySettings
//             );
//             setSecuritySettings(updatedSettings);

//             // Check if this is development mode
//             const isDevelopment = process.env.NODE_ENV === "development";
//             const message = isDevelopment
//                 ? "Security settings updated successfully (using local state - API implementation pending)"
//                 : "Security settings updated successfully";

//             setSubmitStatus(createSuccessNotification(message));
//         } catch (error) {
//             console.error("Error updating security settings:", error);
//             setSubmitStatus(
//                 createErrorNotification(
//                     "Failed to update security settings. Please try again."
//                 )
//             );
//         } finally {
//             setIsSubmitting(false);
//         }
//     };

//     // Handle credit settings form submission
//     const handleCreditSubmit = async (e: React.FormEvent) => {
//         e.preventDefault();
//         setIsSubmitting(true);

//         try {
//             const updatedSettings = await updateCreditSettings(creditSettings);
//             setCreditSettings(updatedSettings);

//             // Check if this is development mode
//             const isDevelopment = process.env.NODE_ENV === "development";
//             const message = isDevelopment
//                 ? "Credit settings updated successfully (using local state - API implementation pending)"
//                 : "Credit settings updated successfully";

//             setSubmitStatus(createSuccessNotification(message));
//         } catch (error) {
//             console.error("Error updating credit settings:", error);
//             setSubmitStatus(
//                 createErrorNotification(
//                     "Failed to update credit settings. Please try again."
//                 )
//             );
//         } finally {
//             setIsSubmitting(false);
//         }
//     };

//     if (loading) {
//         return (
//             <div className="flex justify-center items-center h-64">
//                 <CircularLoader size={32} color="teal" />
//             </div>
//         );
//     }

//     const tabs = [
//         { id: "profile", label: "Profile", icon: User },
//         { id: "system", label: "System", icon: Server },
//         { id: "credits", label: "Credits", icon: CreditCard },
//         { id: "security", label: "Security", icon: Shield },
//         { id: "notifications", label: "Notifications", icon: Bell },
//         { id: "appearance", label: "Appearance", icon: Palette },
//     ];

//     return (
//         <div className="container mx-auto p-6 max-w-6xl">
//             <div className="mb-8">
//                 <h1 className="text-3xl font-bold text-foreground mb-2">
//                     Settings
//                 </h1>
//                 <p className="text-foreground/70">
//                     Manage your platform settings and preferences
//                 </p>
//             </div>

//             {submitStatus && (
//                 <div className="mb-6">
//                     <NotificationCard
//                         type={submitStatus.type}
//                         title={submitStatus.title}
//                         message={submitStatus.message}
//                         onClose={() => setSubmitStatus(null)}
//                         isVisible={true}
//                     />
//                 </div>
//             )}

//             {/* Development Notice */}
//             {process.env.NODE_ENV === "development" && (
//                 <div className="mb-6">
//                     <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
//                         <div className="flex items-center">
//                             <div className="flex-shrink-0">
//                                 <svg
//                                     className="h-5 w-5 text-yellow-400"
//                                     viewBox="0 0 20 20"
//                                     fill="currentColor"
//                                 >
//                                     <path
//                                         fillRule="evenodd"
//                                         d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
//                                         clipRule="evenodd"
//                                     />
//                                 </svg>
//                             </div>
//                             <div className="ml-3">
//                                 <h3 className="text-sm font-medium text-yellow-800">
//                                     Development Mode
//                                 </h3>
//                                 <p className="mt-1 text-sm text-yellow-700">
//                                     Some settings use default values as the
//                                     backend endpoints are still being
//                                     implemented. Credit and Security settings
//                                     will be fully functional once the API is
//                                     ready.
//                                 </p>
//                             </div>
//                         </div>
//                     </div>
//                 </div>
//             )}

//             <div className="flex flex-col lg:flex-row gap-6">
//                 {/* Sidebar Navigation */}
//                 <div className="lg:w-64 flex-shrink-0">
//                     <div className="bg-background border border-stroke rounded-lg p-4 shadow-sm">
//                         <nav className="space-y-2">
//                             {tabs.map((tab) => {
//                                 const Icon = tab.icon;
//                                 return (
//                                     <button
//                                         key={tab.id}
//                                         onClick={() => setActiveTab(tab.id)}
//                                         className={`w-full flex items-center gap-3 px-4 py-3 text-left rounded-lg transition-all duration-200 ${
//                                             activeTab === tab.id
//                                                 ? "bg-teal text-white shadow-md"
//                                                 : "text-foreground hover:bg-teal/10 hover:text-teal"
//                                         }`}
//                                     >
//                                         <Icon size={20} />
//                                         <span className="font-medium">
//                                             {tab.label}
//                                         </span>
//                                     </button>
//                                 );
//                             })}
//                         </nav>
//                     </div>
//                 </div>

//                 {/* Main Content */}
//                 <div className="flex-1">
//                     <div className="bg-background border border-stroke rounded-lg shadow-sm">
//                         {/* Profile Tab */}
//                         {activeTab === "profile" && (
//                             <div className="p-6">
//                                 <div className="mb-6">
//                                     <h2 className="text-2xl font-semibold text-foreground mb-2">
//                                         Profile Settings
//                                     </h2>
//                                     <p className="text-foreground/70">
//                                         Update your personal information and
//                                         preferences
//                                     </p>
//                                 </div>

//                                 <form
//                                     onSubmit={handleProfileSubmit}
//                                     className="space-y-6"
//                                 >
//                                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                                         <div>
//                                             <label
//                                                 htmlFor="name"
//                                                 className="block text-sm font-medium text-foreground mb-2"
//                                             >
//                                                 Full Name
//                                             </label>
//                                             <input
//                                                 type="text"
//                                                 id="name"
//                                                 name="name"
//                                                 value={profileData.name}
//                                                 onChange={handleProfileChange}
//                                                 className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 required
//                                             />
//                                         </div>

//                                         <div>
//                                             <label
//                                                 htmlFor="email"
//                                                 className="block text-sm font-medium text-foreground mb-2"
//                                             >
//                                                 Email Address
//                                             </label>
//                                             <input
//                                                 type="email"
//                                                 id="email"
//                                                 name="email"
//                                                 value={profileData.email}
//                                                 onChange={handleProfileChange}
//                                                 className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 required
//                                             />
//                                         </div>

//                                         <div>
//                                             <label
//                                                 htmlFor="phone"
//                                                 className="block text-sm font-medium text-foreground mb-2"
//                                             >
//                                                 Phone Number
//                                             </label>
//                                             <input
//                                                 type="tel"
//                                                 id="phone"
//                                                 name="phone"
//                                                 value={profileData.phone}
//                                                 onChange={handleProfileChange}
//                                                 className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                             />
//                                         </div>

//                                         <div>
//                                             <label
//                                                 htmlFor="avatar"
//                                                 className="block text-sm font-medium text-foreground mb-2"
//                                             >
//                                                 Avatar URL
//                                             </label>
//                                             <input
//                                                 type="url"
//                                                 id="avatar"
//                                                 name="avatar"
//                                                 value={profileData.avatar}
//                                                 onChange={handleProfileChange}
//                                                 className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                             />
//                                         </div>
//                                     </div>

//                                     <div>
//                                         <label
//                                             htmlFor="address"
//                                             className="block text-sm font-medium text-foreground mb-2"
//                                         >
//                                             Address
//                                         </label>
//                                         <textarea
//                                             id="address"
//                                             name="address"
//                                             rows={3}
//                                             value={profileData.address}
//                                             onChange={handleProfileChange}
//                                             className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                         />
//                                     </div>

//                                     <div className="flex justify-end">
//                                         <button
//                                             type="submit"
//                                             disabled={isSubmitting}
//                                             className="px-6 py-3 bg-teal text-white font-medium rounded-lg hover:bg-tealdarker focus:ring-2 focus:ring-teal focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
//                                         >
//                                             {isSubmitting
//                                                 ? "Saving..."
//                                                 : "Save Changes"}
//                                         </button>
//                                     </div>
//                                 </form>
//                             </div>
//                         )}

//                         {/* System Tab */}
//                         {activeTab === "system" && (
//                             <div className="p-6">
//                                 <div className="mb-6">
//                                     <h2 className="text-2xl font-semibold text-foreground mb-2">
//                                         System Settings
//                                     </h2>
//                                     <p className="text-foreground/70">
//                                         Configure platform-wide settings and
//                                         preferences
//                                     </p>
//                                 </div>

//                                 <form
//                                     onSubmit={handleSystemSubmit}
//                                     className="space-y-8"
//                                 >
//                                     {/* Platform Information */}
//                                     <div className="space-y-4">
//                                         <h3 className="text-lg font-semibold text-foreground border-b border-stroke pb-2">
//                                             Platform Information
//                                         </h3>
//                                         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                                             <div>
//                                                 <label
//                                                     htmlFor="platformName"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Platform Name
//                                                 </label>
//                                                 <input
//                                                     type="text"
//                                                     id="platformName"
//                                                     name="platformName"
//                                                     value={
//                                                         systemSettings.platformName
//                                                     }
//                                                     onChange={
//                                                         handleSystemChange
//                                                     }
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="supportEmail"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Support Email
//                                                 </label>
//                                                 <input
//                                                     type="email"
//                                                     id="supportEmail"
//                                                     name="supportEmail"
//                                                     value={
//                                                         systemSettings.supportEmail
//                                                     }
//                                                     onChange={
//                                                         handleSystemChange
//                                                     }
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>
//                                         </div>

//                                         <div>
//                                             <label
//                                                 htmlFor="platformDescription"
//                                                 className="block text-sm font-medium text-foreground mb-2"
//                                             >
//                                                 Platform Description
//                                             </label>
//                                             <textarea
//                                                 id="platformDescription"
//                                                 name="platformDescription"
//                                                 rows={3}
//                                                 value={
//                                                     systemSettings.platformDescription
//                                                 }
//                                                 onChange={handleSystemChange}
//                                                 className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                             />
//                                         </div>
//                                     </div>

//                                     {/* System Configuration */}
//                                     <div className="space-y-4">
//                                         <h3 className="text-lg font-semibold text-foreground border-b border-stroke pb-2">
//                                             System Configuration
//                                         </h3>
//                                         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                                             <div>
//                                                 <label
//                                                     htmlFor="maxSchoolsPerSubscription"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Max Schools per Subscription
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="maxSchoolsPerSubscription"
//                                                     name="maxSchoolsPerSubscription"
//                                                     value={
//                                                         systemSettings.maxSchoolsPerSubscription
//                                                     }
//                                                     onChange={
//                                                         handleSystemChange
//                                                     }
//                                                     min="1"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="defaultSubscriptionDuration"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Default Subscription
//                                                     Duration (months)
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="defaultSubscriptionDuration"
//                                                     name="defaultSubscriptionDuration"
//                                                     value={
//                                                         systemSettings.defaultSubscriptionDuration
//                                                     }
//                                                     onChange={
//                                                         handleSystemChange
//                                                     }
//                                                     min="1"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="systemBackupFrequency"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     System Backup Frequency
//                                                 </label>
//                                                 <select
//                                                     id="systemBackupFrequency"
//                                                     name="systemBackupFrequency"
//                                                     value={
//                                                         systemSettings.systemBackupFrequency
//                                                     }
//                                                     onChange={
//                                                         handleSystemChange
//                                                     }
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 >
//                                                     <option value="hourly">
//                                                         Hourly
//                                                     </option>
//                                                     <option value="daily">
//                                                         Daily
//                                                     </option>
//                                                     <option value="weekly">
//                                                         Weekly
//                                                     </option>
//                                                     <option value="monthly">
//                                                         Monthly
//                                                     </option>
//                                                 </select>
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="dataRetentionPeriod"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Data Retention Period (days)
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="dataRetentionPeriod"
//                                                     name="dataRetentionPeriod"
//                                                     value={
//                                                         systemSettings.dataRetentionPeriod
//                                                     }
//                                                     onChange={
//                                                         handleSystemChange
//                                                     }
//                                                     min="30"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>
//                                         </div>
//                                     </div>

//                                     {/* System Toggles */}
//                                     <div className="space-y-4">
//                                         <h3 className="text-lg font-semibold text-foreground border-b border-stroke pb-2">
//                                             System Controls
//                                         </h3>
//                                         <div className="space-y-4">
//                                             <div className="flex items-center justify-between p-4 border border-stroke rounded-lg">
//                                                 <div>
//                                                     <h4 className="font-medium text-foreground">
//                                                         Maintenance Mode
//                                                     </h4>
//                                                     <p className="text-sm text-foreground/70">
//                                                         Enable maintenance mode
//                                                         to restrict access
//                                                     </p>
//                                                 </div>
//                                                 <label className="relative inline-flex items-center cursor-pointer">
//                                                     <input
//                                                         type="checkbox"
//                                                         name="maintenanceMode"
//                                                         checked={
//                                                             systemSettings.maintenanceMode
//                                                         }
//                                                         onChange={
//                                                             handleSystemChange
//                                                         }
//                                                         className="sr-only peer"
//                                                     />
//                                                     <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal"></div>
//                                                 </label>
//                                             </div>

//                                             {/* Maintenance Message */}
//                                             {systemSettings.maintenanceMode && (
//                                                 <div className="p-4 border border-stroke rounded-lg">
//                                                     <label
//                                                         htmlFor="maintenanceMessage"
//                                                         className="block text-sm font-medium text-foreground mb-2"
//                                                     >
//                                                         Maintenance Message
//                                                     </label>
//                                                     <textarea
//                                                         id="maintenanceMessage"
//                                                         name="maintenanceMessage"
//                                                         rows={3}
//                                                         value={
//                                                             systemSettings.maintenanceMessage
//                                                         }
//                                                         onChange={
//                                                             handleSystemChange
//                                                         }
//                                                         placeholder="Enter the message to display during maintenance"
//                                                         className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                     />
//                                                 </div>
//                                             )}

//                                             <div className="flex items-center justify-between p-4 border border-stroke rounded-lg">
//                                                 <div>
//                                                     <h4 className="font-medium text-foreground">
//                                                         Allow New Registrations
//                                                     </h4>
//                                                     <p className="text-sm text-foreground/70">
//                                                         Allow new schools to
//                                                         register on the platform
//                                                     </p>
//                                                 </div>
//                                                 <label className="relative inline-flex items-center cursor-pointer">
//                                                     <input
//                                                         type="checkbox"
//                                                         name="allowNewRegistrations"
//                                                         checked={
//                                                             systemSettings.allowNewRegistrations
//                                                         }
//                                                         onChange={
//                                                             handleSystemChange
//                                                         }
//                                                         className="sr-only peer"
//                                                     />
//                                                     <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal"></div>
//                                                 </label>
//                                             </div>
//                                         </div>
//                                     </div>

//                                     <div className="flex justify-end">
//                                         <button
//                                             type="submit"
//                                             disabled={isSubmitting}
//                                             className="px-6 py-3 bg-teal text-white font-medium rounded-lg hover:bg-tealdarker focus:ring-2 focus:ring-teal focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
//                                         >
//                                             {isSubmitting
//                                                 ? "Saving..."
//                                                 : "Save System Settings"}
//                                         </button>
//                                     </div>
//                                 </form>
//                             </div>
//                         )}

//                         {/* Credits Tab */}
//                         {activeTab === "credits" && (
//                             <div className="p-6">
//                                 <div className="mb-6">
//                                     <h2 className="text-2xl font-semibold text-foreground mb-2">
//                                         Credit Settings
//                                     </h2>
//                                     <p className="text-foreground/70">
//                                         Configure credit pricing and payment
//                                         settings
//                                     </p>
//                                 </div>

//                                 <form
//                                     onSubmit={handleCreditSubmit}
//                                     className="space-y-8"
//                                 >
//                                     {/* Credit Pricing */}
//                                     <div className="space-y-4">
//                                         <h3 className="text-lg font-semibold text-foreground border-b border-stroke pb-2">
//                                             Pricing Configuration
//                                         </h3>
//                                         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                                             <div>
//                                                 <label
//                                                     htmlFor="pricePerCredit"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Price per Credit ($)
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="pricePerCredit"
//                                                     name="pricePerCredit"
//                                                     value={
//                                                         creditSettings.pricePerCredit
//                                                     }
//                                                     onChange={
//                                                         handleCreditChange
//                                                     }
//                                                     min="0"
//                                                     step="0.01"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                     required
//                                                 />
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="latePaymentFee"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Late Payment Fee ($)
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="latePaymentFee"
//                                                     name="latePaymentFee"
//                                                     value={
//                                                         creditSettings.latePaymentFee
//                                                     }
//                                                     onChange={
//                                                         handleCreditChange
//                                                     }
//                                                     min="0"
//                                                     step="0.01"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="paymentDuePeriod"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Payment Due Period (days)
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="paymentDuePeriod"
//                                                     name="paymentDuePeriod"
//                                                     value={
//                                                         creditSettings.paymentDuePeriod
//                                                     }
//                                                     onChange={
//                                                         handleCreditChange
//                                                     }
//                                                     min="1"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="paymentGateway"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Payment Gateway
//                                                 </label>
//                                                 <select
//                                                     id="paymentGateway"
//                                                     name="paymentGateway"
//                                                     value={
//                                                         creditSettings.paymentGateway
//                                                     }
//                                                     onChange={
//                                                         handleCreditChange
//                                                     }
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 >
//                                                     <option value="stripe">
//                                                         Stripe
//                                                     </option>
//                                                     <option value="paypal">
//                                                         PayPal
//                                                     </option>
//                                                     <option value="square">
//                                                         Square
//                                                     </option>
//                                                     <option value="flutterwave">
//                                                         Flutterwave
//                                                     </option>
//                                                 </select>
//                                             </div>
//                                         </div>
//                                     </div>

//                                     {/* Credit Summary */}
//                                     <div className="space-y-4">
//                                         <h3 className="text-lg font-semibold text-foreground border-b border-stroke pb-2">
//                                             Summary
//                                         </h3>
//                                         <div className="bg-background border border-stroke rounded-lg p-6">
//                                             <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//                                                 <div className="text-center">
//                                                     <div className="text-2xl font-bold text-teal">
//                                                         $
//                                                         {creditSettings.pricePerCredit.toFixed(
//                                                             2
//                                                         )}
//                                                     </div>
//                                                     <div className="text-sm text-foreground/70">
//                                                         Price per Credit
//                                                     </div>
//                                                 </div>
//                                                 <div className="text-center">
//                                                     <div className="text-2xl font-bold text-orange-500">
//                                                         $
//                                                         {creditSettings.latePaymentFee.toFixed(
//                                                             2
//                                                         )}
//                                                     </div>
//                                                     <div className="text-sm text-foreground/70">
//                                                         Late Fee
//                                                     </div>
//                                                 </div>
//                                                 <div className="text-center">
//                                                     <div className="text-2xl font-bold text-blue-500">
//                                                         {
//                                                             creditSettings.paymentDuePeriod
//                                                         }
//                                                     </div>
//                                                     <div className="text-sm text-foreground/70">
//                                                         Days to Pay
//                                                     </div>
//                                                 </div>
//                                             </div>
//                                         </div>
//                                     </div>

//                                     <div className="flex justify-end">
//                                         <button
//                                             type="submit"
//                                             disabled={isSubmitting}
//                                             className="px-6 py-3 bg-teal text-white font-medium rounded-lg hover:bg-tealdarker focus:ring-2 focus:ring-teal focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
//                                         >
//                                             {isSubmitting
//                                                 ? "Saving..."
//                                                 : "Save Credit Settings"}
//                                         </button>
//                                     </div>
//                                 </form>
//                             </div>
//                         )}

//                         {/* Security Tab */}
//                         {activeTab === "security" && (
//                             <div className="p-6">
//                                 <div className="mb-6">
//                                     <h2 className="text-2xl font-semibold text-foreground mb-2">
//                                         Security Settings
//                                     </h2>
//                                     <p className="text-foreground/70">
//                                         Configure security policies and
//                                         authentication settings
//                                     </p>
//                                 </div>

//                                 <form
//                                     onSubmit={handleSecuritySubmit}
//                                     className="space-y-8"
//                                 >
//                                     {/* Password Policy */}
//                                     <div className="space-y-4">
//                                         <h3 className="text-lg font-semibold text-foreground border-b border-stroke pb-2">
//                                             Password Policy
//                                         </h3>
//                                         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                                             <div>
//                                                 <label
//                                                     htmlFor="passwordMinLength"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Minimum Password Length
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="passwordMinLength"
//                                                     name="passwordMinLength"
//                                                     value={
//                                                         securitySettings.passwordMinLength
//                                                     }
//                                                     onChange={
//                                                         handleSecurityChange
//                                                     }
//                                                     min="6"
//                                                     max="32"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="maxLoginAttempts"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Max Login Attempts
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="maxLoginAttempts"
//                                                     name="maxLoginAttempts"
//                                                     value={
//                                                         securitySettings.maxLoginAttempts
//                                                     }
//                                                     onChange={
//                                                         handleSecurityChange
//                                                     }
//                                                     min="3"
//                                                     max="10"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>

//                                             <div>
//                                                 <label
//                                                     htmlFor="sessionTimeout"
//                                                     className="block text-sm font-medium text-foreground mb-2"
//                                                 >
//                                                     Session Timeout (minutes)
//                                                 </label>
//                                                 <input
//                                                     type="number"
//                                                     id="sessionTimeout"
//                                                     name="sessionTimeout"
//                                                     value={
//                                                         securitySettings.sessionTimeout
//                                                     }
//                                                     onChange={
//                                                         handleSecurityChange
//                                                     }
//                                                     min="5"
//                                                     max="480"
//                                                     className="w-full px-4 py-3 border border-stroke rounded-lg focus:ring-2 focus:ring-teal focus:border-teal transition-colors bg-background text-foreground"
//                                                 />
//                                             </div>
//                                         </div>

//                                         <div className="space-y-4">
//                                             <div className="flex items-center justify-between p-4 border border-stroke rounded-lg">
//                                                 <div>
//                                                     <h4 className="font-medium text-foreground">
//                                                         Require Special
//                                                         Characters
//                                                     </h4>
//                                                     <p className="text-sm text-foreground/70">
//                                                         Require passwords to
//                                                         contain special
//                                                         characters
//                                                     </p>
//                                                 </div>
//                                                 <label className="relative inline-flex items-center cursor-pointer">
//                                                     <input
//                                                         type="checkbox"
//                                                         name="requireSpecialCharacters"
//                                                         checked={
//                                                             securitySettings.requireSpecialCharacters
//                                                         }
//                                                         onChange={
//                                                             handleSecurityChange
//                                                         }
//                                                         className="sr-only peer"
//                                                     />
//                                                     <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal"></div>
//                                                 </label>
//                                             </div>

//                                             <div className="flex items-center justify-between p-4 border border-stroke rounded-lg">
//                                                 <div>
//                                                     <h4 className="font-medium text-foreground">
//                                                         Two-Factor
//                                                         Authentication Required
//                                                     </h4>
//                                                     <p className="text-sm text-foreground/70">
//                                                         Require 2FA for all
//                                                         admin accounts
//                                                     </p>
//                                                 </div>
//                                                 <label className="relative inline-flex items-center cursor-pointer">
//                                                     <input
//                                                         type="checkbox"
//                                                         name="twoFactorRequired"
//                                                         checked={
//                                                             securitySettings.twoFactorRequired
//                                                         }
//                                                         onChange={
//                                                             handleSecurityChange
//                                                         }
//                                                         className="sr-only peer"
//                                                     />
//                                                     <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal"></div>
//                                                 </label>
//                                             </div>
//                                         </div>
//                                     </div>

//                                     <div className="flex justify-end">
//                                         <button
//                                             type="submit"
//                                             disabled={isSubmitting}
//                                             className="px-6 py-3 bg-teal text-white font-medium rounded-lg hover:bg-tealdarker focus:ring-2 focus:ring-teal focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
//                                         >
//                                             {isSubmitting
//                                                 ? "Saving..."
//                                                 : "Save Security Settings"}
//                                         </button>
//                                     </div>
//                                 </form>
//                             </div>
//                         )}

//                         {/* Notifications Tab */}
//                         {activeTab === "notifications" && (
//                             <div className="p-6">
//                                 <div className="mb-6">
//                                     <h2 className="text-2xl font-semibold text-foreground mb-2">
//                                         Notification Settings
//                                     </h2>
//                                     <p className="text-foreground/70">
//                                         Configure notification preferences and
//                                         delivery methods
//                                     </p>
//                                 </div>

//                                 <form
//                                     onSubmit={handleSystemSubmit}
//                                     className="space-y-6"
//                                 >
//                                     <div className="space-y-4">
//                                         <div className="flex items-center justify-between p-4 border border-stroke rounded-lg">
//                                             <div>
//                                                 <h4 className="font-medium text-foreground">
//                                                     Email Notifications
//                                                 </h4>
//                                                 <p className="text-sm text-foreground/70">
//                                                     Receive notifications via
//                                                     email
//                                                 </p>
//                                             </div>
//                                             <label className="relative inline-flex items-center cursor-pointer">
//                                                 <input
//                                                     type="checkbox"
//                                                     name="emailNotifications"
//                                                     checked={
//                                                         systemSettings.emailNotifications
//                                                     }
//                                                     onChange={
//                                                         handleSystemChange
//                                                     }
//                                                     className="sr-only peer"
//                                                 />
//                                                 <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal"></div>
//                                             </label>
//                                         </div>

//                                         <div className="flex items-center justify-between p-4 border border-stroke rounded-lg">
//                                             <div>
//                                                 <h4 className="font-medium text-foreground">
//                                                     SMS Notifications
//                                                 </h4>
//                                                 <p className="text-sm text-foreground/70">
//                                                     Receive notifications via
//                                                     SMS
//                                                 </p>
//                                             </div>
//                                             <label className="relative inline-flex items-center cursor-pointer">
//                                                 <input
//                                                     type="checkbox"
//                                                     name="smsNotifications"
//                                                     checked={
//                                                         systemSettings.smsNotifications
//                                                     }
//                                                     onChange={
//                                                         handleSystemChange
//                                                     }
//                                                     className="sr-only peer"
//                                                 />
//                                                 <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal"></div>
//                                             </label>
//                                         </div>
//                                     </div>

//                                     <div className="flex justify-end">
//                                         <button
//                                             type="submit"
//                                             disabled={isSubmitting}
//                                             className="px-6 py-3 bg-teal text-white font-medium rounded-lg hover:bg-tealdarker focus:ring-2 focus:ring-teal focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
//                                         >
//                                             {isSubmitting
//                                                 ? "Saving..."
//                                                 : "Save Notification Settings"}
//                                         </button>
//                                     </div>
//                                 </form>
//                             </div>
//                         )}

//                         {/* Appearance Tab */}
//                         {activeTab === "appearance" && (
//                             <div className="p-6">
//                                 <div className="mb-6">
//                                     <h2 className="text-2xl font-semibold text-foreground mb-2">
//                                         Appearance Settings
//                                     </h2>
//                                     <p className="text-foreground/70">
//                                         Customize the look and feel of the
//                                         platform
//                                     </p>
//                                 </div>

//                                 <div className="space-y-6">
//                                     <div className="p-6 border border-stroke rounded-lg">
//                                         <h3 className="text-lg font-semibold text-foreground mb-4">
//                                             Color Scheme
//                                         </h3>
//                                         <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
//                                             <div className="text-center">
//                                                 <div className="w-16 h-16 bg-teal rounded-lg mx-auto mb-2 border-2 border-teal"></div>
//                                                 <p className="text-sm font-medium text-foreground">
//                                                     Primary Teal
//                                                 </p>
//                                                 <p className="text-xs text-foreground/70">
//                                                     #17B890
//                                                 </p>
//                                             </div>
//                                             <div className="text-center">
//                                                 <div className="w-16 h-16 bg-tealdarker rounded-lg mx-auto mb-2"></div>
//                                                 <p className="text-sm font-medium text-foreground">
//                                                     Teal Darker
//                                                 </p>
//                                                 <p className="text-xs text-foreground/70">
//                                                     #0E9B6D
//                                                 </p>
//                                             </div>
//                                             <div className="text-center">
//                                                 <div className="w-16 h-16 bg-background border border-stroke rounded-lg mx-auto mb-2"></div>
//                                                 <p className="text-sm font-medium text-foreground">
//                                                     Background
//                                                 </p>
//                                                 <p className="text-xs text-foreground/70">
//                                                     #F5F6FA
//                                                 </p>
//                                             </div>
//                                             <div className="text-center">
//                                                 <div className="w-16 h-16 bg-foreground rounded-lg mx-auto mb-2"></div>
//                                                 <p className="text-sm font-medium text-foreground">
//                                                     Foreground
//                                                 </p>
//                                                 <p className="text-xs text-foreground/70">
//                                                     #1E3D59
//                                                 </p>
//                                             </div>
//                                         </div>
//                                     </div>

//                                     <div className="p-6 border border-stroke rounded-lg">
//                                         <h3 className="text-lg font-semibold text-foreground mb-4">
//                                             Typography
//                                         </h3>
//                                         <div className="space-y-4">
//                                             <div>
//                                                 <p className="text-sm text-foreground/70 mb-2">
//                                                     Primary Font Family
//                                                 </p>
//                                                 <p className="text-lg font-semibold text-foreground">
//                                                     Bricolage Grotesque
//                                                 </p>
//                                             </div>
//                                             <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//                                                 <div>
//                                                     <p className="text-xs text-foreground/70 mb-1">
//                                                         Heading 1
//                                                     </p>
//                                                     <p className="text-2xl font-bold text-foreground">
//                                                         Sample Text
//                                                     </p>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-xs text-foreground/70 mb-1">
//                                                         Heading 2
//                                                     </p>
//                                                     <p className="text-xl font-semibold text-foreground">
//                                                         Sample Text
//                                                     </p>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-xs text-foreground/70 mb-1">
//                                                         Body Text
//                                                     </p>
//                                                     <p className="text-base text-foreground">
//                                                         Sample Text
//                                                     </p>
//                                                 </div>
//                                             </div>
//                                         </div>
//                                     </div>
//                                 </div>
//                             </div>
//                         )}
//                     </div>
//                 </div>
//             </div>
//         </div>
//     );
// }

// export default function Page() {
//     const { logout } = useAuth();
//     return (
//         <Suspense
//             fallback={
//                 <div>
//                     <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
//                         <CircularLoader size={32} color="teal" />
//                     </div>
//                 </div>
//             }
//         >
//             <SuperLayout
//                 navigation={navigation}
//                 showGoPro={true}
//                 onLogout={() => logout()}
//             >
//                 <SettingsContent />
//             </SuperLayout>
//         </Suspense>
//     );
// }
