const mongoose = require('mongoose');

const schoolSettingsSchema = new mongoose.Schema({
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true,
    unique: true,
  },
  general: {
    language: { type: String, default: 'en' },
    timezone: { type: String, default: 'UTC' },
    currency: { type: String, default: 'USD' }, // e.g., USD, EUR, NGN, etc.
  },
  preferences: {
    enable_notifications: { type: Boolean, default: true },
    default_grading_scale: {
      type: String,
      enum: ['A-F', 'Percentage', 'Pass/Fail'],
      default: 'A-F',
    },
    grading_system_base: {
      type: Number,
      enum: [100, 20, 10, 5],
      default: 100, // Common options: out of 100, 20, etc.
    },
  },
  credit: {
    credit_limit: { type: Number, default: 0, min: 0 },
    credit_usage_alert_threshold: {
      type: Number,
      default: 80,
      min: 0,
      max: 100,
    },
  },
}, {
  timestamps: true,
});

const SchoolSettings = mongoose.models.SchoolSettings || mongoose.model('SchoolSettings', schoolSettingsSchema);
module.exports = SchoolSettings;
