"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import MaintenanceMode from "./MaintenanceMode";
import CircularLoader from "./widgets/CircularLoader";
import { getSettings } from "@/app/services/Settings";

interface MaintenanceModeContextType {
    isMaintenanceMode: boolean;
    maintenanceMessage: string;
    platformName: string;
    refreshMaintenanceStatus: () => Promise<void>;
}

const MaintenanceModeContext = createContext<MaintenanceModeContextType | undefined>(undefined);

export const useMaintenanceMode = () => {
    const context = useContext(MaintenanceModeContext);
    if (context === undefined) {
        throw new Error("useMaintenanceMode must be used within a MaintenanceModeProvider");
    }
    return context;
};

interface MaintenanceModeProviderProps {
    children: React.ReactNode;
    bypassForAdmins?: boolean;
}

const MaintenanceModeProvider: React.FC<MaintenanceModeProviderProps> = ({
    children,
    bypassForAdmins = true,
}) => {
    const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);
    const [maintenanceMessage, setMaintenanceMessage] = useState("");
    const [platformName, setPlatformName] = useState("Scholarify");
    const [supportEmail, setSupportEmail] = useState("<EMAIL>");
    const [loading, setLoading] = useState(true);
    const [isAdmin, setIsAdmin] = useState(false);

    const checkMaintenanceStatus = async () => {
        try {
            const settings = await getSettings();
            const general = settings.general;

            setIsMaintenanceMode(general.maintenance_mode);
            setMaintenanceMessage(
                general.maintenance_message ||
                    "We are currently performing scheduled maintenance. Please check back soon."
            );
            setPlatformName(general.platform_name || "Scholarify");
            setSupportEmail(general.support_email || "<EMAIL>");
        } catch (error) {
            console.error("Error checking maintenance status:", error);
            setIsMaintenanceMode(false); // Fallback
        } finally {
            setLoading(false);
        }
    };

    const checkIfAdmin = () => {
        const userRole = localStorage.getItem("userRole");
        const currentPath = window.location.pathname;
        setIsAdmin(userRole === "super-admin" || currentPath.includes("/super-admin"));
    };

    useEffect(() => {
        checkIfAdmin();
        checkMaintenanceStatus();
        const interval = setInterval(checkMaintenanceStatus, 5 * 60 * 1000);
        return () => clearInterval(interval);
    }, []);

    const refreshMaintenanceStatus = async () => {
        await checkMaintenanceStatus();
    };

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <CircularLoader size={32} color="teal" />
            </div>
        );
    }

    if (isMaintenanceMode && !(bypassForAdmins && isAdmin)) {
        return (
            <MaintenanceMode
                message={maintenanceMessage}
                platformName={platformName}
                supportEmail={supportEmail}
            />
        );
    }

    const contextValue: MaintenanceModeContextType = {
        isMaintenanceMode,
        maintenanceMessage,
        platformName,
        refreshMaintenanceStatus,
    };

    return (
        <MaintenanceModeContext.Provider value={contextValue}>
            {children}
        </MaintenanceModeContext.Provider>
    );
};

export default MaintenanceModeProvider;
