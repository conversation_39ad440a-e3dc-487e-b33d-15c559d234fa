"use client";

import { AlertTriangle } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import { createSuccessNotification, createErrorNotification, NotificationState } from "@/app/types/notification";
import { DisciplineSchema } from "@/app/models/Discipline";
import { getDisciplines, createDiscipline, updateDiscipline, deleteDiscipline, deleteMultipleDisciplines } from "@/app/services/DisciplineServices";
import { getStudentsBySchool } from "@/app/services/StudentServices";
import DisciplineModal from "@/components/modals/DisciplineModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import DisciplineSkeleton from "@/components/skeletons/DisciplineSkeleton";
import { verifyPassword } from "@/app/services/UserServices";
import { useTranslation } from "@/hooks/useTranslation";

const BASE_URL = "/school-admin";

interface Student {
  _id: string;
  name: string;
  student_id: string;
}

function DisciplineContent() {
  const [disciplines, setDisciplines] = useState<DisciplineSchema[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedDisciplines, setSelectedDisciplines] = useState<DisciplineSchema[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [disciplineToEdit, setDisciplineToEdit] = useState<DisciplineSchema | null>(null);
  const [disciplineToDelete, setDisciplineToDelete] = useState<DisciplineSchema | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(null);
  const [clearSelection, setClearSelection] = useState(false);
  const { user } = useAuth();
  const { t, tDashboard } = useTranslation();
  const router = useRouter();



  // Columns for the table
  const columns = [
    {
      header: tDashboard('school-admin', 'discipline', 'discipline_id'),
      accessor: (row: DisciplineSchema) => (
        <span className="font-medium">{row.discipline_id}</span>
      )
    },
    {
      header: tDashboard('school-admin', 'discipline', 'student'),
      accessor: (row: DisciplineSchema) => {
        const student = students.find(s => s._id === row.student_id);
        return student ? `${student.name} (${student.student_id})` : tDashboard('school-admin', 'discipline', 'unknown_student');
      }
    },
    {
      header: tDashboard('school-admin', 'discipline', 'comments'),
      accessor: (row: DisciplineSchema) => (
        <span className="text-sm">{row.comments || tDashboard('school-admin', 'discipline', 'no_comments')}</span>
      )
    },
    {
      header: tDashboard('school-admin', 'discipline', 'created_at'),
      accessor: (row: DisciplineSchema) => {
        if (!row.createdAt) return t('common.not_available');
        try {
          return new Date(row.createdAt).toLocaleDateString();
        } catch {
          return tDashboard('school-admin', 'discipline', 'invalid_date');
        }
      }
    }
  ];

  // Actions for the table
  const actions = [
    {
      label: t('common.edit'),
      onClick: (discipline: DisciplineSchema) => {
        handleEditDiscipline(discipline);
      },
    },
    {
      label: t('common.delete'),
      onClick: (discipline: DisciplineSchema) => {
        handleDeleteDiscipline(discipline);
      },
    },
  ];

  // Load data on page load
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoadingData(true);
      
      // Fetch disciplines and students in parallel
      const [disciplinesData, studentsData] = await Promise.all([
        getDisciplines(),
        user?.school_ids?.[0] ? getStudentsBySchool(user.school_ids[0]) : []
      ]);

      // Filter disciplines by school if user has school_ids
      if (user && user.school_ids && user.school_ids.length > 0) {
        const schoolId = user.school_ids[0];
        const filteredDisciplines = disciplinesData.filter(discipline => discipline.school_id === schoolId);
        setDisciplines(filteredDisciplines);
      } else {
        setDisciplines(disciplinesData);
      }

      setStudents(studentsData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setSubmitStatus(createErrorNotification(tDashboard('school-admin', 'discipline', 'failed_to_fetch')));
    } finally {
      setLoadingData(false);
    }
  };

  // Handle creating new discipline
  const handleCreateDiscipline = () => {
    setDisciplineToEdit(null);
    setIsModalOpen(true);
  };

  // Handle editing discipline
  const handleEditDiscipline = (discipline: DisciplineSchema) => {
    setDisciplineToEdit(discipline);
    setIsModalOpen(true);
  };

  // Handle deleting single discipline
  const handleDeleteDiscipline = (discipline: DisciplineSchema) => {
    setDisciplineToDelete(discipline);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  // Handle deleting multiple disciplines
  const handleDeleteMultiple = (selectedIds: string[]) => {
    setDeleteType("multiple");
    setDisciplineToDelete(null);
    setIsDeleteModalOpen(true);
  };

  // Handle selection change
  const handleSelectionChange = (selected: DisciplineSchema[]) => {
    setSelectedDisciplines(selected);
  };

  // Handle save (create or update)
  const handleSave = async (disciplineData: any) => {
    setIsSubmitting(true);
    try {
      if (disciplineToEdit) {
        // Update existing discipline
        await updateDiscipline(disciplineToEdit.discipline_id, disciplineData);
        setSubmitStatus(createSuccessNotification(tDashboard('school-admin', 'discipline', 'record_updated_successfully')));
      } else {
        // Create new discipline
        await createDiscipline(disciplineData);
        setSubmitStatus(createSuccessNotification(tDashboard('school-admin', 'discipline', 'record_created_successfully')));
      }
      
      setIsModalOpen(false);
      setDisciplineToEdit(null);
      await fetchData(); // Refresh the list
    } catch (error) {
      console.error("Error saving discipline:", error);
      setSubmitStatus(createErrorNotification(tDashboard('school-admin', 'discipline', 'failed_to_save')));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation with password
  const handleDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    try {
      // Verify password first
      if (!user?.email) {
        throw new Error(tDashboard('school-admin', 'discipline', 'user_email_not_found'));
      }

      await verifyPassword(user.email, password);

      if (disciplineToDelete) {
        // Delete single discipline
        await deleteDiscipline(disciplineToDelete._id);
        setSubmitStatus(createSuccessNotification(tDashboard('school-admin', 'discipline', 'record_deleted_successfully')));
      } else if (selectedDisciplines.length > 0) {
        // Delete multiple disciplines
        const ids = selectedDisciplines.map(discipline => discipline._id);
        await deleteMultipleDisciplines(ids);
        setSubmitStatus(createSuccessNotification(t('dashboard.school-admin.discipline.records_deleted_successfully', { count: selectedDisciplines.length })));
        setSelectedDisciplines([]);
      }

      setIsDeleteModalOpen(false);
      setDisciplineToDelete(null);
      await fetchData(); // Refresh the list
    } catch (error) {
      console.error("Error deleting discipline record(s):", error);
      if (error instanceof Error && error.message.includes("password")) {
        setSubmitStatus(createErrorNotification(tDashboard('school-admin', 'discipline', 'invalid_password')));
      } else {
        setSubmitStatus(createErrorNotification(tDashboard('school-admin', 'discipline', 'failed_to_delete')));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loadingData) {
    return <DisciplineSkeleton />;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4 text-text">{tDashboard('school-admin', 'discipline', 'discipline_records_management')}</h1>

      {submitStatus && (
        <div className="mb-4">
          <NotificationCard
            type={submitStatus.type}
            title={submitStatus.title}
            message={submitStatus.message}
            onClose={() => setSubmitStatus(null)}
            isVisible={true}
          />
        </div>
      )}

      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: 'spring', stiffness: 300 }}
        onClick={handleCreateDiscipline}
        className="mb-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
      >
        {tDashboard('school-admin', 'discipline', 'add_new_discipline_record')}
      </motion.button>

      <DataTableFix<DisciplineSchema>
        data={disciplines}
        columns={columns}
        actions={actions}
        defaultItemsPerPage={10}
        onSelectionChange={handleSelectionChange}
        handleDeleteMultiple={handleDeleteMultiple}
        clearSelection={clearSelection}
        onSelectionCleared={() => setClearSelection(false)}
      />

      {/* Discipline Modal */}
      <DisciplineModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setDisciplineToEdit(null);
        }}
        onSave={handleSave}
        discipline={disciplineToEdit}
        isSubmitting={isSubmitting}
        students={students}
      />

      {/* Delete Confirmation Modal with Password */}
      <PasswordConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDisciplineToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        title={disciplineToDelete ? tDashboard('school-admin', 'discipline', 'delete_discipline_record') : tDashboard('school-admin', 'discipline', 'delete_selected_records')}
        message={
          disciplineToDelete
            ? t('dashboard.school-admin.discipline.delete_confirmation_single', { disciplineId: disciplineToDelete.discipline_id })
            : t('dashboard.school-admin.discipline.delete_confirmation_multiple', { count: selectedDisciplines.length })
        }
        itemName={disciplineToDelete?.discipline_id}
        itemCount={selectedDisciplines.length}
        type={disciplineToDelete ? "single" : "multiple"}
        loading={isSubmitting}
      />
    </div>
  );
}

export default function Page() {
  const { logout } = useAuth();
  const { tDashboard } = useTranslation();

  const navigation = {
    icon: AlertTriangle,
    baseHref: `${BASE_URL}/discipline`,
    title: tDashboard('school-admin', 'discipline', 'title')
  };

  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <DisciplineContent />
      </SchoolLayout>
    </Suspense>
  );
}
