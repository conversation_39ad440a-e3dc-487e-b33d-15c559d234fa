"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Check<PERSON><PERSON>cle, 
  CreditCard, 
  ArrowRight, 
  Loader2,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { checkCreditPurchaseStatus, checkSchoolCreditPaymentStatus } from '@/app/services/SubscriptionServices';
import { useCreditContext } from '@/context/CreditContext';
import { useTranslation } from '@/hooks/useTranslation';

export default function CreditPurchaseSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshAll } = useCreditContext();
  const { t, tDashboard } = useTranslation();
  
  const [paymentStatus, setPaymentStatus] = useState<'checking' | 'success' | 'failed' | 'pending' | 'expired'>('checking');
  const [purchaseDetails, setPurchaseDetails] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const transaction_id = searchParams.get('transaction_id') || searchParams.get('transId');
  const purchase_id = searchParams.get('purchase_id') || searchParams.get('externalId');

  // Debug: afficher tous les paramètres URL
  console.log('🔗 Paramètres URL reçus:');
  console.log('  - transaction_id:', transaction_id);
  console.log('  - purchase_id:', purchase_id);
  console.log('  - Tous les paramètres:', Object.fromEntries(searchParams.entries()));

  useEffect(() => {
    if (transaction_id) {
      checkPaymentStatus();
    } else {
      // Vérifier s'il y a une transaction en attente dans localStorage
      const pendingPurchase = localStorage.getItem('pending_purchase');
      if (pendingPurchase) {
        try {
          const data = JSON.parse(pendingPurchase);
          if (data.transaction_id) {
            // Utiliser la transaction du localStorage
            checkPaymentStatusById(data.transaction_id);
            return;
          }
        } catch (e) {
          console.error('Error parsing pending purchase:', e);
        }
      }
      setError('ID de transaction manquant');
      setPaymentStatus('failed');
    }
  }, [transaction_id]);

  const checkPaymentStatusById = async (transId: string) => {
    try {
      setPaymentStatus('checking');
      setError(null);

      console.log('🔍 Vérification du statut pour transaction:', transId);

      let response;
      try {
        // Essayer d'abord la route credit-purchase
        response = await checkCreditPurchaseStatus(transId);
        console.log('📦 Réponse reçue (credit-purchase):', response);
      } catch (firstError) {
        console.log('⚠️ Première route échouée, essai de la route alternative...');
        try {
          // Essayer la route school-credit-payment
          response = await checkSchoolCreditPaymentStatus(transId);
          console.log('📦 Réponse reçue (school-credit-payment):', response);
        } catch (secondError) {
          console.error('❌ Les deux routes ont échoué');
          throw firstError; // Relancer la première erreur
        }
      }

      setPurchaseDetails(response);

      if (response.payment_status === 'completed') {
        console.log('✅ Paiement confirmé comme réussi');
        setPaymentStatus('success');
        // Nettoyer le localStorage
        localStorage.removeItem('pending_purchase');
        // Rafraîchir les données de crédit
        await refreshAll();
      } else if (response.payment_status === 'failed') {
        console.log('❌ Paiement confirmé comme échoué');
        setPaymentStatus('failed');
        localStorage.removeItem('pending_purchase');
      } else if (response.payment_status === 'expired') {
        console.log('⏰ Paiement expiré');
        setPaymentStatus('expired');
        localStorage.removeItem('pending_purchase');
      } else if (response.payment_status === 'pending') {
        console.log('⏳ Paiement toujours en attente');
        setPaymentStatus('pending');
        // Réessayer après 3 secondes si moins de 10 tentatives
        if (retryCount < 10) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
            checkPaymentStatusById(transId);
          }, 3000);
        }
      } else {
        console.log('❓ Statut de paiement inconnu:', response.payment_status);
        setPaymentStatus('failed');
        setError(`Statut inconnu: ${response.payment_status}`);
      }
    } catch (err: any) {
      console.error('❌ Erreur lors de la vérification du paiement:', err);
      setError(err.message || 'Erreur lors de la vérification du paiement');
      setPaymentStatus('failed');
    }
  };

  const checkPaymentStatus = async () => {
    if (!transaction_id) return;
    return checkPaymentStatusById(transaction_id);
  };

  const handleRetry = () => {
    setRetryCount(0);
    checkPaymentStatus();
  };

  const handleGoToDashboard = () => {
    router.push('/school-admin/dashboard');
  };

  const handleGoToBuyCredit = () => {
    router.push('/school-admin/buy-credit');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center"
        >
          {/* Status Icon */}
          <div className="mb-6">
            {paymentStatus === 'checking' && (
              <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Loader2 className="h-8 w-8 text-blue-600 dark:text-blue-400 animate-spin" />
              </div>
            )}
            
            {paymentStatus === 'success' && (
              <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            )}
            
            {paymentStatus === 'failed' && (
              <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
            )}
            
            {paymentStatus === 'pending' && (
              <div className="mx-auto w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
                <CreditCard className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              </div>
            )}

            {paymentStatus === 'expired' && (
              <div className="mx-auto w-16 h-16 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              </div>
            )}
          </div>

          {/* Status Message */}
          <div className="mb-6">
            {paymentStatus === 'checking' && (
              <>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {t('dashboard.school-admin.pages.buy_credit.success.checking_payment')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {t('dashboard.school-admin.pages.buy_credit.success.checking_message')}
                </p>
                {retryCount > 0 && (
                  <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                    {t('dashboard.school-admin.pages.buy_credit.success.attempt_count', { count: retryCount })}
                  </p>
                )}
              </>
            )}
            
            {paymentStatus === 'success' && (
              <>
                <h1 className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
                  {t('dashboard.school-admin.pages.buy_credit.success.payment_successful')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {t('dashboard.school-admin.pages.buy_credit.success.success_message')}
                </p>
                {purchaseDetails && (
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-left">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">{t('dashboard.school-admin.pages.buy_credit.success.credits_purchased_label')}</span>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {purchaseDetails.credits_purchased}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">{t('dashboard.school-admin.pages.buy_credit.success.total_amount_label')}</span>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {purchaseDetails.total_amount?.toLocaleString()} FCFA
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">{t('dashboard.school-admin.pages.buy_credit.success.transaction_id_label')}</span>
                        <span className="font-mono text-xs text-gray-900 dark:text-white">
                          {purchaseDetails.transaction_id}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
            
            {paymentStatus === 'failed' && (
              <>
                <h1 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-2">
                  {t('dashboard.school-admin.pages.buy_credit.success.payment_failed')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {error || t('dashboard.school-admin.pages.buy_credit.success.payment_error_message')}
                </p>
                {transaction_id && (
                  <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">
                    ID: {transaction_id}
                  </p>
                )}
              </>
            )}
            
            {paymentStatus === 'pending' && (
              <>
                <h1 className="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                  {t('dashboard.school-admin.pages.buy_credit.success.payment_pending')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {t('dashboard.school-admin.pages.buy_credit.success.pending_message')}
                </p>
                {error && (
                  <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                    {error}
                  </p>
                )}
              </>
            )}

            {paymentStatus === 'expired' && (
              <>
                <h1 className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-2">
                  {t('dashboard.school-admin.pages.buy_credit.success.payment_expired')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {t('dashboard.school-admin.pages.buy_credit.success.expired_message')}
                </p>
                {transaction_id && (
                  <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">
                    ID: {transaction_id}
                  </p>
                )}
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {paymentStatus === 'success' && (
              <button
                onClick={handleGoToDashboard}
                className="w-full bg-blue-600 dark:bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center justify-center"
              >
                {t('dashboard.school-admin.pages.buy_credit.success.go_to_dashboard')}
                <ArrowRight className="h-4 w-4 ml-2" />
              </button>
            )}
            
            {(paymentStatus === 'failed' || paymentStatus === 'expired' || (paymentStatus === 'pending' && error)) && (
              <>
                <button
                  onClick={handleRetry}
                  className="w-full bg-blue-600 dark:bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center justify-center"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {t('dashboard.school-admin.pages.buy_credit.success.check_again')}
                </button>
                <button
                  onClick={handleGoToBuyCredit}
                  className="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  {t('dashboard.school-admin.pages.buy_credit.success.try_new_purchase')}
                </button>
              </>
            )}
            
            <button
              onClick={handleGoToDashboard}
              className="w-full text-gray-600 dark:text-gray-400 py-2 px-4 rounded-lg hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              {t('dashboard.school-admin.pages.buy_credit.success.back_to_dashboard')}
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
