"use client";

import { School, CloudUpload, X } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState, useCallback, useRef } from "react";
import useAuth from "@/app/hooks/useAuth";
import { getSchoolBy_id, uploadSchoolLogo } from "@/app/services/SchoolServices";
import { SchoolSchema } from "@/app/models/SchoolModel";
import NotificationCard from "@/components/NotificationCard";
import { createErrorNotification, NotificationState } from "@/app/types/notification";
import { useTranslation } from "@/hooks/useTranslation";
//shool page

const BASE_URL = "/school-admin";

function SchoolContent() {
  const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(null);
  const { user } = useAuth();
  const { t } = useTranslation();
  const [school, setSchool] = useState<SchoolSchema | null>(null);
  const [loading, setLoading] = useState(true);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(null);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [isDragging, setIsDragging] = useState(false); // New state for drag-and-drop visual feedback
  const fileInputRef = useRef<HTMLInputElement>(null); // Ref for the hidden file input

  // Load school data on page load
  useEffect(() => {
    const fetchSchool = async () => {
      try {
        setLoading(true);
        if (user && user.school_ids && user.school_ids.length > 0) {
          const schoolId = user.school_ids[0];
          const schoolData = await getSchoolBy_id(schoolId);
          setSchool(schoolData);
        }
      } catch (error) {
        console.error("Error fetching school:", error);
        setSubmitStatus(createErrorNotification("Failed to fetch school data"));
      } finally {
        setLoading(false);
      }
    };

    fetchSchool();
  }, [user]);

  // Create a preview URL whenever a file is selected
  useEffect(() => {
    if (logoFile) {
      const url = URL.createObjectURL(logoFile);
      setLogoPreviewUrl(url);
      return () => URL.revokeObjectURL(url); // Clean up the URL object
    }
    setLogoPreviewUrl(null);
  }, [logoFile]);

  // Handle file input change (from click or drop)
  const handleFileChange = useCallback((files: FileList | null) => {
    if (files && files.length > 0) {
      const file = files[0];
      // Basic validation for image types
      if (file.type.startsWith('image/')) {
        setLogoFile(file);
      } else {
        setSubmitStatus(createErrorNotification("Please select an image file (JPEG, PNG, GIF)."));
      }
    }
  }, []);

  // Drag and Drop Handlers
  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault(); // Necessary to allow dropping
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
    handleFileChange(event.dataTransfer.files);
  }, [handleFileChange]);

  // Handle school logo upload
  const handleLogoUpload = async () => {
    if (!logoFile || !school) return;

    setUploadingLogo(true);
    try {
      const response = await uploadSchoolLogo(school.school_id, logoFile);
      setSchool(prev => prev ? { ...prev, logo: response.logoUrl } as any : prev);
      setLogoFile(null); // Clear the selected file
      setSubmitStatus({
        type: "success",
        title: "Logo Upload Successful",
        message: "Your school's logo has been updated successfully.",
        isVisible: true,
      });
    } catch (error) {
      setSubmitStatus({
        type: "error",
        title: "Logo Upload Failed",
        message: error instanceof Error ? error.message : "Failed to upload logo.",
        isVisible: true,
      });
    } finally {
      setUploadingLogo(false);
    }
  };

  const handleClearLogo = () => {
    setLogoFile(null);
    setLogoPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = ''; // Clear the file input value
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  if (!school) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">School Information</h1>
        <div className="bg-red-100 p-4 rounded-lg">
          <p className="text-red-700">No school information found. Please contact support.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 font-inter"> {/* Added font-inter */}
      <h1 className="text-2xl font-bold mb-4 text-foreground">School Information</h1>

      {submitStatus && (
        <div className="mb-4">
          <NotificationCard
            type={submitStatus.type}
            title={submitStatus.title}
            message={submitStatus.message}
            onClose={() => setSubmitStatus(null)}
            isVisible={true}
          />
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-lg"> {/* Changed bg-background to bg-white for clarity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-xl font-semibold mb-4 text-foreground">{school.name}</h2>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">ID:</span> {school.school_id}
            </p>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Email:</span> {school.email || "Not specified"}
            </p>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Phone:</span> {school.phone_number || "Not specified"}
            </p>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Website:</span> {school.website || "Not specified"}
            </p>
          </div>

          <div>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Principal:</span> {school.principal_name}
            </p>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Established:</span>{" "}
              {new Date(school.established_year).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric"
              })}
            </p>
            <p className="text-gray-700 mb-2">
              <span className="font-medium">Address:</span> {school.address || "Not specified"}
            </p>
            <p className="text-gray-700 mb-2">
              <span className="font-bold">Administrator:</span> {user?.name || "Not specified"}
            </p>
          </div>
        </div>

        {/* --- Redesigned Logo Upload Section --- */}
        <div className="mt-8 border-t border-gray-200 pt-6"> {/* Added border-gray-200 */}
          <h3 className="text-xl font-semibold mb-4 text-gray-800">School Logo</h3>
          <div className="flex flex-col items-center gap-6 md:flex-row md:items-start">
            {/* Current/New Logo Display */}
            <div className="relative w-32 h-32 flex-shrink-0">
              <div className="w-full h-full rounded-full border-2 border-gray-300 overflow-hidden flex items-center justify-center bg-gray-100 shadow-sm">
                {(school as any).logo || logoPreviewUrl ? (
                  <img
                    src={logoPreviewUrl || (school as any).logo}
                    alt="School Logo"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <School size={48} className="text-gray-400" />
                )}
              </div>
              {logoFile && (
                <button
                  onClick={handleClearLogo}
                  className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                  aria-label="Clear selected logo"
                >
                  <X size={16} />
                </button>
              )}
            </div>

            {/* Upload and Interaction Area */}
            <div className="flex-1 w-full">
              <p className="text-gray-700 mb-4">
                Upload a new logo to represent your school. Recommended format: JPEG, PNG, or GIF, up to 2MB.
              </p>

              {/* Drag and Drop Zone */}
              <div
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()} // Trigger file input click
                className={`flex flex-col items-center justify-center w-full p-6 border-2 border-dashed rounded-lg transition-all duration-200
                ${isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300 bg-gray-50 hover:border-gray-400 cursor-pointer"}`}
              >
                <CloudUpload size={48} className="text-gray-400 mb-2" />
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={(e) => handleFileChange(e.target.files)}
                  accept="image/jpeg,image/png,image/gif"
                  className="hidden" // Hide the default file input
                />
                {isDragging ? (
                  <p className="text-gray-600 font-medium">Drop the file here...</p>
                ) : (
                  <p className="text-gray-600">
                    <span className="font-bold text-blue-600 hover:underline">Click to upload</span> or drag and drop
                  </p>
                )}
              </div>
              
              {logoFile && (
                <div className="flex flex-col sm:flex-row items-center justify-between mt-4 p-3 bg-gray-100 rounded-lg border border-gray-200 shadow-sm">
                  <p className="text-sm text-gray-700 font-medium truncate mb-2 sm:mb-0 sm:mr-4">
                    Selected: <span className="text-blue-700">{logoFile.name}</span>
                  </p>
                  <button
                    onClick={handleLogoUpload}
                    disabled={uploadingLogo}
                    className="w-full sm:w-auto px-6 py-2 bg-green-600 text-white rounded-lg shadow-md hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                  >
                    {uploadingLogo ? (
                      <div className="flex items-center justify-center">
                        <CircularLoader size={20} color="white" />
                        <span className="ml-2">Uploading...</span>
                      </div>
                    ) : (
                      "Upload Logo"
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
        {/* End Redesigned Logo Upload Section */}

        <div className="mt-6">
          <h3 className="text-lg font-medium mb-2 text-gray-800">Description</h3>
          <p className="text-gray-700">{school.description || "No description available."}</p>
        </div>
        <div className="mt-6 bg-yellow-50 p-4 rounded-lg border border-yellow-200"> {/* Adjusted yellow background and border */}
          <p className="text-yellow-800 text-sm">
            <strong>Note:</strong> If you wish to update any of the school details shown above, please contact Scolarify support.
          </p>
        </div>
      </div>
    </div>
  );
}

export default function Page() {
  const { logout } = useAuth();
  const { t } = useTranslation();

  const navigation = {
    icon: School,
    baseHref: `${BASE_URL}/school`,
    title: t('navigation.school')
  };

  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-screen absolute top-0 left-0 right-0 bottom-0 z-50 bg-white bg-opacity-80">
        <CircularLoader size={32} color="teal" />
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <SchoolContent />
      </SchoolLayout>
    </Suspense>
  );
}