/**
 * Script de test pour vérifier la suppression d'utilisateurs de Firebase
 * Teste avec l'email spécifique: <EMAIL>
 */

const mongoose = require('mongoose');
const admin = require('./src/utils/firebase');
const User = require('./src/models/User');
require('dotenv').config();

const TEST_EMAIL = '<EMAIL>';

async function testFirebaseUserDeletion() {
  try {
    console.log('🧪 Test de suppression d\'utilisateur Firebase\n');
    console.log(`📧 Email de test: ${TEST_EMAIL}\n`);
    
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie\n');

    // 1. Vérifier si l'utilisateur existe dans MongoDB
    console.log('🔍 Vérification dans MongoDB...');
    const mongoUser = await User.findOne({ email: TEST_EMAIL });
    
    if (mongoUser) {
      console.log(`✅ Utilisateur trouvé dans MongoDB:`);
      console.log(`   ID: ${mongoUser._id}`);
      console.log(`   User ID: ${mongoUser.user_id}`);
      console.log(`   Nom: ${mongoUser.name}`);
      console.log(`   Email: ${mongoUser.email}`);
      console.log(`   Rôle: ${mongoUser.role}`);
      console.log(`   Firebase UID: ${mongoUser.firebaseUid || 'Non défini'}`);
    } else {
      console.log(`❌ Utilisateur non trouvé dans MongoDB`);
    }

    // 2. Vérifier si l'utilisateur existe dans Firebase
    console.log('\n🔥 Vérification dans Firebase...');
    let firebaseUser = null;
    try {
      firebaseUser = await admin.auth().getUserByEmail(TEST_EMAIL);
      console.log(`✅ Utilisateur trouvé dans Firebase:`);
      console.log(`   UID: ${firebaseUser.uid}`);
      console.log(`   Email: ${firebaseUser.email}`);
      console.log(`   Email vérifié: ${firebaseUser.emailVerified}`);
      console.log(`   Créé le: ${new Date(firebaseUser.metadata.creationTime).toLocaleString()}`);
      console.log(`   Dernière connexion: ${firebaseUser.metadata.lastSignInTime ? new Date(firebaseUser.metadata.lastSignInTime).toLocaleString() : 'Jamais'}`);
    } catch (firebaseError) {
      console.log(`❌ Utilisateur non trouvé dans Firebase: ${firebaseError.message}`);
    }

    // 3. Si l'utilisateur existe dans les deux, tester la suppression
    if (mongoUser && firebaseUser) {
      console.log('\n⚠️ ATTENTION: L\'utilisateur existe dans les deux systèmes');
      console.log('Cela confirme le problème de synchronisation lors de la suppression');
      
      console.log('\n🗑️ Test de suppression complète...');
      
      // Supprimer de MongoDB
      console.log('📊 Suppression de MongoDB...');
      await User.deleteOne({ email: TEST_EMAIL });
      console.log('✅ Utilisateur supprimé de MongoDB');
      
      // Supprimer de Firebase
      console.log('🔥 Suppression de Firebase...');
      await admin.auth().deleteUser(firebaseUser.uid);
      console.log('✅ Utilisateur supprimé de Firebase');
      
      console.log('\n🎉 Suppression complète réussie !');
      
    } else if (mongoUser && !firebaseUser) {
      console.log('\n⚠️ Utilisateur existe seulement dans MongoDB');
      console.log('Suppression de MongoDB...');
      await User.deleteOne({ email: TEST_EMAIL });
      console.log('✅ Utilisateur supprimé de MongoDB');
      
    } else if (!mongoUser && firebaseUser) {
      console.log('\n⚠️ Utilisateur existe seulement dans Firebase');
      console.log('Suppression de Firebase...');
      await admin.auth().deleteUser(firebaseUser.uid);
      console.log('✅ Utilisateur supprimé de Firebase');
      
    } else {
      console.log('\n✅ Aucun utilisateur trouvé dans les deux systèmes');
      console.log('Le problème a peut-être déjà été résolu');
    }

    // 4. Vérification finale
    console.log('\n🔍 Vérification finale...');
    
    const finalMongoCheck = await User.findOne({ email: TEST_EMAIL });
    console.log(`MongoDB: ${finalMongoCheck ? '❌ Encore présent' : '✅ Supprimé'}`);
    
    try {
      await admin.auth().getUserByEmail(TEST_EMAIL);
      console.log(`Firebase: ❌ Encore présent`);
    } catch (error) {
      console.log(`Firebase: ✅ Supprimé`);
    }

    console.log('\n📋 RÉSUMÉ:');
    console.log('='.repeat(50));
    console.log('✅ Le script a vérifié et nettoyé les incohérences');
    console.log('✅ Les fonctions de suppression ont été mises à jour');
    console.log('✅ Maintenant les suppressions futures incluront Firebase');
    
    console.log('\n💡 RECOMMANDATIONS:');
    console.log('• Utilisez deleteUserById ou deleteMultipleUsers pour les suppressions futures');
    console.log('• Ces fonctions suppriment maintenant automatiquement de Firebase');
    console.log('• En cas de problème, ce script peut nettoyer les incohérences');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Connexion MongoDB fermée');
    console.log('🏁 Test terminé');
  }
}

/**
 * Fonction pour nettoyer un utilisateur spécifique des deux systèmes
 */
async function cleanupUser(email) {
  try {
    console.log(`🧹 Nettoyage de l'utilisateur: ${email}`);
    
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Supprimer de MongoDB
    const mongoResult = await User.deleteOne({ email: email });
    console.log(`MongoDB: ${mongoResult.deletedCount > 0 ? '✅ Supprimé' : '⚠️ Non trouvé'}`);

    // Supprimer de Firebase
    try {
      const firebaseUser = await admin.auth().getUserByEmail(email);
      await admin.auth().deleteUser(firebaseUser.uid);
      console.log(`Firebase: ✅ Supprimé (UID: ${firebaseUser.uid})`);
    } catch (firebaseError) {
      console.log(`Firebase: ⚠️ Non trouvé`);
    }

    await mongoose.disconnect();
    console.log(`✅ Nettoyage terminé pour ${email}`);

  } catch (error) {
    console.error(`❌ Erreur lors du nettoyage:`, error);
    await mongoose.disconnect();
  }
}

// Exécuter le test
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length > 0 && args[0] === 'cleanup') {
    const email = args[1] || TEST_EMAIL;
    cleanupUser(email)
      .then(() => process.exit(0))
      .catch((error) => {
        console.error('❌ Erreur:', error);
        process.exit(1);
      });
  } else {
    testFirebaseUserDeletion()
      .then(() => process.exit(0))
      .catch((error) => {
        console.error('❌ Erreur:', error);
        process.exit(1);
      });
  }
}

module.exports = { testFirebaseUserDeletion, cleanupUser };
