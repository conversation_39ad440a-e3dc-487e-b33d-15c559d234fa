"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { BookOpen, Users, Clock, Calendar, Plus, Eye } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { getTokenFromCookie } from "@/app/services/UserServices";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";
import TeacherClassesSkeleton from "@/components/skeletons/TeacherClassesSkeleton";
import ExamSupervisions from "@/components/Dashboard/Teacher/ExamSupervisions";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface ClassAssignment {
  class_id: string;
  class_name: string;
  subjects: string[];
  periods: string[];
  student_count: number;
  schedule: {
    day: string;
    time: string;
    subject: string;
  }[];
}

const navigation = {
  icon: BookOpen,
  baseHref: "/teacher-dashboard/classes",
  title: "My Classes"
};

export default function TeacherClassesPage() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [classes, setClasses] = useState<ClassAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const fetchingRef = useRef(false);

  const fetchClasses = useCallback(async (schoolId: string) => {
    // Prevent multiple simultaneous calls
    if (fetchingRef.current) {
      console.log("Fetch already in progress, skipping...");
      return;
    }

    try {
      fetchingRef.current = true;
      setLoading(true);

      // Use the teacher assignment service to get real data
      const { getTeacherPermissions, getTeacherStudents } = await import("@/app/services/TeacherPermissionServices");

      // Get teacher schedule data
      const token = getTokenFromCookie("idToken");
      const scheduleResponse = await fetch(`${process.env.BASE_API_URL || "https://backend.scholarifyltd.com/api"}/teacher/schedule/${schoolId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      const [teacherData, studentsData, scheduleData] = await Promise.all([
        getTeacherPermissions(schoolId),
        getTeacherStudents(schoolId),
        scheduleResponse.ok ? scheduleResponse.json() : { schedule: [] }
      ]);

      // Count students per class
      const studentCountByClass = studentsData.reduce((acc, student) => {
        acc[student.class_id] = (acc[student.class_id] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Group schedule data by class
      const scheduleByClass = scheduleData.schedule.reduce((acc: { [x: string]: { day: any; time: any; subject: any; period: any; }[]; }, entry: { class_id: string | number; day: any; time: any; subject: any; period: any; }) => {
        if (!acc[entry.class_id]) {
          acc[entry.class_id] = [];
        }
        acc[entry.class_id].push({
          day: entry.day,
          time: entry.time,
          subject: entry.subject,
          period: entry.period
        });
        return acc;
      }, {} as Record<string, Array<{ day: string; time: string; subject: string; period: string }>>);

      // Transform the data to match our interface
      const classAssignments: ClassAssignment[] = teacherData.assigned_classes.map(cls => {
        // Get subjects for this specific class from assigned_subjects
        let classSubjects = teacherData.assigned_subjects
          .filter(subject => subject.class_id === cls._id)
          .map(subject => subject.name);

        // Get schedule for this class
        const classSchedule = scheduleByClass[cls._id] || [];

        // If no subjects found in assigned_subjects, try to get from schedule
        if (classSubjects.length === 0) {
          classSubjects = [...new Set(classSchedule.map((s: { subject: any; }) => s.subject))] as string[];
          console.log(`No subjects in assigned_subjects for class ${cls.name}, using schedule subjects:`, classSubjects);
        }

        // If still no subjects, check if there are any subjects in the schedule that might not be filtered correctly
        if (classSubjects.length === 0) {
          // Try to get subjects from all schedule entries for debugging
          const allScheduleSubjects = scheduleData.schedule || [];
          const classScheduleSubjects = allScheduleSubjects
            .filter((s: { class_id: string; }) => s.class_id === cls._id)
            .map((s: { subject: any; }) => s.subject);
          classSubjects = [...new Set(classScheduleSubjects)] as string[];
          console.log(`Fallback: Using direct schedule lookup for class ${cls.name}:`, classSubjects);
        }

        // Get unique periods for this class
        const classPeriods = [...new Set(classSchedule.map((s: { period: any; time: any; }) => `Period ${String(s.period)} (${String(s.time)})`))] as string[];

        console.log(`Class ${cls.name} (${cls._id}):`, {
          assigned_subjects_count: teacherData.assigned_subjects.filter(s => s.class_id === cls._id).length,
          schedule_subjects: [...new Set(classSchedule.map((s: { subject: any; }) => s.subject))],
          final_subjects: classSubjects,
          class_schedule_length: classSchedule.length
        });

        return {
          class_id: cls._id,
          class_name: cls.name,
          subjects: classSubjects,
          periods: classPeriods,
          student_count: studentCountByClass[cls._id] || 0,
          schedule: classSchedule
        };
      });

      setClasses(classAssignments);
    } catch (error) {
      console.error("Error fetching classes:", error);
      setClasses([]);
    } finally {
      setLoading(false);
      fetchingRef.current = false;
    }
  }, []);

  useEffect(() => {
    // Only run once when component mounts or when user changes from null to defined
    if (isInitialized) return;

    // Check if user is loaded
    if (!user) return;

    // Check if user is a teacher
    if (user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        fetchClasses(school.school_id);
        setIsInitialized(true);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router, fetchClasses, isInitialized]);

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const getSubjectColor = (subject: string) => {
    const colors = {
      Mathematics: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      Physics: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      Chemistry: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Biology: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      English: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      History: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
    };
    return colors[subject as keyof typeof colors] || "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <TeacherClassesSkeleton itemCount={4} />
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl sm:text-2xl font-bold text-foreground">My Classes</h1>
                  <p className="text-sm sm:text-base text-foreground/60">
                    Manage your classes at {selectedSchool?.school_name}
                  </p>
                </div>
              </div>

              <div className="text-left sm:text-right">
                <p className="text-xl sm:text-2xl font-bold text-foreground">{classes.length}</p>
                <p className="text-xs sm:text-sm text-foreground/60">Total Classes</p>
              </div>
            </div>
          </div>

          {/* Classes Grid */}
          {classes.length === 0 ? (
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="text-center py-12">
                <BookOpen className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No Classes Assigned</h3>
                <p className="text-foreground/60 mb-6">
                  You don't have any classes assigned yet. Contact your school administrator to get class assignments.
                </p>
                
                <button
                  onClick={() => router.push("/teacher-dashboard/dashboard")}
                  className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
                >
                  Back to Dashboard
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {classes.map((classItem, index) => (
                <motion.div
                  key={classItem.class_id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-widget rounded-lg border border-stroke p-4 sm:p-6 hover:shadow-lg transition-all"
                >
                  {/* Class Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-2">
                        {classItem.class_name}
                      </h3>
                      <div className="flex items-center space-x-2 text-foreground/60">
                        <Users className="h-4 w-4" />
                        <span className="text-sm">{classItem.student_count} students</span>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => router.push(`/teacher-dashboard/classes/${classItem.class_id}`)}
                      className="p-2 text-foreground/60 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                      title="View class details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Subjects */}
                  <div className="mb-4">
                    <p className="text-sm font-medium text-foreground/70 mb-2">Subjects:</p>
                    <div className="flex flex-wrap gap-2">
                      {classItem.subjects.map((subject) => (
                        <span
                          key={subject}
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getSubjectColor(subject)}`}
                        >
                          {subject}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Schedule Preview */}
                  <div className="mb-4">
                    <p className="text-sm font-medium text-foreground/70 mb-2">Schedule:</p>
                    {classItem.schedule.length > 0 ? (
                      <div className="space-y-1">
                        {classItem.schedule.slice(0, 3).map((schedule, idx) => (
                          <div key={idx} className="flex items-center space-x-2 text-sm text-foreground/60">
                            <Calendar className="h-3 w-3" />
                            <span>{schedule.day}</span>
                            <Clock className="h-3 w-3 ml-2" />
                            <span>{schedule.time}</span>
                            <span className="text-foreground/40">•</span>
                            <span>{schedule.subject}</span>
                          </div>
                        ))}
                        {classItem.schedule.length > 3 && (
                          <p className="text-xs text-foreground/50">
                            +{classItem.schedule.length - 3} more sessions
                          </p>
                        )}
                      </div>
                    ) : (
                      <p className="text-xs text-foreground/50 italic">
                        No schedule assigned yet
                      </p>
                    )}
                  </div>

                  {/* Quick Actions */}
                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 pt-4 border-t border-stroke">
                    <button
                      onClick={() => router.push(`/teacher-dashboard/attendance/subjects?classId=${classItem.class_id}`)}
                      className="flex-1 px-3 py-2 text-sm bg-teal text-white rounded-md hover:bg-teal-600 transition-colors touch-manipulation"
                    >
                      Take Attendance
                    </button>
                    <button
                      onClick={() => router.push(`/teacher-dashboard/grades?class=${classItem.class_id}`)}
                      className="flex-1 px-3 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors touch-manipulation"
                    >
                      Enter Grades
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm text-foreground/60">Total Students</p>
                  <p className="text-xl sm:text-2xl font-bold text-foreground">
                    {classes.reduce((sum, cls) => sum + cls.student_count, 0)}
                  </p>
                </div>
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Users className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm text-foreground/60">Subjects</p>
                  <p className="text-xl sm:text-2xl font-bold text-foreground">
                    {new Set(classes.flatMap(cls => cls.subjects)).size}
                  </p>
                </div>
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm text-foreground/60">Total Sessions</p>
                  <p className="text-xl sm:text-2xl font-bold text-foreground">
                    {classes.reduce((sum, cls) => sum + cls.schedule.length, 0)}
                  </p>
                </div>
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm text-foreground/60">Today's Classes</p>
                  <p className="text-xl sm:text-2xl font-bold text-foreground">
                    {classes.reduce((sum, cls) =>
                      sum + cls.schedule.filter(s =>
                        s.day === new Date().toLocaleDateString('en-US', { weekday: 'long' })
                      ).length, 0
                    )}
                  </p>
                </div>
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                  <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Exam Supervisions Section */}
          {selectedSchool && user && (
            <ExamSupervisions
              schoolId={selectedSchool.school_id}
              teacherId={user._id}
              className="mt-6"
            />
          )}
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
