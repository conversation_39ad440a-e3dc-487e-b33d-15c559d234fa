"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect, useCallback } from "react";
import Logo from "@/components/widgets/Logo";
import ThemeToggle from "@/components/ThemeToggle";
import { StaticImageData } from "next/image";

// Team Images
import Kevin from "../assets/images/Team/Kevin.jpeg";
import Leni4c from "../assets/images/Team/Leni4c.jpg";
import <PERSON> from "../assets/images/Team/Michel.jpeg";
import Tosin from "../assets/images/Team/Tosin.jpeg";
import Bekenie from "../assets/images/Team/Bekenie.jpeg";
import Thierry from "../assets/images/Team/Thierry.jpeg";
import Jordan from "../assets/images/Team/Jordan.jpeg";
import Franck from "../assets/images/Team/Franck.jpeg";

// Screen Images
import AdminDash from "../assets/images/Screens/AdDash.png";
import SchoolScreen from "../assets/images/Screens/SchoolScreen.png";
import StudentScreen from "../assets/images/Screens/StudentScreen.png";
import StudentApp from "../assets/images/Screens/StudentApp.png";
import ParentApp from "../assets/images/Screens/ParentApp.png";
import AttendApp from "../assets/images/Screens/AttendApp.png";

import {
    ChevronRight,
    BookOpen,
    Users,
    BarChart3,
    MessageCircle,
    Smartphone,
    Monitor,
    GraduationCap,
    Clock,
    Shield,
    Menu,
    X,
    ArrowUp,
    Play,
    Pause,
    ChevronDown,
    Star,
    CheckCircle,
    Zap,
    Heart,
} from "lucide-react";
import Image from "next/image";

// Team member interface
interface TeamMember {
    name: string;
    role: string;
    bio: string;
    avatar: string | StaticImageData;
}

// Feature interface
interface Feature {
    icon: React.ReactNode;
    title: string;
    description: string;
    benefits: string[];
}

// FAQ interface
interface FAQ {
    question: string;
    answer: string;
    isOpen?: boolean;
}

export default function Home() {
    const router = useRouter();
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [showScrollTop, setShowScrollTop] = useState(false);
    const [isCarouselPlaying, setIsCarouselPlaying] = useState(true);
    const [openFAQ, setOpenFAQ] = useState<number | null>(null);
    const [activeSection, setActiveSection] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [mounted, setMounted] = useState(false);

    // Fix hydration by ensuring client-side only rendering for dynamic content
    useEffect(() => {
        setMounted(true);
    }, []);

    // Dashboard preview images with placeholder URLs
    const dashboardImages = [
        {
            url: AdminDash,
            title: "Admin Dashboard",
            description:
                "Comprehensive analytics and school management overview",
        },
        {
            url: SchoolScreen,
            title: "Teacher Portal",
            description: "Class management and student progress tracking",
        },
        {
            url: StudentScreen,
            title: "Student View",
            description: "Real-time insights and performance metrics",
        },
    ];

    // Mobile app preview images
    const mobileImages = [
        {
            url: StudentApp,
            title: "Student Portal",
        },
        {
            url: ParentApp,
            title: "Parent Dashboard",
        },
        {
            url: AttendApp,
            title: "Attendance Dash",
        },
    ];

    // Updated team members with your actual team
    const teamMembers: TeamMember[] = [
        {
            name: "Nonche Thierry",
            role: "Co-Founder / General Manager",
            bio: "Visionary leader with a passion for transforming education through technology. Drives strategic initiatives and operational excellence while fostering innovation in educational solutions that empower schools and students worldwide.",
            avatar: Thierry,
        },
        {
            name: "BEKENIE RANDY",
            role: "Co-Founder / Financial Manager",
            bio: "Strategic financial leader with expertise in scaling EdTech ventures. Manages fiscal operations, investment strategies, and financial planning to ensure sustainable growth and maximize impact in the education sector.",
            avatar: Bekenie,
        },
        {
            name: "Franck Elysee",
            role: "Senior Full-Stack Developer",
            bio: "Seasoned full-stack architect with deep expertise in building scalable educational platforms. Specializes in designing robust backend systems and intuitive frontend experiences that handle complex educational workflows seamlessly.",
            avatar: Franck,
        },
        {
            name: "Jordan",
            role: "Chief Technology Officer",
            bio: "Technology visionary leading digital transformation in education. Oversees technical strategy, infrastructure development, and innovation initiatives while building high-performing engineering teams focused on educational impact.",
            avatar: Jordan,
        },
        {
            name: "Oluwatosin",
            role: "Product Designer",
            bio: "User-centered design expert passionate about making education technology accessible and engaging. Creates intuitive interfaces and seamless user experiences that bridge the gap between complex functionality and user-friendly design.",
            avatar: Tosin,
        },
        {
            name: "Leni4c",
            role: "Frontend Engineer",
            bio: "Frontend specialist crafting responsive, high-performance web applications. Focuses on modern JavaScript frameworks, accessibility standards, and creating delightful user interfaces that enhance the educational experience.",
            avatar: Leni4c,
        },
        {
            name: "Kevin Boukeng ",
            role: "Software Engineer",
            bio: "Skilled software engineer building reliable, maintainable solutions for educational challenges. Contributes to both frontend and backend development while ensuring code quality and system performance across the platform.",
            avatar: Kevin,
        },
        {
            name: "Michel Jérémie (MJ)",
            role: "Fullstack Dev / UI & UX Designer",
            bio: "Versatile developer-designer hybrid combining technical implementation with design thinking. Creates cohesive user experiences from concept to code, ensuring both visual appeal and functional excellence in educational tools.",
            avatar: Michel,
        },
    ];

    // Enhanced features data with benefits
    const features: Feature[] = [
        {
            icon: <BarChart3 className="w-8 h-8" />,
            title: "Admin Dashboard",
            description:
                "Comprehensive school management with analytics, reporting, and administrative controls.",
            benefits: [
                "Real-time analytics",
                "Custom reports",
                "Data visualization",
                "Performance tracking",
            ],
        },
        {
            icon: <GraduationCap className="w-8 h-8" />,
            title: "Teacher Portal",
            description:
                "Class management, student tracking, assignment creation, and grade management tools.",
            benefits: [
                "Grade management",
                "Assignment creation",
                "Student progress",
                "Class scheduling",
            ],
        },
        {
            icon: <Users className="w-8 h-8" />,
            title: "Student Information System",
            description:
                "Complete student profiles, academic records, attendance tracking, and progress monitoring.",
            benefits: [
                "Student profiles",
                "Academic records",
                "Attendance tracking",
                "Progress reports",
            ],
        },
        {
            icon: <MessageCircle className="w-8 h-8" />,
            title: "Communication Tools",
            description:
                "Seamless communication between teachers, students, and parents with messaging and notifications.",
            benefits: [
                "Instant messaging",
                "Push notifications",
                "Email integration",
                "Parent portal",
            ],
        },
        {
            icon: <Clock className="w-8 h-8" />,
            title: "Class Scheduling",
            description:
                "Automated timetable generation, room allocation, and schedule management.",
            benefits: [
                "Auto scheduling",
                "Room management",
                "Conflict resolution",
                "Calendar sync",
            ],
        },
        {
            icon: <Shield className="w-8 h-8" />,
            title: "Secure & Reliable",
            description:
                "Enterprise-grade security with data encryption and reliable cloud infrastructure.",
            benefits: [
                "Data encryption",
                "Secure authentication",
                "Regular backups",
                "99.9% uptime",
            ],
        },
    ];

    // Enhanced FAQ data
    const faqs: FAQ[] = [
        {
            question: "What is Scholarify?",
            answer: "Scholarify is a comprehensive school management system designed to streamline administrative tasks, enhance communication, and improve educational outcomes for schools of all sizes. Our platform integrates all aspects of school management into one seamless solution.",
        },
        {
            question: "Who can use Scholarify?",
            answer: "Scholarify is designed for school administrators, teachers, students, and parents. Each user type has access to relevant features and information tailored to their specific needs and responsibilities within the educational ecosystem.",
        },
        {
            question: "Is Scholarify secure?",
            answer: "Yes, Scholarify uses enterprise-grade security measures including end-to-end data encryption, secure authentication protocols, regular security audits, and compliance with educational data protection standards like FERPA and COPPA.",
        },
        {
            question: "Can I try Scholarify before purchasing?",
            answer: "Absolutely! We offer a comprehensive 30-day free trial where you can explore all features, import your data, and see how Scholarify fits your school's unique needs. No credit card required to start your trial.",
        },
        {
            question: "Do you provide training and support?",
            answer: "Yes! We provide comprehensive onboarding, training materials, video tutorials, documentation, and dedicated support. Our customer success team ensures smooth implementation and ongoing success with 24/7 support availability.",
        },
        {
            question: "How does pricing work?",
            answer: "Our pricing is based on the number of students and features you need. We offer flexible plans for small schools to large districts, with custom enterprise solutions available. Contact us for a personalized quote.",
        },
    ];

    // Stats data for credibility
    const stats = [
        {
            number: "100+",
            label: "Students Managed",
            icon: <Users className="w-6 h-6" />,
        },
        {
            number: "50+",
            label: "Schools Trust Us",
            icon: <GraduationCap className="w-6 h-6" />,
        },
        {
            number: "99.9%",
            label: "Uptime Guarantee",
            icon: <Shield className="w-6 h-6" />,
        },
        {
            number: "24/7",
            label: "Support Available",
            icon: <Heart className="w-6 h-6" />,
        },
    ];

    // Auto-rotate dashboard images with play/pause functionality
    useEffect(() => {
        if (!mounted || !isCarouselPlaying) return;

        const interval = setInterval(() => {
            setCurrentImageIndex((prev) => (prev + 1) % dashboardImages.length);
        }, 4000);
        return () => clearInterval(interval);
    }, [dashboardImages.length, isCarouselPlaying, mounted]);

    // Scroll detection for back-to-top button and active section
    useEffect(() => {
        if (!mounted) return;

        const handleScroll = () => {
            setShowScrollTop(window.scrollY > 400);

            // Update active section based on scroll position
            const sections = [
                "about",
                "features",
                "previews",
                "docs",
                "team",
                "faq",
            ];
            const currentSection = sections.find((section) => {
                const element = document.getElementById(section);
                if (element) {
                    const rect = element.getBoundingClientRect();
                    return rect.top <= 100 && rect.bottom >= 100;
                }
                return false;
            });

            if (currentSection) {
                setActiveSection(currentSection);
            }
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, [mounted]);

    // Enhanced scroll to section with loading state
    const scrollToSection = useCallback(
        (sectionId: string) => {
            if (!mounted) return;

            setIsLoading(true);
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: "smooth", block: "start" });
                setTimeout(() => setIsLoading(false), 800);
            }
            setMobileMenuOpen(false);
        },
        [mounted]
    );

    // Scroll to top function
    const scrollToTop = () => {
        if (!mounted) return;
        window.scrollTo({ top: 0, behavior: "smooth" });
    };

    // Handle logo click to go home
    const handleLogoClick = () => {
        if (!mounted) return;

        if (window.scrollY > 100) {
            scrollToTop();
        } else {
            router.push("/");
        }
    };

    // Toggle FAQ
    const toggleFAQ = (index: number) => {
        setOpenFAQ(openFAQ === index ? null : index);
    };

    // Handle navigation with loading state
    const handleNavigation = async (path: string) => {
        setIsLoading(true);
        await router.push(path);
        setIsLoading(false);
    };

    return (
        <div className="min-h-screen bg-white dark:bg-gray-900 relative">
            {/* Loading Overlay */}
            {isLoading && (
                <div className="fixed inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50 flex items-center justify-center">
                    <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 border-4 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-teal-600 font-medium">
                            Loading...
                        </span>
                    </div>
                </div>
            )}

            {/* Navigation Header */}
            <nav className="fixed top-0 left-0 right-0 z-40 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 transition-all duration-300 shadow-sm dark:shadow-2xl">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        {/* Clickable Logo */}
                        <div className="flex items-center">
                            <button
                                onClick={handleLogoClick}
                                className="transition-transform duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 rounded-lg"
                                aria-label="Go to homepage"
                            >
                                <Logo />
                            </button>
                        </div>

                        {/* Desktop Navigation */}
                        <div className="hidden md:flex items-center space-x-8">
                            {[
                                { id: "about", label: "About" },
                                { id: "features", label: "Features" },
                                { id: "team", label: "Team" },
                                { id: "docs", label: "Docs" },
                            ].map(({ id, label }) => (
                                <button
                                    key={id}
                                    onClick={() => scrollToSection(id)}
                                    className={`text-gray-800 dark:text-gray-100 hover:text-teal-600 dark:hover:text-teal-300 transition-colors font-medium relative ${
                                        activeSection === id
                                            ? "text-teal-600 dark:text-teal-300"
                                            : ""
                                    }`}
                                >
                                    {label}
                                    {mounted && activeSection === id && (
                                        <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-teal-600 dark:bg-teal-300 rounded-full"></div>
                                    )}
                                </button>
                            ))}
                        </div>

                        <div className="flex items-center space-x-4">
                            {/* Theme Toggle */}
                            <ThemeToggle />

                            <button
                                onClick={() => handleNavigation("/login")}
                                className="bg-teal-600 hover:bg-teal-700 dark:text-white text-gray-800 px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                            >
                                Login
                            </button>

                            {/* Mobile menu button */}
                            <button
                                onClick={() =>
                                    setMobileMenuOpen(!mobileMenuOpen)
                                }
                                className="md:hidden text-gray-800 dark:text-gray-100 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                                aria-label="Toggle mobile menu"
                            >
                                {mobileMenuOpen ? (
                                    <X className="w-6 h-6" />
                                ) : (
                                    <Menu className="w-6 h-6" />
                                )}
                            </button>
                        </div>
                    </div>

                    {/* Enhanced Mobile Navigation */}
                    {mobileMenuOpen && (
                        <div className="md:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                            <div className="px-2 pt-2 pb-3 space-y-1">
                                {[
                                    { id: "about", label: "About" },
                                    { id: "features", label: "Features" },
                                    { id: "team", label: "Team" },
                                    { id: "docs", label: "Docs" },
                                ].map(({ id, label }) => (
                                    <button
                                        key={id}
                                        onClick={() => scrollToSection(id)}
                                        className={`block px-3 py-2 text-gray-800 dark:text-gray-100 hover:text-teal-600 dark:hover:text-teal-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors w-full text-left font-medium rounded-lg ${
                                            activeSection === id
                                                ? "text-teal-600 dark:text-teal-300 bg-teal-50 dark:bg-teal-900/20"
                                                : ""
                                        }`}
                                    >
                                        {label}
                                    </button>
                                ))}

                                {/* Mobile Theme Toggle */}
                                <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-800 dark:text-gray-100 font-medium">
                                            Theme
                                        </span>
                                        <ThemeToggle />
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </nav>

            {/* Enhanced Hero Section */}
            <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-teal-50 dark:from-gray-900 dark:via-gray-800 dark:to-teal-900 relative overflow-hidden min-h-[80vh] flex items-center">
                {/* Background decoration */}
                <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
                <div className="absolute top-20 right-10 w-72 h-72 bg-teal-200/40 dark:bg-teal-500/20 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
                <div className="absolute bottom-20 left-10 w-72 h-72 bg-blue-200/40 dark:bg-blue-500/20 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-200/20 dark:bg-purple-500/10 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse delay-2000"></div>

                <div className="max-w-7xl mx-auto relative">
                    <div className="text-center">
                        <div className="inline-flex items-center px-4 py-2 bg-teal-100 dark:bg-teal-900/50 text-teal-800 dark:text-teal-100 rounded-full text-sm font-medium mb-6 border border-teal-200 dark:border-teal-800">
                            <Zap className="w-4 h-4 mr-2" />
                            Trusted by 500+ Schools Worldwide
                        </div>

                        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                            Welcome to{" "}
                            <span className="text-teal-600 dark:text-teal-300 relative">
                                Scholarify
                                <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-teal-400 to-teal-600 dark:from-teal-300 dark:to-teal-400 rounded-full"></div>
                            </span>
                        </h1>

                        <p className="text-xl md:text-2xl text-gray-700 dark:text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed">
                            The complete school management solution that
                            empowers administrators, teachers, and students to
                            achieve educational excellence.
                        </p>

                        {/* Stats Row */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-3xl mx-auto mb-8">
                            {stats.map((stat, index) => (
                                <div
                                    key={index}
                                    className="text-center bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-all duration-200"
                                >
                                    <div className="flex items-center justify-center text-teal-600 dark:text-teal-300 mb-2">
                                        {stat.icon}
                                    </div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                                        {stat.number}
                                    </div>
                                    <div className="text-sm text-gray-700 dark:text-gray-200">
                                        {stat.label}
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <button
                                onClick={() => scrollToSection("docs")}
                                className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 px-8 py-3 rounded-lg font-medium transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 group border-2 border-gray-300 dark:border-gray-600"
                            >
                                <BookOpen className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
                                Explore Docs
                            </button>
                            <button
                                onClick={() => handleNavigation("/login")}
                                className="bg-teal-600 text-gray-800 hover:bg-teal-700 dark:bg-teal-600 dark:text-white dark:hover:bg-teal-500 px-8 py-3 rounded-lg font-medium transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 group border-2 border-teal-600 dark:border-teal-600"
                            >
                                Get Started
                                <ChevronRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            {/* Key Benefits Section */}
            <section className="py-16 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Why Choose Scholarify?
                        </h2>
                        <p className="text-lg text-gray-700 dark:text-gray-200 max-w-2xl mx-auto">
                            Transform your educational institution with our
                            comprehensive management platform
                        </p>
                    </div>

                    <div className="grid md:grid-cols-3 gap-8">
                        {[
                            {
                                icon: <Zap className="w-10 h-10" />,
                                title: "Lightning Fast",
                                description:
                                    "Experience blazing fast performance with our optimized platform built for educational institutions.",
                                color: "from-yellow-400 to-orange-500",
                            },
                            {
                                icon: <Shield className="w-10 h-10" />,
                                title: "Secure & Compliant",
                                description:
                                    "Enterprise-grade security with full compliance to educational data protection standards.",
                                color: "from-green-400 to-teal-500",
                            },
                            {
                                icon: <Heart className="w-10 h-10" />,
                                title: "User-Friendly",
                                description:
                                    "Intuitive design that makes it easy for teachers, students, and administrators to use.",
                                color: "from-pink-400 to-purple-500",
                            },
                        ].map((benefit, index) => (
                            <div key={index} className="relative group">
                                <div className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 p-8 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group-hover:scale-105">
                                    <div
                                        className={`inline-flex p-4 rounded-full bg-gradient-to-r ${benefit.color} text-white mb-6 shadow-lg`}
                                    >
                                        {benefit.icon}
                                    </div>
                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                                        {benefit.title}
                                    </h3>
                                    <p className="text-gray-700 dark:text-gray-200 leading-relaxed">
                                        {benefit.description}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* About Scholarify Section */}
            <section id="about" className="py-16 bg-gray-50 dark:bg-gray-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            About Scholarify
                        </h2>
                        <p className="text-lg text-gray-700 dark:text-gray-200 max-w-3xl mx-auto leading-relaxed">
                            Scholarify revolutionizes school management by
                            providing an integrated platform that connects
                            administrators, teachers, students, and parents in
                            one seamless ecosystem.
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 gap-12 items-center">
                        <div className="space-y-6">
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                Transforming Education Management
                            </h3>
                            <div className="space-y-4">
                                {[
                                    "Our comprehensive school management system streamlines administrative processes, enhances communication, and provides powerful insights to drive educational success.",
                                    "From student enrollment and class scheduling to grade management and parent communication, Scholarify handles it all with intuitive design and robust functionality.",
                                    "Built with modern technology and designed for scalability, Scholarify grows with your institution and adapts to your unique needs.",
                                ].map((text, index) => (
                                    <div
                                        key={index}
                                        className="flex items-start space-x-3"
                                    >
                                        <CheckCircle className="w-5 h-5 text-teal-600 dark:text-teal-300 mt-0.5 flex-shrink-0" />
                                        <p className="text-gray-700 dark:text-gray-200 leading-relaxed">
                                            {text}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            {features.slice(0, 4).map((feature, index) => (
                                <div
                                    key={index}
                                    className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 dark:border-gray-600 group hover:-translate-y-1"
                                >
                                    <div className="text-teal-600 dark:text-teal-300 mb-3 group-hover:scale-110 transition-transform">
                                        {feature.icon}
                                    </div>
                                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                                        {feature.title}
                                    </h4>
                                    <p className="text-sm text-gray-700 dark:text-gray-200 leading-relaxed">
                                        {feature.description}
                                    </p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Enhanced Features Section */}
            <section id="features" className="py-16 bg-white dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Powerful Features
                        </h2>
                        <p className="text-lg text-gray-700 dark:text-gray-200 max-w-3xl mx-auto leading-relaxed">
                            Everything you need to manage your school
                            efficiently and effectively.
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {features.map((feature, index) => (
                            <div
                                key={index}
                                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 hover:-translate-y-1 group"
                            >
                                <div className="text-teal-600 dark:text-teal-300 mb-4 group-hover:scale-110 transition-transform">
                                    {feature.icon}
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                    {feature.title}
                                </h3>
                                <p className="text-gray-700 dark:text-gray-200 leading-relaxed mb-4">
                                    {feature.description}
                                </p>
                                <div className="space-y-2">
                                    {feature.benefits.map(
                                        (benefit, benefitIndex) => (
                                            <div
                                                key={benefitIndex}
                                                className="flex items-center space-x-2 text-sm"
                                            >
                                                <Star className="w-3 h-3 text-teal-600 dark:text-teal-300 fill-current" />
                                                <span className="text-gray-700 dark:text-gray-200">
                                                    {benefit}
                                                </span>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Enhanced Product Previews Section */}
            <section
                id="previews"
                className="py-16 bg-gray-50 dark:bg-gray-800"
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Product Previews
                        </h2>
                        <p className="text-lg text-gray-700 dark:text-gray-200 leading-relaxed">
                            See Scholarify in action across different platforms
                            and user roles.
                        </p>
                    </div>

                    {/* Enhanced Desktop Dashboard Preview */}
                    <div className="mb-16">
                        <div className="flex items-center justify-center mb-8">
                            <Monitor className="w-8 h-8 text-teal-600 dark:text-teal-300 mr-3" />
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                Admin & Teacher Dashboard
                            </h3>
                        </div>
                        <div className="bg-white dark:bg-gray-700 rounded-lg shadow-lg p-8 border border-gray-200 dark:border-gray-600">
                            {mounted && (
                                <div className="flex justify-center items-center mb-6 space-x-4">
                                    <div className="flex space-x-2">
                                        {dashboardImages.map((image, index) => (
                                            <button
                                                key={index}
                                                onClick={() =>
                                                    setCurrentImageIndex(index)
                                                }
                                                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                                                    currentImageIndex === index
                                                        ? "bg-teal-600 text-gray-800 shadow-md transform scale-105"
                                                        : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500"
                                                }`}
                                            >
                                                {image.title}
                                            </button>
                                        ))}
                                    </div>
                                    <button
                                        onClick={() =>
                                            setIsCarouselPlaying(
                                                !isCarouselPlaying
                                            )
                                        }
                                        className="p-2 rounded-lg bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                                        aria-label={
                                            isCarouselPlaying
                                                ? "Pause carousel"
                                                : "Play carousel"
                                        }
                                    >
                                        {isCarouselPlaying ? (
                                            <Pause className="w-4 h-4" />
                                        ) : (
                                            <Play className="w-4 h-4" />
                                        )}
                                    </button>
                                </div>
                            )}
                            <div className="aspect-video bg-gray-100 dark:bg-gray-600 rounded-lg overflow-hidden shadow-inner">
                                {mounted ? (
                                    <div className="relative w-full h-full">
                                        <Image
                                            src={
                                                dashboardImages[
                                                    currentImageIndex
                                                ].url
                                            }
                                            alt={
                                                dashboardImages[
                                                    currentImageIndex
                                                ].title
                                            }
                                            fill
                                            className="object-cover transition-all duration-500"
                                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                                            priority
                                        />
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent flex items-end">
                                            <div className="p-6 text-white">
                                                <h4 className="text-xl font-semibold mb-2 drop-shadow-lg">
                                                    {
                                                        dashboardImages[
                                                            currentImageIndex
                                                        ].title
                                                    }
                                                </h4>
                                                <p className="text-gray-100 drop-shadow-lg">
                                                    {
                                                        dashboardImages[
                                                            currentImageIndex
                                                        ].description
                                                    }
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="w-full h-full flex items-center justify-center">
                                        <div className="text-gray-500 dark:text-gray-400">
                                            Loading preview...
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Mobile App Preview */}
                    <div>
                        <div className="flex items-center justify-center mb-8">
                            <Smartphone className="w-8 h-8 text-teal-600 dark:text-teal-400 mr-3" />
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                Mobile App Preview
                            </h3>
                        </div>
                        <div className="flex justify-center">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl">
                                {mobileImages.map((image, index) => (
                                    <div
                                        key={index}
                                        className="bg-white dark:bg-gray-700 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-600 hover:shadow-xl transition-all duration-200 hover:-translate-y-1 group"
                                    >
                                        <div className="aspect-[9/16] bg-gray-100 dark:bg-gray-600 rounded-lg overflow-hidden mb-4 shadow-inner group-hover:shadow-lg transition-shadow">
                                            {mounted ? (
                                                <div className="relative w-full h-full">
                                                    <Image
                                                        src={image.url}
                                                        alt={image.title}
                                                        fill
                                                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                                                        sizes="(max-width: 768px) 100vw, 33vw"
                                                    />
                                                </div>
                                            ) : (
                                                <div className="w-full h-full flex items-center justify-center">
                                                    <div className="text-gray-500 dark:text-gray-400">
                                                        Loading...
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                        <h4 className="font-semibold text-gray-900 dark:text-white text-center">
                                            {image.title}
                                        </h4>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Enhanced Documentation Section */}
            <section id="docs" className="py-16 bg-white dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Documentation
                        </h2>
                        <p className="text-lg text-gray-700 dark:text-gray-200 leading-relaxed">
                            Everything you need to get started with Scholarify.
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {[
                            {
                                icon: <BookOpen className="w-8 h-8" />,
                                title: "Getting Started Guide",
                                description:
                                    "Step-by-step instructions to set up and configure Scholarify for your school. Learn about user onboarding, initial configuration, and how to access key features. No API Reference required—everything is managed through the intuitive UI.",
                                link: "Read Guide",
                                route: "/docs/getting-started",
                                color: "teal",
                            },
                            {
                                icon: <Users className="w-8 h-8" />,
                                title: "User Management",
                                description:
                                    "Learn how to manage users, roles, and permissions across your school system.",
                                link: "View Docs",
                                route: "/docs/user-management",
                                color: "blue",
                            },
                            {
                                icon: <Shield className="w-8 h-8" />,
                                title: "Security & Data Protection",
                                description:
                                    "How Scholarify protects your data: encryption, secure authentication, user permissions, and compliance with FERPA, COPPA, and GDPR. Learn how to configure security settings, manage access, and keep your school safe.",
                                link: "Security Guide",
                                route: "/docs/security",
                                color: "green",
                            },
                        ].map((doc, index) => (
                            <div
                                key={index}
                                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 hover:-translate-y-1 group"
                            >
                                <div className="text-teal-600 dark:text-teal-300 mb-4 group-hover:scale-110 transition-transform">
                                    {doc.icon}
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                    {doc.title}
                                </h3>
                                <p className="text-gray-700 dark:text-gray-200 mb-4 leading-relaxed">
                                    {doc.description}
                                </p>
                                <button
                                    onClick={() => handleNavigation(doc.route)}
                                    className="text-teal-600 dark:text-teal-300 hover:text-teal-700 dark:hover:text-teal-200 font-medium flex items-center transition-colors group-hover:translate-x-1"
                                >
                                    {doc.link}
                                    <ChevronRight className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" />
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Enhanced Team Section */}
            <section id="team" className="py-16 bg-gray-50 dark:bg-gray-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Meet the Team Behind Scholarify
                        </h2>
                        <p className="text-lg text-gray-700 dark:text-gray-200 leading-relaxed">
                            Passionate educators and technologists working
                            together to transform education.
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
                        {teamMembers.map((member, index) => (
                            <div
                                key={index}
                                className="bg-white dark:bg-gray-700 rounded-lg shadow-sm p-6 text-center border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200 hover:-translate-y-1 group"
                            >
                                <div className="w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden shadow-md group-hover:shadow-lg transition-shadow">
                                    {mounted ? (
                                        <div className="relative w-full h-full">
                                            <Image
                                                src={member.avatar}
                                                alt={member.name}
                                                fill
                                                className="object-cover group-hover:scale-110 transition-transform duration-300"
                                                sizes="96px"
                                            />
                                        </div>
                                    ) : (
                                        <div className="w-full h-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                            <Users className="w-8 h-8 text-gray-400" />
                                        </div>
                                    )}
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                                    {member.name}
                                </h3>
                                <p className="text-teal-600 dark:text-teal-300 font-medium mb-3 text-sm">
                                    {member.role}
                                </p>
                                <p className="text-gray-700 dark:text-gray-200 text-sm leading-relaxed mb-4">
                                    {member.bio}
                                </p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Enhanced FAQ Section */}
            <section id="faq" className="py-16 bg-white dark:bg-gray-900">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Frequently Asked Questions
                        </h2>
                        <p className="text-lg text-gray-700 dark:text-gray-200 leading-relaxed">
                            Get answers to common questions about Scholarify.
                        </p>
                    </div>

                    <div className="space-y-4">
                        {faqs.map((faq, index) => (
                            <div
                                key={index}
                                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
                            >
                                <button
                                    onClick={() => toggleFAQ(index)}
                                    className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                >
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
                                        {faq.question}
                                    </h3>
                                    <ChevronDown
                                        className={`w-5 h-5 text-gray-600 dark:text-gray-300 transition-transform duration-200 ${
                                            openFAQ === index
                                                ? "rotate-180"
                                                : ""
                                        }`}
                                    />
                                </button>
                                {openFAQ === index && (
                                    <div className="px-6 pb-4">
                                        <p className="text-gray-700 dark:text-gray-200 leading-relaxed">
                                            {faq.answer}
                                        </p>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Final Call-to-Action Section */}
            <section className="py-20 bg-gradient-to-r from-teal-600 via-teal-700 to-teal-800 dark:from-gray-800 dark:via-gray-900 dark:to-black relative overflow-hidden">
                {/* Background effects */}
                <div className="absolute inset-0 bg-black/10 dark:bg-black/30"></div>
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
                <div className="absolute -top-16 -right-16 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-16 -left-16 w-48 h-48 bg-white/10 rounded-full blur-xl"></div>

                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
                    <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                        Ready to Transform Your School?
                    </h2>
                    <p className="text-xl md:text-2xl text-teal-100 dark:text-gray-200 mb-8 leading-relaxed">
                        Join thousands of educators who are already using
                        Scholarify to streamline their operations and improve
                        student outcomes.
                    </p>

                    <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
                        <button
                            onClick={() => handleNavigation("/login")}
                            className="bg-white text-teal-700 text-gray-700 hover:bg-gray-100 dark:bg-gray-100 dark:text-teal-700 dark:hover:bg-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 border-2 border-white dark:border-gray-100"
                        >
                            Start Free Trial
                        </button>
                        <button
                            onClick={() => scrollToSection("docs")}
                            className="border-2 border-white text-white hover:bg-white hover:text-teal-700 dark:border-gray-200 dark:text-gray-200 dark:hover:bg-gray-200 dark:hover:text-gray-900 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
                        >
                            View Documentation
                        </button>
                    </div>

                    {/* Trust indicators */}
                    <div className="flex flex-wrap justify-center items-center gap-8 text-teal-100 dark:text-gray-200">
                        <div className="flex items-center gap-2">
                            <Shield className="w-5 h-5" />
                            <span className="text-sm font-medium">
                                Enterprise Security
                            </span>
                        </div>
                        <div className="flex items-center gap-2">
                            <CheckCircle className="w-5 h-5" />
                            <span className="text-sm font-medium">
                                99.9% Uptime
                            </span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Heart className="w-5 h-5" />
                            <span className="text-sm font-medium">
                                24/7 Support
                            </span>
                        </div>
                    </div>
                </div>
            </section>

            {/* Enhanced Footer */}
            <footer className="bg-gray-900 text-white py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid md:grid-cols-4 gap-8">
                        <div>
                            <div className="mb-4">
                                <button
                                    onClick={handleLogoClick}
                                    className="transition-transform duration-200 hover:scale-105"
                                >
                                    <Logo />
                                </button>
                            </div>
                            <p className="text-gray-400 mb-4 leading-relaxed">
                                Transforming education through innovative school
                                management solutions.
                            </p>
                        </div>

                        <div>
                            <h4 className="font-semibold mb-4 text-white">
                                Product
                            </h4>
                            <ul className="space-y-2 text-gray-400">
                                <li>
                                    <button
                                        onClick={() =>
                                            scrollToSection("features")
                                        }
                                        className="hover:text-white transition-colors"
                                    >
                                        Features
                                    </button>
                                </li>
                                <li>
                                    <button
                                        onClick={() => scrollToSection("docs")}
                                        className="hover:text-white transition-colors"
                                    >
                                        Documentation
                                    </button>
                                </li>
                                <li>
                                    <button
                                        onClick={() =>
                                            handleNavigation("/login")
                                        }
                                        className="hover:text-white transition-colors"
                                    >
                                        Login
                                    </button>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h4 className="font-semibold mb-4 text-white">
                                Company
                            </h4>
                            <ul className="space-y-2 text-gray-400">
                                <li>
                                    <button
                                        onClick={() => scrollToSection("about")}
                                        className="hover:text-white transition-colors"
                                    >
                                        About
                                    </button>
                                </li>
                                <li>
                                    <button
                                        onClick={() => scrollToSection("team")}
                                        className="hover:text-white transition-colors"
                                    >
                                        Team
                                    </button>
                                </li>
                                <li>
                                    <button
                                        onClick={() =>
                                            handleNavigation("/careers")
                                        }
                                        className="hover:text-white transition-colors"
                                    >
                                        Careers
                                    </button>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h4 className="font-semibold mb-4 text-white">
                                Support
                            </h4>
                            <ul className="space-y-2 text-gray-400">
                                <li>
                                    <button
                                        onClick={() =>
                                            handleNavigation("/help-center")
                                        }
                                        className="hover:text-white transition-colors"
                                    >
                                        Help Center
                                    </button>
                                </li>
                                <li>
                                    <button
                                        onClick={() =>
                                            handleNavigation("/contact")
                                        }
                                        className="hover:text-white transition-colors"
                                    >
                                        Contact Us
                                    </button>
                                </li>
                                <li>
                                    <button
                                        onClick={() =>
                                            handleNavigation("/privacy-policy")
                                        }
                                        className="hover:text-white transition-colors"
                                    >
                                        Privacy Policy
                                    </button>
                                </li>
                                <li>
                                    <button
                                        onClick={() =>
                                            handleNavigation(
                                                "/terms-of-service"
                                            )
                                        }
                                        className="hover:text-white transition-colors"
                                    >
                                        Terms of Service
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                        <p>&copy; 2024 Scholarify. All rights reserved.</p>
                    </div>
                </div>
            </footer>

            {/* Back to Top Button */}
            {mounted && showScrollTop && (
                <button
                    onClick={scrollToTop}
                    className="fixed bottom-8 right-8 bg-teal-600 hover:bg-teal-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1 z-30"
                    aria-label="Back to top"
                >
                    <ArrowUp className="w-5 h-5" />
                </button>
            )}
        </div>
    );
}
