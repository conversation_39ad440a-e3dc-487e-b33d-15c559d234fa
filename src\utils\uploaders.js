const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const cloudinary = require('../utils/cloudinary');

/* ------------------------------------------------------------------ */
/*  🗂  1. CSV UPLOADER  — local disk                                 */
/* ------------------------------------------------------------------ */

const csvStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';

    // Check if folder exists, if not create it
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const unique = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, unique + path.extname(file.originalname)); // e.g. 1629999999-123.csv
  },
});

const csvFileFilter = (req, file, cb) => {
  const ok =
    file.mimetype === 'text/csv' ||
    file.mimetype === 'application/vnd.ms-excel' ||
    file.originalname.toLowerCase().endsWith('.csv');

  cb(ok ? null : new Error('Only CSV files are allowed'), ok);
};

const uploadCSV = multer({ storage: csvStorage, fileFilter: csvFileFilter });

/* ------------------------------------------------------------------ */
/*  🖼  2. AVATAR IMAGE UPLOADER  — Cloudinary                        */
/* ------------------------------------------------------------------ */

const avatarStorage = new CloudinaryStorage({
  cloudinary,
  params: async (req) => {
    const studentId = req.params.id || 'unknown';
    return {
      folder: `students/${studentId}/avatar`,
      allowed_formats: ['jpg', 'jpeg', 'png'],
      public_id: 'avatar', // ensures one avatar per student (overwrite)
    };
  },
});

const uploadAvatarImage = multer({ storage: avatarStorage });

/* ------------------------------------------------------------------ */
/* ------------------------------------------------------------------ */
/*  📄 3. GENERIC FILE UPLOADER — Cloudinary                         */
/* ------------------------------------------------------------------ */

const justificationFileStorage = new CloudinaryStorage({
  cloudinary,
  params: async (req, file) => {
    const schoolNameRaw = req.body.schoolName || req.params.schoolName || 'unknown-school';
    const schoolName = schoolNameRaw.replace(/\s+/g, '-').toLowerCase(); // sanitize folder name

    return {
      folder: `${schoolName}/justifications`,
      resource_type: 'auto', // handles images, pdfs, docs, etc.
      public_id: path.parse(file.originalname).name, // keep original name (no extension)
    };
  },
});

const uploadJustificationFile = multer({ storage: justificationFileStorage });

/* ------------------------------------------------------------------ */
/*  🧑‍🦱  3. USER AVATAR IMAGE UPLOADER — Cloudinary                  */
/* ------------------------------------------------------------------ */

const userAvatarStorage = new CloudinaryStorage({
  cloudinary,
  params: async (req) => {
    const userId = req.params.id || 'unknown';
    return {
      folder: `users/${userId}/avatar`,
      allowed_formats: ['jpg', 'jpeg', 'png'],
      public_id: 'avatar',
    };
  },
});

const uploadUserAvatarImage = multer({ storage: userAvatarStorage });

/* ------------------------------------------------------------------ */
/*  🏫  SCHOOL LOGO UPLOADER — Cloudinary                           */
/* ------------------------------------------------------------------ */

const schoolLogoStorage = new CloudinaryStorage({
  cloudinary,
  params: async (req, file) => {
    // We get school_id from req.params.id to store the logo for a specific school
    const schoolId = req.params.id || 'unknown-school';  // Default if school_id is missing
    
    return {
      folder: `schools/${schoolId}/logos`, // Store in school-specific folder e.g. /schools/school-id/logos/
      allowed_formats: ['jpg', 'jpeg', 'png', 'svg'], // Allowing image and vector formats
      public_id: path.parse(file.originalname).name,  // Use original file name, avoid duplicates
    };
  },
});

const uploadSchoolLogo = multer({ storage: schoolLogoStorage });

module.exports = {
  uploadCSV,          // .single('file')
  uploadAvatarImage,  // .single('image')
  uploadJustificationFile,
  uploadUserAvatarImage,
  uploadSchoolLogo,
};
