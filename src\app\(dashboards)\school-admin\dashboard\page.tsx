"use client";

import { Coins, DollarSign, GraduationCap, LayoutDashboard, Users } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import StatsOverview from "@/components/widgets/StatsOverview";
import TopClassesChart from "@/components/utils/TopClassesChart";
import RecentAnnouncements from "@/components/widgets/RecentAnnouncements";
import useAuth from "@/app/hooks/useAuth";
import { useEffect, useState } from "react";
import CircularLoader from "@/components/widgets/CircularLoader";
import { getStudentsBySchool } from "@/app/services/StudentServices";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { getTeachersBySchool } from "@/app/services/TeacherServices";
import { SchoolAdminDashboardSkeleton } from "@/components/skeletons/SchoolAdminDashboardSkeleton";
import { getTopClassesBySchool } from "@/app/services/ClassServices";
import { getRecentAnnouncementsBySchool } from "@/app/services/AnnouncementServices";
import { StudentSchema } from "@/app/models/StudentModel";
import { SchoolSchema } from "@/app/models/SchoolModel";
import { useTranslation } from "@/hooks/useTranslation";

const BASE_URL = "/school-admin";

export default function Page() {
  const { logout, user } = useAuth();
  const { t, tDashboard } = useTranslation();
  const [students, setStudents] = useState<StudentSchema[]>([]);
  const [school, setSchool] = useState<SchoolSchema | null>(null);
  const [teacherCount, setTeacherCount] = useState<number>(0);
  const [loading, setLoading] = useState(true);

  // Navigation avec titre dynamique
  const navigation = {
    icon: LayoutDashboard,
    baseHref: `${BASE_URL}/dashboard`,
    title: school ? `${t('navigation.dashboard')} - ${school.name}` : t('navigation.dashboard')
  };

  // Charger toutes les données nécessaires pour le dashboard
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        if (user && user.school_ids && user.school_ids.length > 0) {
          const schoolId = user.school_ids[0];

          // Récupérer les données en parallèle
          const [schoolData, studentsData, teachersCount] = await Promise.all([
            getSchoolBy_id(schoolId),
            getStudentsBySchool(schoolId),
            getTeachersBySchool(schoolId).then((teachers: any[]) => teachers.length)
          ]);
          setSchool(schoolData);
          setStudents(studentsData);
          setTeacherCount(teachersCount);
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  if (loading) {
    return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <SchoolAdminDashboardSkeleton />
      </SchoolLayout>
    );
  }

  // Si pas d'école associée à l'utilisateur
  if (!school) {
    return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="flex justify-center items-center h-64">
          <p className="text-foreground/60">{t('dashboard.school-admin.pages.dashboard.no_school_associated')}</p>
        </div>
      </SchoolLayout>
    );
  }

  return (
    <SchoolLayout
      navigation={navigation}
      showGoPro={true}
      onLogout={() => logout()}
    >
      <div className="flex flex-col gap-6">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4">
          <StatsOverview
            value={teacherCount.toString()}
            changePercentage={2.35}
            title={tDashboard('school-admin', 'dashboard', 'total_teachers')}
            icon={<Users />}
          />
          <StatsOverview
            value={school?.credit?.toLocaleString() || "0"}
            changePercentage={5.78}
            title={tDashboard('school-admin', 'dashboard', 'current_credit_balance')}
            icon={<Coins />}
          />
          <StatsOverview
            value={students.length.toString()}
            changePercentage={3.48}
            title={tDashboard('school-admin', 'dashboard', 'total_students')}
            icon={<GraduationCap />}
          />
        </div>

        {/* Charts and Recent Announcements */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {/* Top Classes Chart */}
          <TopClassesChart schoolId={school._id} />

          {/* Recent Announcements */}
          <RecentAnnouncements schoolId={school._id} />
        </div>
      </div>
    </SchoolLayout>
  );
}
