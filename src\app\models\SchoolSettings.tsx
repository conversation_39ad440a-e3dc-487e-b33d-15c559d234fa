export interface SchoolSettingsSchema extends Record<string, unknown> {
  _id: string; // MongoDB ObjectId as string
  school_id: string; // ObjectId of the school

  general: {
    language: string;      // e.g., "en"
    timezone: string;      // e.g., "UTC"
    currency: string;      // e.g., "USD", "EUR"
  };

  preferences: {
    enable_notifications: boolean;
    default_grading_scale: 'A-F' | 'Percentage' | 'Pass/Fail';
    grading_system_base: 100 | 20 | 10 | 5;
  };

  credit: {
    credit_limit: number;
    credit_usage_alert_threshold: number; // % between 0–100
  };

  createdAt: string;
  updatedAt: string;
}
