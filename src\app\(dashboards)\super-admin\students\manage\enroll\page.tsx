"use client";

import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout'
import { GraduationCap } from 'lucide-react'
import React from 'react'

function EnrollmentPage() {
    return (
        <div>
            enrollment page
        </div>
    )
}
function Page() {
    return (
        <SuperLayout
            navigation={{
                icon: GraduationCap,
                title: "Student Registration",
                baseHref: "/super-admin/students"
            }}
            showGoPro={true}
            onLogout={() => console.log("Logout")}
        >

            <EnrollmentPage />
        </SuperLayout>
    )
}

export default Page