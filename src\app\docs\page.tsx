"use client";

import { useRouter } from "next/navigation";
import {
    BookO<PERSON>,
    Users,
    Code,
    ArrowRight,
    ArrowLeft,
    Clock,
    CheckCircle,
    Download,
    PlayCircle,
    FileText,
    Lightbulb,
    MessageCircle,
} from "lucide-react";

export default function DocsHomePage() {
    const router = useRouter();

    const mainSections = [
        {
            title: "Getting Started Guide",
            description:
                "Everything you need to set up and configure Scholarify for your school",
            icon: <BookOpen className="w-8 h-8" />,
            href: "/docs/getting-started",
            color: "teal",
            estimatedTime: "15 min read",
            features: [
                "System requirements",
                "Account setup process",
                "Initial configuration",
                "School profile setup",
            ],
        },
        {
            title: "User Management",
            description:
                "Learn how to manage users, assign roles, and configure permissions",
            icon: <Users className="w-8 h-8" />,
            href: "/docs/user-management",
            color: "blue",
            estimatedTime: "20 min read",
            features: [
                "User roles and permissions",
                "Adding new users",
                "Bulk import process",
                "Security best practices",
            ],
        },
        // API Reference removed
    ];

    const quickLinks = [
        {
            title: "System Requirements",
            description: "Check compatibility requirements",
            icon: <CheckCircle className="w-5 h-5" />,
            href: "/docs/getting-started#requirements",
        },
        {
            title: "Video Tutorials",
            description: "Step-by-step video guides",
            icon: <PlayCircle className="w-5 h-5" />,
            href: "#video-tutorials",
        },
        {
            title: "FAQ",
            description: "Frequently asked questions",
            icon: <MessageCircle className="w-5 h-5" />,
            href: "/#faq",
        },
        {
            title: "Release Notes",
            description: "Latest updates and changes",
            icon: <FileText className="w-5 h-5" />,
            href: "#release-notes",
        },
    ];

    const handleNavigation = (href: string) => {
        router.push(href);
    };

    return (
        <div className="p-8">
            {/* Back Button */}
            <div className="mb-6">
                <button
                    onClick={() => router.push("/")}
                    className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-300 transition-colors group"
                >
                    <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
                    <span className="text-sm font-medium">Back to Home</span>
                </button>
            </div>

            {/* Header */}
            <div className="text-center mb-12">
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    Scholarify Documentation
                </h1>
                <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
                    Welcome to the comprehensive documentation for Scholarify.
                    Whether you're just getting started or looking for advanced
                    features, you'll find everything you need to make the most
                    of your school management system.
                </p>
            </div>

            {/* Quick Stats */}
            <div className="grid md:grid-cols-4 gap-6 mb-12">
                <div className="text-center">
                    <div className="text-3xl font-bold text-teal-600 dark:text-teal-400 mb-2">
                        3
                    </div>
                    <div className="text-gray-600 dark:text-gray-400 text-sm">
                        Main Guides
                    </div>
                </div>
                <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                        50+
                    </div>
                    <div className="text-gray-600 dark:text-gray-400 text-sm">
                        API Endpoints
                    </div>
                </div>
                <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                        24/7
                    </div>
                    <div className="text-gray-600 dark:text-gray-400 text-sm">
                        Support
                    </div>
                </div>
                <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                        99.9%
                    </div>
                    <div className="text-gray-600 dark:text-gray-400 text-sm">
                        Uptime
                    </div>
                </div>
            </div>

            {/* Main Documentation Sections */}
            <div className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
                    Main Documentation
                </h2>
                <div className="grid lg:grid-cols-3 gap-8">
                    {mainSections.map((section, index) => (
                        <div
                            key={index}
                            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-all duration-200 hover:-translate-y-1 group cursor-pointer"
                            onClick={() => handleNavigation(section.href)}
                        >
                            <div
                                className={`text-${section.color}-600 dark:text-${section.color}-400 mb-4 group-hover:scale-110 transition-transform`}
                            >
                                {section.icon}
                            </div>

                            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                {section.title}
                            </h3>

                            <p className="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
                                {section.description}
                            </p>

                            <div className="flex items-center space-x-2 mb-4">
                                <Clock className="w-4 h-4 text-gray-400" />
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                    {section.estimatedTime}
                                </span>
                            </div>

                            <div className="space-y-2 mb-6">
                                {section.features.map(
                                    (feature, featureIndex) => (
                                        <div
                                            key={featureIndex}
                                            className="flex items-center space-x-2"
                                        >
                                            <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                                {feature}
                                            </span>
                                        </div>
                                    )
                                )}
                            </div>

                            <div className="flex items-center justify-between">
                                <button
                                    className={`text-${section.color}-600 dark:text-${section.color}-400 hover:text-${section.color}-700 dark:hover:text-${section.color}-300 font-medium flex items-center transition-colors group-hover:translate-x-1`}
                                >
                                    Read Guide
                                    <ArrowRight className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" />
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Quick Links Section */}
            <div className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
                    Quick Links
                </h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {quickLinks.map((link, index) => (
                        <button
                            key={index}
                            onClick={() => handleNavigation(link.href)}
                            className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left group"
                        >
                            <div className="text-teal-600 dark:text-teal-400 mb-3 group-hover:scale-110 transition-transform">
                                {link.icon}
                            </div>
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                {link.title}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {link.description}
                            </p>
                        </button>
                    ))}
                </div>
            </div>

            {/* Getting Help Section */}
            <div className="bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-900/20 dark:to-blue-900/20 border border-teal-200 dark:border-teal-800 rounded-lg p-8">
                <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                        Need Additional Help?
                    </h2>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed max-w-2xl mx-auto">
                        Our documentation covers most use cases, but if you need
                        personalized assistance, our support team is here to
                        help you succeed.
                    </p>
                </div>

                <div className="grid md:grid-cols-3 gap-6">
                    <div className="text-center">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                            <MessageCircle className="w-8 h-8 text-teal-600 dark:text-teal-400 mx-auto mb-3" />
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                Live Chat Support
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                Get instant help with our 24/7 chat support
                            </p>
                            <button className="text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 text-sm font-medium">
                                Start Chat
                            </button>
                        </div>
                    </div>

                    <div className="text-center">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                            <PlayCircle className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-3" />
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                Video Tutorials
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                Watch step-by-step video guides
                            </p>
                            <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium">
                                Watch Videos
                            </button>
                        </div>
                    </div>

                    <div className="text-center">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                            <Download className="w-8 h-8 text-purple-600 dark:text-purple-400 mx-auto mb-3" />
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                Setup Assistance
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                Schedule a free setup consultation
                            </p>
                            <button className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 text-sm font-medium">
                                Book Session
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Tips Section */}
            <div className="mt-12 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                    <Lightbulb className="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5" />
                    <div>
                        <h3 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                            Pro Tips for Success
                        </h3>
                        <ul className="space-y-2 text-sm text-yellow-800 dark:text-yellow-200">
                            <li>
                                • Start with the Getting Started guide to
                                establish a solid foundation
                            </li>
                            <li>
                                • Set up user roles and permissions early to
                                ensure proper access control
                            </li>
                            <li>
                                • Use the bulk import feature for adding
                                multiple users efficiently
                            </li>
                            <li>
                                • Bookmark the API reference if you plan to
                                integrate with other systems
                            </li>
                            <li>
                                • Join our community forum to connect with other
                                Scholarify users
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
}
