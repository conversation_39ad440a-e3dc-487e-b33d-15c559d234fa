"use client";

import { FileCheck2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import React, { Suspense, useEffect, useState } from "react";
import useAuth from "@/app/hooks/useAuth";
import StudentAttendance from "@/components/attendance/StudentAttendance";
import { useRouter, useSearchParams } from "next/navigation";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";

const BASE_URL = "/teacher-dashboard";

const navigation = {
    icon: FileCheck2,
    baseHref: `${BASE_URL}/attendance`,
    title: "Attendance",
};
interface SelectedSchool {
    school_id: string;
    school_name: string;
    access_granted_at: string;
}
export default function Page() {
    const { logout } = useAuth();
    const searchParams = useSearchParams();
    const { user } = useAuth();
    const schoolId = user?.school_ids?.[0] ?? null;
    const classIdRaw = searchParams.get("classId");
    const subjectId = searchParams.get("subjectId");
    const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);

    const router = useRouter();

    useEffect(() => {
        // Get selected school from localStorage
        const storedSchool = localStorage.getItem("teacher_selected_school");
        if (storedSchool) {
            try {
                const school = JSON.parse(storedSchool);
                setSelectedSchool(school);
            } catch (error) {
                console.error("Error parsing stored school:", error);
                router.push("/teacher-dashboard");
            }
        } else {
            router.push("/teacher-dashboard");
        }
    }, [user, router]);
    const handleSchoolChange = () => {
        localStorage.removeItem("teacher_selected_school");
        router.push("/teacher-dashboard");
    };

    return (
        <Suspense>
            <TeacherLayout
                navigation={navigation}
                selectedSchool={selectedSchool ? {
                    _id: selectedSchool.school_id,
                    name: selectedSchool.school_name
                } : null}
                onSchoolChange={handleSchoolChange}
                onLogout={logout}
            >
                {user && (
                    <StudentAttendance
                        schoolId={schoolId as string}
                        classIdRaw={classIdRaw as string}
                        subjectId={subjectId as string}
                    />
                )}
            </TeacherLayout>
        </Suspense>
    );
}
