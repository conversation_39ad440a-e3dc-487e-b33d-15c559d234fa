"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CreditCard, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  ExternalLink,
  Eye,
  Plus
} from 'lucide-react';
import Link from 'next/link';
import { getCreditPurchaseHistory } from '@/app/services/SubscriptionServices';
import { formatCurrency, formatCredits } from '@/app/services/SubscriptionServices';
import { CreditPurchaseSchema } from '@/app/models/SchoolSubscriptionModel';
import { useTranslation } from '@/hooks/useTranslation';

interface RecentTransactionsProps {
  schoolId: string;
  onPurchaseClick: () => void;
}

export default function RecentTransactions({ schoolId, onPurchaseClick }: RecentTransactionsProps) {
  const { t, tDashboard } = useTranslation();
  const [transactions, setTransactions] = useState<CreditPurchaseSchema[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (schoolId) {
      fetchRecentTransactions();
    }
  }, [schoolId]);

  const fetchRecentTransactions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getCreditPurchaseHistory(schoolId, 5, 0);
      setTransactions(response.purchases || []);
    } catch (err: any) {
      console.error('Error fetching recent transactions:', err);
      setError(err.message || tDashboard('school-admin', 'buy_credit', 'error_loading_transactions'));
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'expired':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return tDashboard('school-admin', 'buy_credit', 'completed');
      case 'failed':
        return tDashboard('school-admin', 'buy_credit', 'failed');
      case 'pending':
        return tDashboard('school-admin', 'buy_credit', 'processing');
      case 'expired':
        return tDashboard('school-admin', 'buy_credit', 'cancelled');
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400';
      case 'failed':
        return 'text-red-600 dark:text-red-400';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'expired':
        return 'text-orange-600 dark:text-orange-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return tDashboard('school-admin', 'buy_credit', 'today');
    } else if (diffDays === 2) {
      return tDashboard('school-admin', 'buy_credit', 'yesterday');
    } else if (diffDays <= 7) {
      return t('dashboard.school-admin.pages.buy_credit.days_ago', { days: diffDays - 1 });
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <CreditCard className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
          {tDashboard('school-admin', 'buy_credit', 'recent_transactions')}
        </h3>
        <Link
          href="/school-admin/buy-credit/history"
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center"
        >
          <Eye className="h-4 w-4 mr-1" />
          {tDashboard('school-admin', 'buy_credit', 'view_all')}
        </Link>
      </div>

      {loading ? (
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                  <div>
                    <div className="w-24 h-4 bg-gray-300 dark:bg-gray-600 rounded mb-1"></div>
                    <div className="w-16 h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="w-16 h-4 bg-gray-300 dark:bg-gray-600 rounded mb-1"></div>
                  <div className="w-20 h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-4">
          <XCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          <button
            onClick={fetchRecentTransactions}
            className="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
          >
            {tDashboard('school-admin', 'buy_credit', 'retry')}
          </button>
        </div>
      ) : transactions.length === 0 ? (
        <div className="text-center py-8">
          <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400 mb-4">{tDashboard('school-admin', 'buy_credit', 'no_recent_transactions')}</p>
          <button
            onClick={onPurchaseClick}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            {tDashboard('school-admin', 'buy_credit', 'buy_credits_action')}
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {transactions.map((transaction, index) => (
            <motion.div
              key={transaction._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {getStatusIcon(transaction.payment_status)}
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {tDashboard('school-admin', 'buy_credit', 'credit_purchase')}
                  </p>
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <span>{formatDate(transaction.purchase_date)}</span>
                    <span>•</span>
                    <span className={getStatusColor(transaction.payment_status)}>
                      {getStatusText(transaction.payment_status)}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <p className={`font-semibold ${
                  transaction.payment_status === 'completed' 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-gray-900 dark:text-gray-100'
                }`}>
                  {transaction.payment_status === 'completed' ? '+' : ''}{formatCredits(transaction.credits_purchased)}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {formatCurrency(transaction.total_amount)}
                </p>
                {transaction.payment_status === 'pending' && transaction.payment_gateway_response?.link && (
                  <a
                    href={transaction.payment_gateway_response.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center justify-end mt-1"
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    {tDashboard('school-admin', 'buy_credit', 'finalize')}
                  </a>
                )}
              </div>
            </motion.div>
          ))}
          
          {transactions.length >= 5 && (
            <div className="text-center pt-2">
              <Link
                href="/school-admin/buy-credit/history"
                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center justify-center"
              >
                <Eye className="h-4 w-4 mr-1" />
                {tDashboard('school-admin', 'buy_credit', 'view_complete_history')}
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
