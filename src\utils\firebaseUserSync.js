/**
 * Utilitaires pour synchroniser les utilisateurs entre MongoDB et Firebase
 * Nettoie les utilisateurs orphelins et maintient la cohérence
 */

const admin = require('./firebase');
const User = require('../models/User');

/**
 * Supprime un utilisateur de Firebase par email ou UID
 */
async function deleteFirebaseUser(identifier, isUID = false) {
  try {
    let firebaseUser;
    
    if (isUID) {
      // Supprimer directement par UID
      await admin.auth().deleteUser(identifier);
      console.log(`✅ Utilisateur Firebase supprimé par UID: ${identifier}`);
      return { success: true, uid: identifier };
    } else {
      // Trouver par email puis supprimer
      firebaseUser = await admin.auth().getUserByEmail(identifier);
      await admin.auth().deleteUser(firebaseUser.uid);
      console.log(`✅ Utilisateur Firebase supprimé: ${identifier} (UID: ${firebaseUser.uid})`);
      return { success: true, email: identifier, uid: firebaseUser.uid };
    }
  } catch (error) {
    if (error.code === 'auth/user-not-found') {
      console.log(`⚠️ Utilisateur Firebase non trouvé: ${identifier}`);
      return { success: false, error: 'User not found', identifier };
    } else {
      console.error(`❌ Erreur suppression Firebase ${identifier}:`, error.message);
      return { success: false, error: error.message, identifier };
    }
  }
}

/**
 * Supprime un utilisateur de MongoDB et Firebase
 */
async function deleteUserCompletely(userIdentifier, identifierType = 'email') {
  const results = {
    mongodb: { success: false },
    firebase: { success: false }
  };

  try {
    // Construire la requête MongoDB selon le type d'identifiant
    let mongoQuery = {};
    switch (identifierType) {
      case 'email':
        mongoQuery = { email: userIdentifier };
        break;
      case 'user_id':
        mongoQuery = { user_id: userIdentifier };
        break;
      case '_id':
        mongoQuery = { _id: userIdentifier };
        break;
      default:
        throw new Error('Type d\'identifiant non supporté');
    }

    // Récupérer l'utilisateur avant suppression pour avoir ses infos
    const user = await User.findOne(mongoQuery);
    
    if (user) {
      // Supprimer de MongoDB
      const mongoResult = await User.deleteOne(mongoQuery);
      results.mongodb = {
        success: mongoResult.deletedCount > 0,
        deletedCount: mongoResult.deletedCount,
        user: {
          email: user.email,
          user_id: user.user_id,
          name: user.name,
          firebaseUid: user.firebaseUid
        }
      };

      // Supprimer de Firebase
      if (user.firebaseUid) {
        results.firebase = await deleteFirebaseUser(user.firebaseUid, true);
      } else if (user.email) {
        results.firebase = await deleteFirebaseUser(user.email, false);
      }
    } else {
      console.log(`⚠️ Utilisateur non trouvé dans MongoDB: ${userIdentifier}`);
      
      // Essayer quand même de supprimer de Firebase si c'est un email
      if (identifierType === 'email') {
        results.firebase = await deleteFirebaseUser(userIdentifier, false);
      }
    }

    return results;

  } catch (error) {
    console.error('❌ Erreur lors de la suppression complète:', error);
    return {
      mongodb: { success: false, error: error.message },
      firebase: { success: false, error: error.message }
    };
  }
}

/**
 * Trouve et nettoie les utilisateurs orphelins
 */
async function findAndCleanOrphanUsers() {
  try {
    console.log('🔍 Recherche d\'utilisateurs orphelins...\n');

    // Récupérer tous les utilisateurs MongoDB avec email
    const mongoUsers = await User.find({ email: { $exists: true, $ne: null } });
    console.log(`📊 Utilisateurs MongoDB trouvés: ${mongoUsers.length}`);

    const orphans = {
      mongoOnly: [], // Dans MongoDB mais pas Firebase
      firebaseOnly: [], // Dans Firebase mais pas MongoDB
      missingUID: [] // Dans MongoDB sans firebaseUid
    };

    // Vérifier chaque utilisateur MongoDB dans Firebase
    for (const mongoUser of mongoUsers) {
      try {
        const firebaseUser = await admin.auth().getUserByEmail(mongoUser.email);
        
        // Utilisateur existe dans les deux
        if (!mongoUser.firebaseUid) {
          // Mais le firebaseUid n'est pas stocké dans MongoDB
          orphans.missingUID.push({
            mongo: mongoUser,
            firebase: firebaseUser
          });
        }
      } catch (firebaseError) {
        if (firebaseError.code === 'auth/user-not-found') {
          // Utilisateur dans MongoDB mais pas Firebase
          orphans.mongoOnly.push(mongoUser);
        }
      }
    }

    // Rapport
    console.log('\n📋 RAPPORT DES ORPHELINS:');
    console.log(`• MongoDB seulement: ${orphans.mongoOnly.length}`);
    console.log(`• Firebase UID manquant: ${orphans.missingUID.length}`);

    if (orphans.mongoOnly.length > 0) {
      console.log('\n📊 Utilisateurs MongoDB sans Firebase:');
      orphans.mongoOnly.forEach(user => {
        console.log(`   • ${user.email} (${user.name}) - ${user.role}`);
      });
    }

    if (orphans.missingUID.length > 0) {
      console.log('\n📊 Utilisateurs avec UID Firebase manquant:');
      orphans.missingUID.forEach(item => {
        console.log(`   • ${item.mongo.email} - Firebase UID: ${item.firebase.uid}`);
      });
    }

    return orphans;

  } catch (error) {
    console.error('❌ Erreur lors de la recherche d\'orphelins:', error);
    throw error;
  }
}

/**
 * Répare les firebaseUid manquants
 */
async function repairMissingFirebaseUIDs(orphans) {
  console.log('\n🔧 Réparation des firebaseUid manquants...');
  
  let repaired = 0;
  for (const item of orphans.missingUID) {
    try {
      await User.updateOne(
        { _id: item.mongo._id },
        { firebaseUid: item.firebase.uid }
      );
      console.log(`✅ UID réparé pour ${item.mongo.email}: ${item.firebase.uid}`);
      repaired++;
    } catch (error) {
      console.error(`❌ Erreur réparation ${item.mongo.email}:`, error.message);
    }
  }
  
  console.log(`🎉 ${repaired} firebaseUid réparés`);
  return repaired;
}

module.exports = {
  deleteFirebaseUser,
  deleteUserCompletely,
  findAndCleanOrphanUsers,
  repairMissingFirebaseUIDs
};
