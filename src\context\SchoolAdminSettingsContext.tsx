"use client";

import React, {
    createContext,
    useContext,
    useState,
    useEffect,
    ReactNode,
} from "react";
// Ensure these paths match your project's alias configuration
import { getUserById, updateUser, uploadUserAvatar } from "@/app/services/UserServices";
import { getSchoolSettings, upsertSchoolSettings } from "@/app/services/SchoolSettingServices";
import useAuth from "@/app/hooks/useAuth";
import { UserSchema } from "@/app/models/UserModel";
import { useTranslation } from "@/hooks/useTranslation";

// Define the shape of the general settings form for school admin
interface GeneralSettingsForm {
    language: string;
    timezone: string;
    currency: string;
}

// Define the shape of the preferences form for school admin
interface PreferencesForm {
    enable_notifications: boolean;
    default_grading_scale: "A-F" | "Percentage" | "Pass/Fail";
    grading_system_base: 100 | 20 | 10 | 5;
}

// Define the shape of the credit form for school admin
interface CreditForm {
    credit_limit: number;
    credit_usage_alert_threshold: number;
}

// Define the shape of the profile settings form (reused from Super Admin context)
interface ProfileSettingsForm {
    name: string;
    phone: string;
    address: string;
    avatar: string;
}

// Define the type for the context value
interface SchoolAdminSettingsContextType {
    user: UserSchema | undefined;
    isLoading: boolean;
    profileForm: ProfileSettingsForm;
    setProfileForm: React.Dispatch<React.SetStateAction<ProfileSettingsForm>>;
    newAvatarFile: File | null;
    setNewAvatarFile: React.Dispatch<React.SetStateAction<File | null>>;
    avatarPreviewUrl: string | null;
    setAvatarPreviewUrl: React.Dispatch<React.SetStateAction<string | null>>;
    generalForm: GeneralSettingsForm;
    setGeneralForm: React.Dispatch<React.SetStateAction<GeneralSettingsForm>>;
    preferencesForm: PreferencesForm;
    setPreferencesForm: React.Dispatch<React.SetStateAction<PreferencesForm>>;
    creditForm: CreditForm;
    setCreditForm: React.Dispatch<React.SetStateAction<CreditForm>>;
    handleAvatarChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleProfileSubmit: () => Promise<void>;
    handleGeneralSubmit: () => Promise<void>;
    handlePreferencesSubmit: () => Promise<void>;
    handleCreditSubmit: () => Promise<void>;
    notificationMessage: string;
    notificationType: "success" | "error";
    isNotificationCard: boolean;
    setNotification: (message: string, type: "success" | "error") => void;
    setIsNotificationCard: React.Dispatch<React.SetStateAction<boolean>>;
    activeTab: "profile" | "general" | "preferences" | "credit";
    setActiveTab: React.Dispatch<React.SetStateAction<"profile" | "general" | "preferences" | "credit">>;
}

// Create the context
const SchoolAdminSettingsContext = createContext<
    SchoolAdminSettingsContextType | undefined
>(undefined);

// Define the props for the SchoolAdminSettingsProvider
interface SchoolAdminSettingsProviderProps {
    children: ReactNode;
}

export const SchoolAdminSettingsProvider: React.FC<
    SchoolAdminSettingsProviderProps
> = ({ children }) => {
    const { user: authUser } = useAuth();
    const { t } = useTranslation();

    const [user, setUser] = useState<UserSchema | undefined>(undefined);
    const [isLoading, setIsLoading] = useState(true);

    const [profileForm, setProfileForm] = useState<ProfileSettingsForm>({
        name: "",
        phone: "",
        address: "",
        avatar: "",
    });
    const [newAvatarFile, setNewAvatarFile] = useState<File | null>(null);
    const [avatarPreviewUrl, setAvatarPreviewUrl] = useState<string | null>(null);

    const [generalForm, setGeneralForm] = useState<GeneralSettingsForm>({
        language: "en",
        timezone: "UTC",
        currency: "USD",
    });

    const [preferencesForm, setPreferencesForm] = useState<PreferencesForm>({
        enable_notifications: true,
        default_grading_scale: "A-F",
        grading_system_base: 100,
    });

    const [creditForm, setCreditForm] = useState<CreditForm>({
        credit_limit: 0,
        credit_usage_alert_threshold: 80,
    });

    const [activeTab, setActiveTab] = useState<"profile" | "general" | "preferences" | "credit">("profile");

    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<
        "success" | "error"
    >("success");

    // Function to set notification messages
    const setNotification = (message: string, type: "success" | "error") => {
        setNotificationMessage(message);
        setNotificationType(type);
        setIsNotificationCard(true);
    };

    // Fetch user first
    useEffect(() => {
        const fetchUser = async () => {
            if (!authUser?.user_id) {
                setIsLoading(false);
                return;
            }

            try {
                setIsLoading(true);
                const userProfile = await getUserById(authUser.user_id);
                setUser(userProfile);
                setProfileForm({
                    name: userProfile.name || "",
                    phone: userProfile.phone || "",
                    address: userProfile.address || "",
                    avatar:
                        userProfile.avatar ||
                        "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg",
                });
                setAvatarPreviewUrl(
                    userProfile.avatar ||
                    "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg"
                );
            } catch (error) {
                console.error("Failed to fetch user:", error);
                setNotification("Failed to load user profile.", "error");
            } finally {
                setIsLoading(false);
            }
        };

        fetchUser();
    }, [authUser]);

    // Fetch school settings after user is loaded
    useEffect(() => {
        const fetchSettings = async () => {
            const schoolId = user?.school_ids?.[0] || user?.school_id;
            if (!schoolId) {
                // console.warn("School ID not available, cannot fetch school settings.");
                return;
            }

            try {
                const schoolSettings = await getSchoolSettings(schoolId as string);
                if (schoolSettings) {
                    setGeneralForm(schoolSettings.general || {
                        language: "en",
                        timezone: "UTC",
                        currency: "USD",
                    });
                    setPreferencesForm(schoolSettings.preferences || {
                        enable_notifications: true,
                        default_grading_scale: "A-F",
                        grading_system_base: 100,
                    });
                    setCreditForm(schoolSettings.credit || {
                        credit_limit: 0,
                        credit_usage_alert_threshold: 80,
                    });
                }
            } catch (error) {
                console.error("Failed to fetch school settings:", error);
                setNotification("Failed to load school settings.", "error");
            }
        };

        if (user) {
            fetchSettings();
        }
    }, [user]);

    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            if (avatarPreviewUrl?.startsWith("blob:"))
                URL.revokeObjectURL(avatarPreviewUrl);
            setNewAvatarFile(file);
            setAvatarPreviewUrl(URL.createObjectURL(file));
        }
    };

    const handleProfileSubmit = async () => {
        if (!user?.user_id) {
            setNotification("User not loaded. Cannot update profile.", "error");
            return;
        }

        try {
            let avatarToUpdate = profileForm.avatar;
            if (newAvatarFile) {
                const uploadResult = await uploadUserAvatar(user.user_id, newAvatarFile);
                avatarToUpdate = uploadResult.avatarUrl;
                setAvatarPreviewUrl(uploadResult.avatarUrl);
                setNewAvatarFile(null); // Clear the file after upload
            }

            await updateUser(user.user_id, {
                name: profileForm.name,
                user_id: user.user_id,
                role: user.role,
                phone: profileForm.phone,
                address: profileForm.address,
                avatar: avatarToUpdate,
            });

            setUser((prevUser) => ({
                ...prevUser!,
                name: profileForm.name,
                phone: profileForm.phone,
                address: profileForm.address,
                avatar: avatarToUpdate,
            }));

            setNotification(t("messages.success.profile_updated"), "success");
        } catch (error) {
            console.error("Failed to update profile:", error);
            setNotification(t("messages.error.profile_update_failed"), "error");
        }
    };

    const handleGeneralSubmit = async () => {
        const schoolId = user?.school_ids?.[0] || user?.school_id;
        if (!schoolId) {
            setNotification("School ID is not available. Cannot update general settings.", "error");
            return;
        }

        try {
            await upsertSchoolSettings({
                school_id: schoolId as string,
                general: generalForm,
            });
            setNotification("General settings updated", "success");
        } catch (error) {
            console.error("Failed to update general settings:", error);
            setNotification("Failed to update general settings", "error");
        }
    };

    const handlePreferencesSubmit = async () => {
        const schoolId = user?.school_ids?.[0] || user?.school_id;
        if (!schoolId) {
            setNotification("School ID is not available. Cannot update preferences.", "error");
            return;
        }

        try {
            await upsertSchoolSettings({
                school_id: schoolId as string,
                preferences: preferencesForm,
            });
            setNotification("Preferences updated", "success");
        } catch (error) {
            console.error("Failed to update preferences:", error);
            setNotification("Failed to update preferences", "error");
        }
    };

    const handleCreditSubmit = async () => {
        const schoolId = user?.school_ids?.[0] || user?.school_id;
        if (!schoolId) {
            setNotification("School ID is not available. Cannot update credit settings.", "error");
            return;
        }

        try {
            await upsertSchoolSettings({
                school_id: schoolId as string,
                credit: creditForm,
            });
            setNotification("Credit settings updated", "success");
        } catch (error) {
            console.error("Failed to update credit settings:", error);
            setNotification("Failed to update credit settings", "error");
        }
    };

    const contextValue: SchoolAdminSettingsContextType = {
        user,
        isLoading,
        profileForm,
        setProfileForm,
        newAvatarFile,
        setNewAvatarFile,
        avatarPreviewUrl,
        setAvatarPreviewUrl,
        generalForm,
        setGeneralForm,
        preferencesForm,
        setPreferencesForm,
        creditForm,
        setCreditForm,
        handleAvatarChange,
        handleProfileSubmit,
        handleGeneralSubmit,
        handlePreferencesSubmit,
        handleCreditSubmit,
        notificationMessage,
        notificationType,
        isNotificationCard,
        setNotification,
        setIsNotificationCard,
        activeTab,
        setActiveTab,
    };

    return (
        <SchoolAdminSettingsContext.Provider value={contextValue}>
            {children}
        </SchoolAdminSettingsContext.Provider>
    );
};

// Custom hook to use the SchoolAdminSettingsContext
export const useSchoolAdminSettings = () => {
    const context = useContext(SchoolAdminSettingsContext);
    if (context === undefined) {
        throw new Error("useSchoolAdminSettings must be used within a SchoolAdminSettingsProvider");
    }
    return context;
};
