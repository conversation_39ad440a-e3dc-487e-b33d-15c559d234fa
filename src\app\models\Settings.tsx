export interface SettingsSchema extends Record<string, unknown> {
  _id: string; // MongoDB ObjectId

  general: {
    platform_name: string;
    support_email: string;
    default_language: string;
    maintenance_mode: boolean;
    maintenance_message?: string;
  };

  credit: {
    resell_price_per_credit: number;
    buy_price_per_credit: number;
  };

  createdAt?: string;
  updatedAt?: string;
}

// For creating new settings
export interface SettingsCreateSchema extends Record<string, unknown> {
  general: {
    platform_name: string;
    support_email: string;
    default_language?: string;
    maintenance_mode?: boolean;
    maintenance_message?: string;
  };

  credit: {
    resell_price_per_credit: number;
    buy_price_per_credit: number;
  };
}

// For updating settings
export interface SettingsUpdateSchema extends Partial<SettingsCreateSchema> {
  _id: string;
}

// For deleting settings (if needed)
export interface SettingsDeleteSchema extends Record<string, unknown> {
  _id: string;
}
