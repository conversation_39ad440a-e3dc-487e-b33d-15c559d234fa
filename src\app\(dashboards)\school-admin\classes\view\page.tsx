"use client";

import { Presentation } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense} from "react";
import ClassDetailView from "@/components/Dashboard/ReusableComponents/ClassViewComponent";
import useAuth from "@/app/hooks/useAuth";
import { useTranslation } from "@/hooks/useTranslation";


const BASE_URL = "/school-admin";



export default function Page() {
  const { logout } = useAuth();
  const { user } = useAuth();
  const { t, tDashboard } = useTranslation();

  const navigation = {
    icon: Presentation,
    baseHref: `${BASE_URL}/classes`,
    title: tDashboard('school-admin', 'classes', 'title')
  };

  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        {/* You might want to pass schoolId from somewhere here */}
        {user && <ClassDetailView user={user} />}
      </SchoolLayout>
    </Suspense>
  );
}
