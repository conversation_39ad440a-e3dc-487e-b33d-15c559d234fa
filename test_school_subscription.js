/**
 * Script de test pour vérifier la création automatique de souscription
 * lors de la création d'une école avec 50 crédits gratuits
 */

const mongoose = require('mongoose');
const School = require('./src/models/School');
const SchoolSubscription = require('./src/models/SchoolSubscription');
require('dotenv').config();

async function testSchoolCreationWithSubscription() {
  try {
    console.log('🧪 Test de création automatique de souscription...\n');
    
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie\n');

    // Créer une école de test
    const testSchoolData = {
      school_id: `TEST_${Date.now()}`,
      name: `École Test ${Date.now()}`,
      email: `test${Date.now()}@example.com`,
      address: 'Adresse de test',
      principal_name: 'Directeur Test',
      established_year: new Date('2024-01-01'),
      description: 'École créée pour tester la création automatique de souscription'
    };

    console.log('🏫 Création de l\'école de test...');
    console.log(`   Nom: ${testSchoolData.name}`);
    console.log(`   ID: ${testSchoolData.school_id}`);

    // Créer l'école (cela devrait déclencher le hook post-save)
    const newSchool = new School(testSchoolData);
    await newSchool.save();

    console.log('✅ École créée avec succès !');
    console.log(`   MongoDB ID: ${newSchool._id}\n`);

    // Attendre un peu pour que le hook post-save se termine
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Vérifier si la souscription a été créée automatiquement
    console.log('🔍 Vérification de la souscription automatique...');
    const subscription = await SchoolSubscription.findOne({ school_id: newSchool._id });

    if (subscription) {
      console.log('✅ Souscription trouvée !');
      console.log(`   ID Souscription: ${subscription._id}`);
      console.log(`   Plan: ${subscription.plan_type}`);
      console.log(`   Statut: ${subscription.status}`);
      console.log(`   Crédits balance: ${subscription.credits_balance}`);
      console.log(`   Crédits achetés: ${subscription.credits_purchased}`);
      console.log(`   Crédits utilisés: ${subscription.credits_used}`);
      console.log(`   Fonctionnalités: ${subscription.features.join(', ')}`);

      // Vérifier que les crédits sont bien à 50
      if (subscription.credits_balance === 50) {
        console.log('\n🎉 TEST RÉUSSI ! L\'école a bien reçu 50 crédits gratuits !');
      } else {
        console.log(`\n❌ TEST ÉCHOUÉ ! Crédits attendus: 50, reçus: ${subscription.credits_balance}`);
      }
    } else {
      console.log('❌ Aucune souscription trouvée ! Le hook post-save n\'a pas fonctionné.');
    }

    // Nettoyer les données de test
    console.log('\n🧹 Nettoyage des données de test...');
    if (subscription) {
      await SchoolSubscription.deleteOne({ _id: subscription._id });
      console.log('✅ Souscription de test supprimée');
    }
    await School.deleteOne({ _id: newSchool._id });
    console.log('✅ École de test supprimée');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Connexion MongoDB fermée');
    console.log('🏁 Test terminé');
  }
}

// Exécuter le test
if (require.main === module) {
  testSchoolCreationWithSubscription()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}

module.exports = { testSchoolCreationWithSubscription };
