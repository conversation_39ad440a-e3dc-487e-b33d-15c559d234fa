'use client';

import React from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { useNotifications } from '@/hooks/useNotifications';
import NotificationContainer from '../NotificationContainer';
import LanguageSelector from '../LanguageSelector';

const TranslationExample: React.FC = () => {
  const { t, tDashboard, currentLocale, tDefault } = useTranslation();
  const { notifications, showSuccess, showError, showWarning, showInfo, removeNotification } = useNotifications();

  const handleTestNotifications = () => {
    showSuccess('messages.success.saved');
    setTimeout(() => showError('messages.error.network'), 1000);
    setTimeout(() => showWarning('messages.confirmation.delete'), 2000);
    setTimeout(() => showInfo('language.language_changed'), 3000);
  };

  const handleTestDashboardTranslations = () => {
    // Exemples de traductions pour différents dashboards
    const superAdminTitle = tDashboard('super-admin', 'dashboard', 'title');
    const schoolAdminTitle = tDashboard('school-admin', 'dashboard', 'title');
    const teacherDashboardTitle = tDashboard('teacher-dashboard', 'dashboard', 'title');

    showInfo('common.success', { 
      message: `${superAdminTitle} | ${schoolAdminTitle} | ${teacherDashboardTitle}` 
    });
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('common.example_title', { title: 'Système de Traduction' })}
          </h1>
          <LanguageSelector variant="dropdown" />
        </div>

        <div className="space-y-6">
          {/* Section Navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h2 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
              {tDefault('navigation.title', 'Navigation')}
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <span className="px-3 py-2 bg-blue-100 dark:bg-blue-900 rounded text-sm">
                {t('navigation.dashboard')}
              </span>
              <span className="px-3 py-2 bg-blue-100 dark:bg-blue-900 rounded text-sm">
                {t('navigation.schools')}
              </span>
              <span className="px-3 py-2 bg-blue-100 dark:bg-blue-900 rounded text-sm">
                {t('navigation.students')}
              </span>
              <span className="px-3 py-2 bg-blue-100 dark:bg-blue-900 rounded text-sm">
                {t('navigation.teachers')}
              </span>
            </div>
          </div>

          {/* Section Dashboard Translations */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h2 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
              Traductions Dashboard
            </h2>
            <div className="space-y-2">
              <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded">
                <strong>Super Admin:</strong> {tDashboard('super-admin', 'dashboard', 'title')}
              </div>
              <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded">
                <strong>School Admin:</strong> {tDashboard('school-admin', 'dashboard', 'title')}
              </div>
              <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded">
                <strong>Teacher:</strong> {tDashboard('teacher-dashboard', 'dashboard', 'title')}
              </div>
            </div>
          </div>

          {/* Section Common Elements */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h2 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
              Éléments Communs
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                {t('common.save')}
              </button>
              <button className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                {t('common.cancel')}
              </button>
              <button className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                {t('common.delete')}
              </button>
              <button className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                {t('common.add')}
              </button>
              <button className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors">
                {t('common.edit')}
              </button>
              <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                {t('common.view')}
              </button>
            </div>
          </div>

          {/* Section Test Buttons */}
          <div>
            <h2 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
              Tests
            </h2>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={handleTestNotifications}
                className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors"
              >
                Tester les Notifications
              </button>
              <button
                onClick={handleTestDashboardTranslations}
                className="px-4 py-2 bg-teal-600 text-white rounded hover:bg-teal-700 transition-colors"
              >
                Tester les Traductions Dashboard
              </button>
            </div>
          </div>

          {/* Section Current Language Info */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded">
            <h3 className="font-semibold mb-2 text-gray-800 dark:text-gray-200">
              Informations sur la langue actuelle
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Code:</strong> {currentLocale} <br />
              <strong>Nom:</strong> {t(`language.${currentLocale === 'fr' ? 'french' : 'english'}`)}
            </p>
          </div>
        </div>
      </div>

      {/* Notification Container */}
      <NotificationContainer
        notifications={notifications}
        onRemove={removeNotification}
        position="top-right"
      />
    </div>
  );
};

export default TranslationExample;
