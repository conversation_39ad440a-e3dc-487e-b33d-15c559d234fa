/**
 * Script de test pour la fonction registerUser avec messages de bienvenue
 * Teste l'enregistrement d'utilisateurs avec envoi automatique d'emails et SMS
 */

const mongoose = require('mongoose');
const User = require('./src/models/User');
const School = require('./src/models/School');
require('dotenv').config();

// Simuler une requête HTTP
function createMockRequest(userData) {
  return {
    body: userData,
    user: { id: 'test-admin-id' } // Simuler un admin qui crée l'utilisateur
  };
}

function createMockResponse() {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
}

// Données de test pour différents rôles
const testUsers = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'super',
    phone: '+237123456789',
    address: '123 Rue Test, Douala'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPassword456!',
    role: 'admin',
    phone: '+237987654321',
    address: '456 Avenue Test, Yaoundé',
    school_ids: [] // Sera rempli avec un vrai ID d'école
  },
  {
    name: 'Pierre Durand',
    email: '<EMAIL>',
    password: 'TestPassword789!',
    role: 'teacher',
    phone: '+237555666777',
    address: '789 Boulevard Test, Bafoussam',
    school_ids: [] // Sera rempli avec un vrai ID d'école
  },
  {
    name: 'Sophie Bernard',
    email: '<EMAIL>',
    password: 'TestPassword101!',
    role: 'parent',
    phone: null, // Test sans téléphone
    address: '101 Place Test, Garoua',
    school_ids: [] // Sera rempli avec un vrai ID d'école
  }
];

async function testRegisterUserWithWelcome() {
  try {
    console.log('🧪 Test de registerUser avec messages de bienvenue\n');
    
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie\n');

    // Créer une école de test pour les utilisateurs qui en ont besoin
    const testSchool = new School({
      school_id: `TEST_SCHOOL_${Date.now()}`,
      name: `École Test ${Date.now()}`,
      email: `test.school${Date.now()}@example.com`,
      address: 'Adresse École Test',
      principal_name: 'Directeur Test',
      established_year: new Date('2024-01-01'),
      description: 'École créée pour tester registerUser'
    });

    await testSchool.save();
    console.log(`🏫 École de test créée: ${testSchool.name} (ID: ${testSchool._id})\n`);

    // Ajouter l'ID de l'école aux utilisateurs qui en ont besoin
    testUsers.forEach(user => {
      if (user.role !== 'super' && user.school_ids !== undefined) {
        user.school_ids = [testSchool._id.toString()];
      }
    });

    const results = [];

    // Importer le contrôleur
    const userController = require('./src/controllers/userController');

    for (const userData of testUsers) {
      console.log(`\n🔄 Test d'enregistrement: ${userData.name} (${userData.role})`);
      console.log(`   Email: ${userData.email}`);
      console.log(`   Téléphone: ${userData.phone || 'Non fourni'}`);
      console.log(`   École: ${userData.school_ids && userData.school_ids.length > 0 ? testSchool.name : 'Aucune'}`);

      try {
        // Créer les mocks de requête et réponse
        const req = createMockRequest(userData);
        const res = createMockResponse();

        // Appeler la fonction registerUser
        await userController.registerUser(req, res);

        // Vérifier la réponse
        if (res.status.mock.calls.length > 0) {
          const statusCode = res.status.mock.calls[0][0];
          console.log(`   📊 Statut: ${statusCode}`);

          if (res.json.mock.calls.length > 0) {
            const responseData = res.json.mock.calls[0][0];
            console.log(`   📋 Message: ${responseData.message}`);

            if (responseData.user) {
              console.log(`   👤 Utilisateur créé: ${responseData.user.name}`);
              console.log(`   🆔 User ID: ${responseData.user.user_id}`);
              
              if (responseData.welcomeMessages) {
                console.log(`   📧 Email envoyé: ${responseData.welcomeMessages.email ? '✅' : '❌'}`);
                console.log(`   📱 SMS envoyé: ${responseData.welcomeMessages.sms ? '✅' : '❌'}`);
              }

              results.push({
                user: userData,
                success: true,
                statusCode: statusCode,
                userId: responseData.user.user_id,
                welcomeMessages: responseData.welcomeMessages
              });

              console.log('   ✅ Test réussi');
            } else {
              console.log('   ❌ Aucun utilisateur dans la réponse');
              results.push({
                user: userData,
                success: false,
                error: 'No user in response'
              });
            }
          }
        }

      } catch (error) {
        console.error(`   ❌ Erreur lors du test:`, error.message);
        results.push({
          user: userData,
          success: false,
          error: error.message
        });
      }

      // Attendre un peu entre les tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Résumé des résultats
    console.log('\n📊 RÉSUMÉ DES TESTS');
    console.log('='.repeat(60));
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`✅ Enregistrements réussis: ${successful}/${results.length}`);
    console.log(`❌ Enregistrements échoués: ${failed}/${results.length}`);

    if (successful > 0) {
      console.log('\n✅ SUCCÈS:');
      results.filter(r => r.success).forEach(r => {
        console.log(`   • ${r.user.name} (${r.user.role}) - ID: ${r.userId}`);
        if (r.welcomeMessages) {
          console.log(`     Email: ${r.welcomeMessages.email ? '✅' : '❌'}, SMS: ${r.welcomeMessages.sms ? '✅' : '❌'}`);
        }
      });
    }

    if (failed > 0) {
      console.log('\n❌ ÉCHECS:');
      results.filter(r => !r.success).forEach(r => {
        console.log(`   • ${r.user.name} (${r.user.role}): ${r.error}`);
      });
    }

    // Nettoyer les données de test
    console.log('\n🧹 Nettoyage des données de test...');
    
    // Supprimer les utilisateurs créés
    for (const result of results.filter(r => r.success)) {
      try {
        await User.deleteOne({ user_id: result.userId });
        console.log(`✅ Utilisateur ${result.user.name} supprimé`);
      } catch (error) {
        console.error(`❌ Erreur suppression ${result.user.name}:`, error.message);
      }
    }

    // Supprimer l'école de test
    await School.deleteOne({ _id: testSchool._id });
    console.log('✅ École de test supprimée');

    console.log('\n🎯 FONCTIONNALITÉS TESTÉES:');
    console.log('   ✅ Enregistrement d\'utilisateurs par rôle');
    console.log('   ✅ Génération d\'IDs utilisateur selon le rôle');
    console.log('   ✅ Création Firebase automatique');
    console.log('   ✅ Envoi d\'emails de bienvenue avec redirection');
    console.log('   ✅ Envoi de SMS de bienvenue (optionnel)');
    console.log('   ✅ Gestion des utilisateurs sans téléphone');
    console.log('   ✅ Association avec les écoles');

    console.log('\n💡 NOTES:');
    console.log('   • Les emails et SMS ne sont pas envoyés réellement en mode test');
    console.log('   • Configurez EMAIL_* et VONAGE_* pour tester l\'envoi réel');
    console.log('   • Les utilisateurs reçoivent un lien pour configurer leur mot de passe');

  } catch (error) {
    console.error('❌ Erreur générale lors du test:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Connexion MongoDB fermée');
    console.log('🏁 Test terminé');
  }
}

// Exécuter le test
if (require.main === module) {
  testRegisterUserWithWelcome()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}

module.exports = { testRegisterUserWithWelcome };
