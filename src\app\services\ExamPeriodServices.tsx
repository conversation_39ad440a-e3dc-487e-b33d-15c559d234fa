import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.NEXT_PUBLIC_API_URL || "https://backend.scholarifyltd.com/api";
// const BASE_API_URL = "http://localhost:3002/api"; // For local development, change as needed

export interface ExamPeriod {
  _id: string;
  exam_period_id: string;
  school_id: string;
  term_id: string;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  exam_type: 'mid_term' | 'final' | 'quiz' | 'test' | 'assessment' | 'other';
  status: 'draft' | 'scheduled' | 'active' | 'completed' | 'cancelled';
  academic_year: string;
  priority: number;
  settings: {
    allow_normal_classes: boolean;
    suspend_normal_classes: boolean;
    notify_teachers: boolean;
    notify_students: boolean;
  };
  created_by: string;
  updated_by?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  
  // Populated fields
  term_id_details?: {
    _id: string;
    name: string;
    term_type: string;
    start_date: string;
    end_date: string;
  };
  created_by_details?: {
    _id: string;
    first_name?: string;
    last_name?: string;
    name?: string;
  };
}

export interface ExamPeriodConflict {
  name: string;
  start_date: string;
  end_date: string;
  term: string;
}

export interface CreateExamPeriodData {
  term_id: string;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  exam_type?: 'mid_term' | 'final' | 'quiz' | 'test' | 'assessment' | 'other';
  academic_year: string;
  priority?: number;
  settings?: {
    allow_normal_classes?: boolean;
    suspend_normal_classes?: boolean;
    notify_teachers?: boolean;
    notify_students?: boolean;
  };
  notes?: string;
}

// Get all exam periods for a school
export async function getExamPeriods(schoolId: string, filters: {
  term_id?: string;
  academic_year?: string;
  status?: string;
} = {}): Promise<{
  exam_periods: ExamPeriod[];
  total: number;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    if (filters.term_id) queryParams.append('term_id', filters.term_id);
    if (filters.academic_year) queryParams.append('academic_year', filters.academic_year);
    if (filters.status) queryParams.append('status', filters.status);

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/exam-periods/school/${schoolId}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching exam periods:", response.statusText);
      throw new Error("Failed to fetch exam periods");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch exam periods error:", error);
    throw new Error("Failed to fetch exam periods");
  }
}

// Get exam periods by term
export async function getExamPeriodsByTerm(schoolId: string, termId: string): Promise<{
  exam_periods: ExamPeriod[];
  total: number;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/exam-periods/school/${schoolId}/term/${termId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching exam periods by term:", response.statusText);
      throw new Error("Failed to fetch exam periods by term");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch exam periods by term error:", error);
    throw new Error("Failed to fetch exam periods by term");
  }
}

// Get single exam period
export async function getExamPeriod(schoolId: string, examPeriodId: string): Promise<{
  exam_period: ExamPeriod;
  exam_schedules: any[];
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/exam-periods/school/${schoolId}/${examPeriodId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching exam period:", response.statusText);
      throw new Error("Failed to fetch exam period");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch exam period error:", error);
    throw new Error("Failed to fetch exam period");
  }
}

// Create new exam period
export async function createExamPeriod(schoolId: string, examPeriodData: CreateExamPeriodData): Promise<{
  exam_period: ExamPeriod;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/exam-periods/school/${schoolId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(examPeriodData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating exam period:", errorData);

      // Check if it's a conflict error
      if (errorData.conflicts) {
        const conflictError = new Error(errorData.message || "Exam period conflicts detected") as any;
        conflictError.conflicts = errorData.conflicts;
        throw conflictError;
      }

      throw new Error(errorData.message || "Failed to create exam period");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create exam period error:", error);
    throw error;
  }
}

// Update exam period
export async function updateExamPeriod(schoolId: string, examPeriodId: string, updateData: Partial<CreateExamPeriodData>): Promise<{
  exam_period: ExamPeriod;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/exam-periods/school/${schoolId}/${examPeriodId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error updating exam period:", errorData);

      // Check if it's a conflict error
      if (errorData.conflicts) {
        const conflictError = new Error(errorData.message || "Exam period conflicts detected") as any;
        conflictError.conflicts = errorData.conflicts;
        throw conflictError;
      }

      throw new Error(errorData.message || "Failed to update exam period");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update exam period error:", error);
    throw error;
  }
}

// Helper function to format exam period dates
export function formatExamPeriodDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

// Helper function to get exam type display name
export function getExamTypeDisplayName(examType: string): string {
  const typeMap: Record<string, string> = {
    'mid_term': 'Mid-Term',
    'final': 'Final Exam',
    'quiz': 'Quiz',
    'test': 'Test',
    'assessment': 'Assessment',
    'other': 'Other'
  };
  
  return typeMap[examType] || examType;
}

// Helper function to get status color
export function getExamPeriodStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    'draft': 'text-gray-600 bg-gray-100',
    'scheduled': 'text-blue-600 bg-blue-100',
    'active': 'text-green-600 bg-green-100',
    'completed': 'text-purple-600 bg-purple-100',
    'cancelled': 'text-red-600 bg-red-100'
  };
  
  return colorMap[status] || 'text-gray-600 bg-gray-100';
}
