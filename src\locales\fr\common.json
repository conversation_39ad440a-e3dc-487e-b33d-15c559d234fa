{"common": {"loading": "Chargement...", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "add": "Ajouter", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "import": "Importer", "refresh": "Actualiser", "back": "Retour", "next": "Suivant", "previous": "Précédent", "submit": "So<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "yes": "O<PERSON>", "no": "Non", "close": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view": "Voir", "download": "Télécharger", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "apply": "Appliquer", "actions": "Actions", "status": "Statut", "active": "Actif", "inactive": "Inactif", "enabled": "Activé", "disabled": "Désactivé", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "warning": "Avertissement", "info": "Information", "required": "Requis", "optional": "Optionnel", "name": "Nom", "email": "Email", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "date": "Date", "time": "<PERSON><PERSON>", "description": "Description", "total": "Total", "amount": "<PERSON><PERSON>", "price": "Prix", "quantity": "Quantité", "example_title": "Exemple: {title}", "unknown": "Inconnu", "deleting": "Suppression en cours...", "continue": "<PERSON><PERSON><PERSON>", "notification": "Notification", "update": "Mettre à jour", "create": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "sending": "Envoi en cours...", "view_all": "Voir tout", "manage": "<PERSON><PERSON><PERSON>", "not_available": "N/A", "gender": "Genre", "mandatory": "obligatoire", "full_name": "Nom Co<PERSON>t", "phone_number": "Numéro de Téléphone", "transaction_id": "ID Transaction", "search_placeholder": "Rechercher...", "no_results_found": "Aucun résultat trouvé", "load_more": "Charger plus", "go_back": "Retour", "user_id": "ID Utilisateur", "role": "R<PERSON><PERSON>", "payment_status": {"completed": "Complété", "pending": "En attente", "failed": "<PERSON><PERSON><PERSON>", "refunded": "Re<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "unknown": "Inconnu"}, "credits": "Crédits", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "loading_data": "Chargement des données...", "please_wait": "Veuillez patienter...", "operation_successful": "Opération réussie", "operation_failed": "Opération échouée", "confirm_delete": "Êtes-vous sûr de vouloir supprimer cet élément ?", "confirm_delete_multiple": "Êtes-vous sûr de vouloir supprimer ces éléments ?", "delete_confirmation": "Confirmation de suppression", "bulk_actions": "Actions en lot", "selected_items": "Éléments sélectionnés", "all_items": "Tous les éléments", "select_all": "<PERSON><PERSON>", "deselect_all": "<PERSON><PERSON>", "showing_count": "Affichage de {filtered} sur {total} {type}", "created": "c<PERSON><PERSON>", "updated": "mis à jour", "view_plans": "Voir les plans", "filters": "Filtres", "clear_filters": "Effacer les filtres", "all_terms": "Tous les trimestres", "all_sequences": "Toutes les séquences", "term": "Trimestre", "sequence": "S<PERSON><PERSON>", "no_data_available": "<PERSON><PERSON><PERSON> donnée disponible", "since_last_month": "<PERSON><PERSON><PERSON> le mois dernier", "average_grade": "<PERSON> moyenne", "number_of_students": "Nombre d'étudiants", "saving": "Enregistrement...", "missing_required_information": "Informations requises <PERSON><PERSON><PERSON>"}, "navigation": {"dashboard": "Tableau de bord", "schools": "Écoles", "users": "Utilisateurs", "students": "Étudiants", "teachers": "Enseignants", "parents": "Parents", "classes": "Classes", "subjects": "<PERSON><PERSON><PERSON>", "grades": "Notes", "attendance": "Présence", "schedule": "Emploi du temps", "reports": "Rapports", "settings": "Paramètres", "profile": "Profil", "logout": "Déconnexion", "notifications": "Notifications", "credits": "Crédits", "subscriptions": "Abonnements", "payments": "Paiements", "analytics": "Analyses", "refunds": "Remboursements", "school": "École", "staff": "Personnel", "people_management": "Gestion du Personnel", "academic_records": "Dossiers Académiques", "terms": "Trimestres", "timetable": "Emploi du temps", "teacher_assignment": "Affectation des Enseignants", "periods": "<PERSON><PERSON><PERSON><PERSON>", "exam_types": "Types d'Examens", "discipline": "Discipline", "communications": "Communications", "announcements": "Annonces", "resources": "Ressources", "financial": "Financier", "fee_types": "Types de Frais", "fee_transactions": "Transactions de Frais", "buy_credit": "Acheter des Crédits", "reports_analytics": "Rapports et Analyses"}, "dashboard": {"top_classes_performance": "Performance des meilleures classes", "performance_overview_scale": "Aperçu des performances (Échelle: 0-20)", "priority_announcements": "Annonces prioritaires", "no_announcements_available": "Aucune annonce disponible", "create_first_announcement": "<PERSON><PERSON>er votre première annonce", "failed_to_load_announcements": "Échec du chargement des annonces", "super-admin": {"pages": {"dashboard": {"title": "Tableau de bord Super Admin", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble", "statistics": "Statistiques", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "total_schools": "Total des écoles", "total_students": "Total des étudiants", "total_teachers": "Total des enseignants", "total_users": "Total des utilisateurs", "active_subscriptions": "Abonnements actifs", "revenue": "<PERSON><PERSON><PERSON> (XAF)", "growth": "Croissance"}, "schools": {"title": "Gestion des écoles", "add_school": "Ajouter une école", "edit_school": "Modifier l'école", "school_name": "Nom de l'école", "school_code": "Code de l'école", "school_type": "Type d'école", "contact_person": "<PERSON><PERSON> de contact", "subscription_plan": "Plan d'abonnement", "registration_date": "Date d'inscription", "last_activity": "Dernière activité", "view_details": "Voir les détails", "manage_credits": "<PERSON><PERSON><PERSON> les crédits", "view_analytics": "Voir les analyses", "principal_name": "Nom du directeur", "established_year": "Année de <PERSON>réation", "website": "Site web"}, "credits": {"title": "Gestion des Crédits", "available_credits": "Crédits disponibles", "used_credits": "Crédits utilisés", "total_credits": "Total des crédits", "credit_history": "Historique des crédits", "purchase_credits": "Acheter des crédits", "credit_usage": "Utilisation des crédits", "remaining_credits": "Crédits restants", "credit_balance": "Solde de crédits", "per_student": "par étudiant", "per_message": "par message", "overview": "Vue d'ensemble des crédits et analytics par école", "search_placeholder": "Rechercher par nom d'école ou adresse...", "balance": "Solde", "revenue": "<PERSON>en<PERSON>", "efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remaining": "Restants", "view_details": "Voir Détails", "manage_title": "Détails des Crédits", "manage_description": "Gestion des crédits et analytics détaillées", "missing_school_id": "ID d'école manquant", "select_school_message": "Veuillez sélectionner une école pour voir ses détails.", "back_to_list": "Retour à la liste", "status": "Statut", "current_balance": "Solde Actuel", "since_beginning": "<PERSON><PERSON><PERSON> le dé<PERSON>", "average_revenue_per_student": "Revenu moyen par étudiant", "autonomy": "Autonomie", "estimated_days_remaining": "Jours restants estimés", "usage_metrics": "Métriques d'Utilisation", "registered_students": "Étudiants Enregistrés", "purchased_credits": "Cré<PERSON><PERSON>", "purchase_count": "Nombre d'Achats", "average_purchase": "<PERSON><PERSON><PERSON>", "recent_transactions": "Transactions Récentes", "no_transactions_found": "Aucune transaction trouvée", "all_transactions": "Toutes les Transactions", "subscription_details": "Détails de la Souscription", "plan_type": "Type de Plan", "start_date": "Date de Début", "activated": "activées", "new_transaction": "Nouvelle Transaction de Crédit", "academic_year": "<PERSON><PERSON>", "select_academic_year": "Sélectionner l'année académique", "payment_method": "Méthode de Paiement", "amount_paid": "<PERSON><PERSON>", "send_credit": "Envoyer <PERSON><PERSON>", "total_revenue": "<PERSON><PERSON><PERSON>"}, "subscriptions": {"title": "Gestion des abonnements", "plan_basic": "Basique", "plan_standard": "Standard", "plan_custom": "<PERSON><PERSON><PERSON><PERSON>", "plan_features": "Fonctionnalités du plan", "subscription_status": "Statut de l'abonnement", "renewal_date": "Date de renouvellement", "upgrade_plan": "Mettre à niveau le plan", "downgrade_plan": "Rétrograder le plan", "cancel_subscription": "Annuler l'abonnement"}, "settings": {"title": "Paramètres", "profile_tab": "Paramètres du Profil", "credit_tab": "Paramètres des Crédits", "general_tab": "Paramètres Généraux", "profile_title": "Paramètres du Profil", "change_avatar": "Changer l'Avatar", "update_profile": "Mettre à jour le Profil", "credit_title": "Paramètres des Crédits", "resell_price_per_credit": "Prix de Revente par Crédit", "buy_price_per_credit": "Prix d'Achat par Crédit", "update_credit_settings": "Mettre à jour les Paramètres de Crédit", "general_title": "Paramètres Généraux", "platform_name": "Nom de la Plateforme", "support_email": "Email de Support", "default_language": "Langue par Défaut", "maintenance_mode": "Mode Maintenance", "maintenance_message": "Message de Maintenance", "maintenance_message_placeholder": "Nous reviendrons bientôt !", "update_general_settings": "Mettre à jour les Paramètres Généraux", "credit_updated": "Paramètres de crédit mis à jour avec succès", "credit_update_failed": "Échec de la mise à jour des paramètres de crédit", "general_updated": "Paramètres généraux mis à jour avec succès", "general_update_failed": "Échec de la mise à jour des paramètres généraux"}, "reports": {"title": "Rapports", "generate_report": "Générer un rapport", "export_data": "Exporter les données", "date_range": "Plage de dates", "report_type": "Type de rapport"}, "users": {"title": "Gestion des utilisateurs", "add_user": "Ajouter un utilisateur", "edit_user": "Modifier l'utilisateur", "user_role": "Rôle de l'utilisateur", "user_status": "Statut de l'utilisateur", "last_login": "Dernière connexion", "account_created": "<PERSON><PERSON><PERSON>", "all_roles": "To<PERSON> les rôles", "filter_by_role": "Filtrer par rôle", "user_details": "Détails de l'utilisateur", "delete_user": "Supprimer l'utilisateur", "bulk_delete": "Suppression en lot", "selected_users": "Utilisateurs sélectionnés"}, "parents": {"title": "Gestion des parents", "invite_parent": "Inviter un parent", "parent_details": "<PERSON><PERSON><PERSON> du parent", "children": "<PERSON><PERSON><PERSON>", "contact_info": "Informations de contact"}, "refunds": {"title": "Gestion des remboursements", "refund_request": "<PERSON><PERSON><PERSON> de rembo<PERSON>", "refund_status": "Statut du remboursement", "refund_amount": "Montant du remboursement", "process_refund": "Trai<PERSON> le remboursement", "refund_reason": "Raison du remboursement"}, "subscription-plans": {"title": "Plans d'abonnement", "description": "<PERSON><PERSON><PERSON> les plans d'abonnement et leurs fonctionnalités", "create_plan": "Créer un plan", "edit_plan": "Modifier le plan", "plan_details": "<PERSON>é<PERSON> du plan", "plan_features": "Fonctionnalités du plan", "plan_pricing": "Tarification du plan", "active_plans": "Plans actifs", "new_plan": "Nouveau Plan", "search_placeholder": "Rechercher par nom, description...", "all_plans": "Tous les plans", "active": "Actifs", "inactive": "Inactifs", "popular": "Populaires", "total_plans": "Total des plans", "popular_plans": "Plans populaires", "with_chatbot": "Avec chatbot", "no_plans_found": "Aucun plan trouvé", "no_plans_match_criteria": "Aucun plan ne correspond à vos critères de recherche.", "create_first_plan": "Commencez par créer votre premier plan de souscription.", "min": "Min", "max": "Max", "chatbot": "<PERSON><PERSON><PERSON>", "features": "fonctionnalités", "order": "Ordre", "basic_info": "Informations de base", "display_name": "Nom d'affichage", "display_name_placeholder": "Plan Basic", "technical_name": "Nom technique", "technical_name_placeholder": "basic (auto-généré si vide)", "description_placeholder": "Description du plan...", "pricing": "Tarification", "price_per_credit": "Prix par crédit (XAF)", "minimum_purchase": "Achat minimum", "maximum_purchase": "Achat maximum", "unlimited": "Illimité", "enable_chatbot": "<PERSON><PERSON> le chatbot pour ce plan", "chatbot_credits_per_purchase": "Cré<PERSON>s chatbot par achat", "feature_placeholder": "Fonctionnalité...", "options": "Options", "recommended_for": "Recommandé pour", "recommended_for_placeholder": "Petites écoles (1-100 étudiants)", "display_order": "Ordre d'affichage", "active_plan": "Plan actif", "popular_plan": "Plan populaire", "contact_required": "Contact requis", "updating_plan": "Mise à jour du plan...", "creating_plan": "Création du plan...", "plan_updated": "Plan mis à jour !", "plan_created": "Plan créé !", "plan_updated_success": "Le plan de souscription a été mis à jour avec succès.", "plan_created_success": "Le plan de souscription a été créé avec succès."}, "classes": {"title": "Gestion des Classes", "search_placeholder": "Rechercher par nom d'école ou adresse...", "no_schools_found": "Aucune école trouvée correspondant à vos critères de recherche.", "manage_classes": "<PERSON><PERSON><PERSON> les Classes", "manage_classes_of": "<PERSON><PERSON>rer les Classes de", "class_level": "Niveau de Classe", "class_name": "Nom de la Classe", "class_code": "Code de la Classe", "class_level_name": "Nom du Niveau de Classe", "add_new_class": "Ajouter une Nouvelle Classe", "add_new_level": "Ajouter un Nouveau Niveau", "all_class_levels": "Tous les Niveaux de Classe", "edit_class": "Modifier la Classe", "edit_level": "Modifier le Niveau", "select_level": "Sélectionner un niveau", "level_name_required": "Le nom du niveau de classe est obligatoire."}, "students": {"title": "Gestion des Étudiants", "school_id": "ID École", "school_name": "Nom de l'École", "student_id": "ID Étudiant", "student_name": "Nom de l'Étudiant", "birthday": "Date de Naissance", "place_of_birth": "<PERSON><PERSON> de Naissance", "class_level": "Niveau de Classe", "parent_name": "Nom du Parent", "registered": "Inscrit", "no_class": "Aucune classe", "no_guardian": "<PERSON><PERSON><PERSON> tuteur", "manage_students": "<PERSON><PERSON><PERSON> les Étudiants", "manage_students_of": "G<PERSON>rer les Étudiants de", "popup_blocked_message": "Popup bloqué ! Veuillez autoriser les popups pour voir la liste des étudiants.", "import_complete": "Import terminé : {successful}/{total} étudiants importés.", "register_student": "Inscrire un Étudiant", "upload_csv": "Télécharger Liste CSV", "print_student_list": "Imprimer Liste des Étudiants", "print_id_cards": "Imprimer Cartes d'Identité"}}}, "school-admin": {"pages": {"dashboard": {"title": "Tableau de bord École", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble de l'école", "statistics": "Statistiques de l'école", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "total_students": "Total des étudiants", "total_teachers": "Total des enseignants", "total_classes": "Total des classes", "attendance_rate": "<PERSON>x de présence", "academic_performance": "Performance académique", "current_credit_balance": "Solde de crédit actuel", "no_school_associated": "Aucune école associée à ce compte"}, "students": {"title": "Gestion des étudiants", "add_student": "Ajouter un étudiant", "edit_student": "Modifier l'étudiant", "student_id": "ID étudiant", "student_name": "Nom de l'étudiant", "class_level": "Niveau de classe", "enrollment_date": "Date d'inscription", "parent_contact": "Contact parent", "academic_year": "<PERSON><PERSON> acadé<PERSON>"}, "school": {"title": "Informations de l'École", "school_details": "Détails de l'école", "school_name": "Nom de l'école", "school_address": "Adresse de l'école", "contact_info": "Informations de contact", "edit_school": "Modifier l'école"}, "announcements": {"title": "Annonces", "page_title": "Annonces", "page_subtitle": "Gérer les annonces et communications de l'école", "create_announcement": "<PERSON><PERSON>er une annonce", "add_new_announcement": "Ajouter une nouvelle annonce", "edit_announcement": "Modifier l'annonce", "delete_announcement": "Supprimer l'annonce", "announcement_title": "Titre de l'annonce", "announcement_content": "Contenu de l'annonce", "publish_date": "Date de publication", "target_audience": "Public cible", "priority": "Priorité", "status": "Statut", "published": "<PERSON><PERSON><PERSON>", "draft": "Brouillon", "archived": "Archivé", "search_placeholder": "Rechercher des annonces...", "filters": "Filtres", "selected": "sélectionné(s)", "delete_selected": "Supprimer la sélection", "all_priorities": "Toutes les priorités", "urgent": "<PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "low": "Faible", "all_status": "Tous les statuts", "all_audiences": "Tous les publics", "everyone": "<PERSON>ut le monde", "teachers": "Enseignants", "parents": "Parents", "students": "Étudiants", "clear_filters": "Effacer les filtres", "no_announcements_found": "<PERSON><PERSON><PERSON> annonce trouvée", "get_started_message": "Commencez par créer votre première annonce.", "adjust_filters_message": "Essayez d'ajuster vos critères de recherche ou de filtre.", "create_first_announcement": "Créer la première annonce", "showing_count": "Affichage de {filtered} sur {total} annonces", "delete_all": "<PERSON>ut supprimer", "content_required": "Contenu", "content_placeholder": "Entrez le contenu de l'annonce", "expires_at": "Date d'expiration", "expires_at_optional": "Date d'expiration (optionnel)", "publish_immediately": "Publier immédiatement", "save_as_draft": "Enregistrer comme brouillon", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "create_announcement_action": "Créer l'annonce", "update_announcement_action": "Mettre à jour l'annonce", "delete_confirmation_title": "Supprimer l'annonce", "delete_confirmation_message": "Êtes-vous sûr de vouloir supprimer cette annonce ?", "delete_warning": "Cette action ne peut pas être annulée.", "continue": "<PERSON><PERSON><PERSON>", "enter_password_confirm": "Veuillez entrer votre mot de passe pour confirmer la suppression de :", "password_placeholder": "Entrez votre mot de passe", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete_success": "L'annonce a été supprimée avec succès !", "delete_error": "Une erreur s'est produite lors de la suppression de l'annonce. R<PERSON><PERSON>ez et si cela persiste, contactez le support.", "password_required_alert": "Veuillez entrer votre mot de passe pour confirmer la suppression.", "delete_all_title": "Supprimer toutes les annonces", "delete_selected_title": "Supprimer les annonces sélectionnées", "delete_all_message": "Êtes-vous sûr de vouloir supprimer toutes les annonces ? Cette action ne peut pas être annulée.", "delete_selected_message": "Êtes-vous sûr de vouloir supprimer les annonces sélectionnées ? Cette action ne peut pas être annulée.", "all": "Tous", "view_details": "Détails de l'annonce", "back_to_announcements": "Retour aux annonces", "no_announcement_id": "Aucun ID d'annonce fourni", "failed_to_load": "Échec du chargement des détails de l'annonce", "announcement_not_found": "Annonce non trouvée", "priority_label": "Priorité {priority}", "target_audience_label": "Public cible", "created_label": "<PERSON><PERSON><PERSON>", "published_label": "<PERSON><PERSON><PERSON>", "expires_label": "Expire", "content_title": "Contenu", "expired_warning": "<PERSON>tte annonce a expiré le {date}"}, "terms": {"title": "Gestion des Trimestres", "add_term": "Ajouter un trimestre", "edit_term": "Modifier le trimestre", "delete_term": "Supprimer le trimestre", "term_name": "Nom du trimestre", "start_date": "Date de début", "end_date": "Date de fin", "current_term": "Trimestre actuel", "active": "Actif", "inactive": "Inactif", "set_as_current": "Définir comme actuel", "last_term": "<PERSON><PERSON>"}, "discipline": {"title": "Dossiers Disciplinaires", "add_record": "Ajouter un dossier", "edit_record": "Modifier le dossier", "delete_record": "Supp<PERSON>er le dossier", "student_name": "Nom de l'étudiant", "incident_type": "Type d'incident", "incident_date": "Date de l'incident", "description": "Description", "action_taken": "Action prise", "severity": "Gravité", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "Élevée", "discipline_records_management": "Gestion des dossiers disciplinaires", "discipline_id": "ID Discipline", "student": "Étudiant", "comments": "Commentaires", "created_at": "<PERSON><PERSON><PERSON>", "unknown_student": "Étudiant inconnu", "no_comments": "Aucun commentaire", "invalid_date": "Date invalide", "add_new_discipline_record": "Ajouter un nouveau dossier disciplinaire", "failed_to_fetch": "Échec de la récupération des dossiers disciplinaires", "record_updated_successfully": "Dossier disciplinaire mis à jour avec succès", "record_created_successfully": "Dossier disciplinaire créé avec succès", "failed_to_save": "Échec de l'enregistrement du dossier disciplinaire", "user_email_not_found": "Email de l'utilisateur non trouvé", "record_deleted_successfully": "Dossier disciplinaire supprimé avec succès", "records_deleted_successfully": "{count} dossiers disciplinaires supprimés avec succès", "invalid_password": "Mot de passe invalide", "failed_to_delete": "Échec de la suppression du/des dossier(s) disciplinaire(s)", "delete_discipline_record": "Supprimer le dossier disciplinaire", "delete_selected_records": "Supprimer les dossiers sélectionnés", "delete_confirmation_single": "Êtes-vous sûr de vouloir supprimer le dossier disciplinaire \"{disciplineId}\" ? Cette action ne peut pas être annulée.", "delete_confirmation_multiple": "Êtes-vous sûr de vouloir supprimer {count} dossiers disciplinaires sélectionnés ? Cette action ne peut pas être annulée.", "student_required": "L'étudiant est requis", "school_id_required": "L'ID de l'école est requis", "edit_discipline_record": "Modifier le dossier disciplinaire", "add_discipline_record": "Ajouter un nouveau dossier disciplinaire", "select_student": "Sélectionner un étudiant", "comments_placeholder": "Entrez les détails disciplinaires et commentaires..."}, "reports": {"title": "Rapports et Analyses", "generate_report": "Générer un rapport", "student_reports": "Rapports d'étudiants", "financial_reports": "Rapports financiers", "attendance_reports": "Rapports de présence", "academic_reports": "Rapports académiques", "export_data": "Exporter les données"}, "buy_credit": {"title": "Acheter des Crédits", "page_title": "Gestion des Crédits", "page_subtitle": "<PERSON><PERSON>rez votre souscription et achetez des crédits pour votre école", "current_balance": "Solde actuel", "purchase_credits": "Acheter des crédits", "credit_packages": "Forfaits de crédits", "transaction_history": "Historique des transactions", "usage_analytics": "Analyses d'utilisation", "error_title": "<PERSON><PERSON><PERSON>", "subscription_not_found": "Données de souscription non trouvées", "student_stats_error": "Erreur lors du chargement des statistiques des étudiants", "retry": "<PERSON><PERSON><PERSON><PERSON>", "debug_test": "Test Debug", "view_history": "Voir l'historique", "buy_more_credits": "Acheter plus de crédits", "available_credits": "Crédits disponibles", "credits_used": "Crédits utilisés", "total_purchased": "Total acheté", "efficiency_score": "Score d'efficacité", "subscription_overview": "Aperçu de l'abonnement", "subscription_type": "Type d'abonnement", "subscription_status": "Statut de l'abonnement", "next_billing": "Prochaine facturation", "monthly_limit": "Limite mensuelle", "usage_this_month": "Utilisation ce mois-ci", "student_statistics": "Statistiques des étudiants", "total_students": "Total des étudiants", "active_students": "Étudiants actifs", "inactive_students": "Étudiants inactifs", "recent_activity": "Activité récente", "no_recent_activity": "Aucune activité récente", "pending_purchases": "Achats en attente", "no_pending_purchases": "Aucun achat en attente", "purchase_date": "Date d'achat", "amount": "<PERSON><PERSON>", "status": "Statut", "processing": "En cours de traitement", "completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "low_balance_title": "Solde de crédits faible", "low_balance_message": "Il vous reste seulement {credits}. Pensez à recharger pour continuer à utiliser toutes les fonctionnalités.", "recharge_now": "Recharger maintenant", "pending_payment_title": "Paiement en attente", "pending_payment_message": "Vous avez {count} paiement(s) en attente. Cliquez pour finaliser votre achat de crédits.", "finalize_payment": "Finaliser ({credits})", "total_paid": "Total payé", "registered_students": "Étudiants enregistrés", "low_balance": "Solde faible", "normal_balance": "Solde normal", "efficiency": "Efficacité: {score}%", "equivalent": "Équivalent: {credits} crédits <PERSON>", "stats_error": "<PERSON><PERSON><PERSON>", "stats_loading": "...", "of_total": "{rate}% du total", "total_students_count": "({count} étudiants au total)", "registered_payments": "Paiements enregistrés", "current_plan": "Plan actuel", "features": "Fonctionnalités", "features_enabled": "{count} activées", "quick_actions": "Actions rapides", "buy_credits_action": "Acheter des crédits", "recharge_balance": "Recharger votre solde", "detailed_history": "Historique détaillé", "view_all_transactions": "Voir toutes les transactions", "change_plan": "Changer de plan", "discover_offers": "Découvrir nos offres", "recent_transactions": "Transactions récentes", "view_all": "Voir tout", "no_recent_transactions": "Aucune transaction récente", "credit_purchase": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "days_ago": "Il y a {days} jours", "finalize": "Finaliser", "view_complete_history": "Voir l'historique complet", "error_loading_transactions": "Erreur lors du chargement des transactions", "credits_purchased": "Credits Purchased", "modal": {"buy_credits_title": "Acheter des crédits", "credits_to_buy": "Nombre de crédits à acheter", "custom_amount": "<PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "credits_label": "Crédits:", "unit_price": "Prix unitaire:", "subtotal": "Sous-total:", "taxes": "Taxes:", "total": "Total:", "billing_info": "Informations de facturation", "full_name": "Nom complet", "email_address": "Adresse e-mail", "phone_number": "Numéro de téléphone", "organization": "Organisation (optionnel)", "proceed_payment": "Procéder au paiement", "checking_payment": "Vérification du paiement...", "checking_status": "Vérification du statut de votre paiement", "payment_initiated": "Paiement initié avec succès !", "redirect_message": "Vous allez être redirigé vers la page de paiement"}, "history": {"title": "Historique des achats de crédits", "back_to_credits": "Retour aux crédits", "search_placeholder": "Rechercher par ID de transaction...", "filter_by_status": "Filtrer par statut", "filter_by_date": "Filtrer par date", "all_statuses": "Tous les statuts", "last_30_days": "30 derniers jours", "last_90_days": "90 derniers jours", "this_year": "<PERSON><PERSON> an<PERSON>", "export_csv": "Exporter CSV", "no_purchases_found": "<PERSON><PERSON><PERSON> achat trouvé", "no_purchases_message": "Aucun achat de crédit n'a été effectué pour le moment.", "adjust_filters": "Essayez d'ajuster vos filtres ou votre terme de recherche.", "transaction_id": "ID de transaction", "credits_purchased": "<PERSON><PERSON><PERSON><PERSON>", "total_amount": "Montant total", "purchase_date": "Date d'achat", "payment_method": "Méthode de paiement", "view_details": "Voir les détails", "loading_history": "Chargement de l'historique...", "error_loading": "Erreur lors du chargement de l'historique", "try_again": "<PERSON><PERSON><PERSON><PERSON>", "reason_not_specified": "Raison non spécifiée", "purchase_history_title": "Historique des achats", "purchase_history_subtitle": "Consultez l'historique de vos achats de crédits", "export": "Exporter", "completed_transactions": "Complétées", "pending_transactions": "En attente", "cancelled_transactions": "<PERSON><PERSON><PERSON>", "total_credits": "Total crédits", "efficiency_rate": "<PERSON><PERSON> d'efficaci<PERSON>", "credits_obtained": "<PERSON><PERSON><PERSON><PERSON> obtenus", "completed_transactions_only": "Transactions complétées", "total_transactions": "Total transactions", "all_categories": "Toutes catégories", "search_by_id": "Rechercher par ID...", "all_dates": "Toutes les dates", "today": "<PERSON><PERSON><PERSON>'hui", "this_week": "<PERSON><PERSON> se<PERSON>", "this_month": "<PERSON> mois", "transaction": "Transaction", "date": "Date", "credits": "Crédits", "amount": "<PERSON><PERSON>", "status": "Statut", "actions": "Actions", "cancellation_reason": "Raison d'annulation:"}, "success": {"checking_payment": "Vérification du paiement...", "checking_message": "Nous vérifions le statut de votre paiement. Veuillez patienter.", "attempt_count": "Tentative {count}/10", "payment_successful": "Paiement ré<PERSON> !", "success_message": "Votre achat de crédits a été traité avec succès.", "payment_failed": "<PERSON><PERSON><PERSON>", "failed_message": "Votre paiement n'a pas pu être traité. Veuillez réessayer.", "payment_pending": "Paiement en attente", "pending_message": "Votre paiement est en cours de traitement. Cela peut prendre quelques minutes.", "payment_expired": "Paiement expiré", "expired_message": "Votre session de paiement a expiré. Veuillez recommencer.", "back_to_credits": "Retour aux crédits", "retry_payment": "Réessayer le paiement", "credits_purchased_label": "Cré<PERSON><PERSON>:", "total_amount_label": "Montant total:", "transaction_id_label": "ID de transaction:", "payment_error_message": "Une erreur est survenue lors du traitement de votre paiement.", "go_to_dashboard": "<PERSON>er au tableau de bord", "check_again": "Vérifier à nouveau", "try_new_purchase": "Essayer un nouvel achat", "back_to_dashboard": "Retour au tableau de bord"}}, "settings": {"title": "Paramètres", "profile_settings": "Paramètres du profil", "general_settings": "Paramètres généraux", "preferences": "Préférences", "credit_settings": "Paramètres de crédit", "language": "<PERSON><PERSON>", "timezone": "<PERSON><PERSON> ho<PERSON>", "currency": "<PERSON><PERSON>", "notifications": "Notifications", "grading_scale": "Échelle de notation", "grading_system": "Système de notation"}, "attendance": {"title": "Gestion des Présences", "page_title": "<PERSON><PERSON><PERSON> les Présences", "page_subtitle": "Sélectionnez une classe pour voir ou saisir les présences.", "mark_attendance": "Marquer la présence", "attendance_report": "Rapport de présence", "present": "Présent", "absent": "Absent", "late": "En retard", "excused": "Excusé", "view_attendance": "Voir les présences", "attendance_summary": "Résumé des présences", "search_classes": "Rechercher des classes...", "current_term": "Trimestre actuel : {term}", "no_classes_found": "Aucune classe trouvée", "no_classes_available": "Aucune classe disponible", "adjust_search_terms": "Essayez d'ajuster vos termes de recherche", "classes_will_appear": "Les classes apparaîtront ici une fois qu'elles seront créées", "failed_to_load_classes": "Échec du chargement des données des classes", "total_students": "Total des étudiants", "manage_attendance": "<PERSON><PERSON><PERSON> les présences", "subjects": {"page_title": "<PERSON><PERSON><PERSON> les présences - {className}", "page_subtitle": "Sélectionnez une matière pour gérer les présences.", "loading_title": "Chargement...", "not_assigned": "Non assigné", "no_subjects_available": "Aucune matière disponible", "subjects_will_appear": "Les matières apparaîtront ici une fois qu'elles seront créées et assignées aux enseignants", "breadcrumb_attendance": "Présences"}, "student_attendance": {"attendance_for": "Présences pour {className}", "pick_a_date": "Choisir une date", "select_schedule": "Sélectionner un horaire", "no_schedules": "Aucun horaire disponible", "search_students": "Rechercher des étudiants...", "mark_all_present": "<PERSON><PERSON> tous présents", "mark_all_absent": "<PERSON><PERSON> tous absents", "mark_all_late": "<PERSON><PERSON> tous en retard", "mark_all_excused": "Marquer tous excusés", "save_attendance": "Enregistrer les présences", "saving": "Enregistrement...", "no_students_found": "Aucun étudiant trouvé", "no_students_message": "Aucun étudiant n'est inscrit dans cette classe.", "present": "Présent", "absent": "Absent", "late": "En retard", "excused": "Excusé", "attendance_saved": "Présences enregistrées avec succès !", "attendance_save_failed": "Échec de l'enregistrement des présences"}}, "classes": {"title": "Gestion des Classes", "add_class": "Ajouter une classe", "edit_class": "Modifier la classe", "delete_class": "Supprimer la classe", "class_name": "Nom de la classe", "class_capacity": "Capacité de la classe", "assigned_teacher": "Enseignant assigné", "schedule": "Emploi du temps", "view_class": "Voir la classe", "class_details": "Détails de la classe", "no_school_id": "Aucun ID d'école trouvé pour cet utilisateur.", "notification": "Notification", "add_new_class": "Ajouter une nouvelle classe", "all_levels": "To<PERSON> les niveaux", "class_level": "Niveau de classe", "class_code": "Code de classe", "view": "Voir", "delete": "<PERSON><PERSON><PERSON><PERSON>", "add_new_level": "Ajouter un nouveau niveau", "class_level_name": "Nom du niveau de classe", "class_created_success": "Classe créée avec succès !", "class_creation_failed": "Échec de la création de la classe", "invalid_password": "Mot de passe invalide", "class_deleted": "Classe supprimée", "class_deletion_failed": "Échec de la suppression de la classe", "classes_deleted": "Classes supprimées", "bulk_deletion_failed": "Échec de la suppression en lot", "level_created_success": "Niveau créé avec succès !", "level_deleted": "Niveau supprimé", "delete_all_classes": "Supprimer toutes les classes", "delete_selected_classes": "Supprimer les classes sélectionnées", "delete_confirmation": "Êtes-vous sûr de vouloir supprimer {count} classe(s) ?", "classes": "classes", "student_id": "ID Étudiant", "full_name": "Nom complet", "age": "Âge", "gender": "Genre", "status": "Statut", "not_specified": "Non spécifié", "class_updated_success": "Classe mise à jour avec succès !", "error_updating_class": "Erreur lors de la mise à jour de la classe.", "invalid_password_error": "Mot de passe invalide !", "class_deleted_success": "Classe supprimée avec succès !", "error_deleting_class": "Erreur lors de la suppression de la classe.", "subjects_for_class": "Matières pour {className}", "no_description": "Aucune description", "no_subjects_found": "Aucune matière trouvée pour cette classe.", "students_of_class": "Étudiants de {className}", "school": "École", "modals": {"delete_class": "Supprimer la classe", "delete_class_confirmation": "Êtes-vous sûr de vouloir supprimer la classe \"{className}\" ? Cette action est irréversible.", "enter_password_confirm": "Entrez votre mot de passe pour confirmer", "deleting": "Suppression...", "class_deleted_successfully": "La classe a été supprimée avec succès !", "error_deleting_class": "Une erreur s'est produite lors de la suppression de cette classe. <PERSON><PERSON><PERSON><PERSON> et si cela persiste, contactez le support !", "password_required": "Veuillez entrer votre mot de passe pour confirmer la suppression.", "update_class": "Modifier la classe", "class_name": "Nom de la classe", "class_code": "Code de la classe", "class_level": "Niveau de classe", "select_level": "Sélectionner le niveau", "class_updated_successfully": "La classe a été envoyée avec succès !", "error_updating_class": "Une erreur s'est produite lors de la mise à jour de cette classe. R<PERSON><PERSON>ez et si cela persiste, contactez le support !", "create_level": "<PERSON><PERSON>er un niveau", "level_name": "Nom du niveau", "level_name_required": "Le nom du niveau est requis", "level_created_successfully": "Le niveau a été créé avec succès !", "error_creating_level": "Une erreur s'est produite lors de la création de ce niveau. R<PERSON><PERSON>ez et si cela persiste, contactez le support !", "delete_level": "Supp<PERSON><PERSON> le niveau", "delete_level_confirmation": "Êtes-vous sûr de vouloir supprimer le niveau \"{levelName}\" ? Cette action est irréversible.", "level_deleted_successfully": "Le niveau a été supprimé avec succès !", "error_deleting_level": "Une erreur s'est produite lors de la suppression de ce niveau. <PERSON><PERSON><PERSON><PERSON> et si cela persiste, contactez le support !"}}, "examtype": {"title": "Types d'Examens", "add_exam_type": "Ajouter un type d'examen", "edit_exam_type": "Modifier le type d'examen", "delete_exam_type": "Supprimer le type d'examen", "exam_type_name": "Nom du type d'examen", "description": "Description", "weight": "Poids", "exam_type_management": "Gestion des types d'examens", "exam_type": "Type d'examen", "created_at": "<PERSON><PERSON><PERSON>", "invalid_date": "Date invalide", "add_new_exam_type": "Ajouter un nouveau type d'examen", "failed_to_fetch": "Échec de la récupération des types d'examens", "exam_type_updated_successfully": "Type d'examen mis à jour avec succès", "exam_type_created_successfully": "Type d'examen créé avec succès", "failed_to_save": "Échec de l'enregistrement du type d'examen", "user_email_not_found": "Email de l'utilisateur non trouvé", "exam_type_deleted_successfully": "Type d'examen supprimé avec succès", "exam_types_deleted_successfully": "{count} types d'examens supprimés avec succès", "invalid_password": "Mot de passe invalide", "failed_to_delete": "Échec de la suppression du/des type(s) d'examen(s)", "delete_selected_exam_types": "Supprimer les types d'examens sélectionnés", "delete_confirmation_single": "Êtes-vous sûr de vouloir supprimer le type d'examen \"{examType}\" ? Cette action ne peut pas être annulée.", "delete_confirmation_multiple": "Êtes-vous sûr de vouloir supprimer {count} types d'examens sélectionnés ? Cette action ne peut pas être annulée.", "exam_type_required": "Le type d'examen est requis", "school_id_required": "L'ID de l'école est requis", "edit_exam_type_modal": "Modifier le type d'examen", "add_exam_type_modal": "Ajouter un nouveau type d'examen", "exam_type_placeholder": "ex: <PERSON><PERSON><PERSON><PERSON> continu, Examen final, Quiz", "duration": "<PERSON><PERSON><PERSON>"}, "fees": {"title": "Types de frais", "fee_types_management": "Gestion des types de frais", "fee_type": "Type de frais", "amount": "<PERSON><PERSON>", "created_at": "<PERSON><PERSON><PERSON>", "invalid_date": "Date invalide", "add_new_fee_type": "Ajouter un nouveau type de frais", "failed_to_fetch": "Échec de la récupération des types de frais", "fee_updated_successfully": "Type de frais mis à jour avec succès", "fee_created_successfully": "Type de frais créé avec succès", "failed_to_save": "Échec de l'enregistrement du type de frais", "user_email_not_found": "Email de l'utilisateur non trouvé", "fee_deleted_successfully": "Type de frais supprimé avec succès", "fees_deleted_successfully": "{count} types de frais supprimés avec succès", "invalid_password": "Mot de passe invalide", "failed_to_delete": "Échec de la suppression du/des type(s) de frais", "delete_fee_type": "Supprimer le type de frais", "delete_selected_fee_types": "Supprimer les types de frais sélectionnés", "delete_confirmation_single": "Êtes-vous sûr de vouloir supprimer ce type de frais ? Cette action ne peut pas être annulée.", "delete_confirmation_multiple": "Êtes-vous sûr de vouloir supprimer {count} types de frais sélectionnés ? Cette action ne peut pas être annulée.", "tuition_fee": "Frais de scolarité", "registration_fee": "Frais d'inscription", "library_fee": "Frais de bibliothèque", "laboratory_fee": "Frais de laboratoire", "sports_fee": "Frais de sport", "technology_fee": "Frais de technologie", "transportation_fee": "Frais de transport", "examination_fee": "Frais d'examen", "activity_fee": "Frais d'activité", "uniform_fee": "Frais d'uniforme", "textbook_fee": "Frais de manuel", "fee_type_required": "Le type de frais est requis", "amount_required": "Le montant est requis", "valid_amount_required": "Veuillez entrer un montant positif valide", "school_id_required": "L'ID de l'école est requis", "edit_fee_type": "Modifier le type de frais", "add_fee_type": "Ajouter un nouveau type de frais", "fee_type_placeholder": "S<PERSON><PERSON><PERSON><PERSON> ou saisir le type de frais"}, "grades": {"title": "Gestion des Notes", "manage_grades": "Gestion des Notes", "select_class_subtitle": "Sélectionnez une classe pour voir ou saisir les notes.", "search_classes": "Rechercher des classes...", "current_term": "Trimestre actuel : {term}", "no_school_id": "Aucun ID d'école trouvé", "failed_to_load_classes": "Échec du chargement des données des classes", "no_classes_found": "Aucune classe trouvée", "no_classes_available": "Aucune classe disponible", "adjust_search_terms": "Essayez d'ajuster vos termes de recherche", "classes_will_appear": "Les classes apparaîtront ici une fois créées", "total_grades": "Total des notes", "classes": {"no_teacher_assigned": "Aucun professeur assigné", "class_average": "Note Moyenne de la classe", "errors": {"failed_to_load_grades": "Échec du chargement des notes", "failed_to_load_class_data": "Échec du chargement des données de la classe", "no_subjects_available": "Aucune matière disponible pour cette classe", "no_subjects_available_explanation": "Les matières s'afficheront ici une fois créées et assigné à des professeurs", "select_grades_to_delete": "<PERSON>euillez Sélectionner une note pour supprimer", "failed_to_submit_grade": "Échec de la soumission de la note", "failed_to_verify_password": "Email inconnu. Echec de la vérification du mot de passe.", "grade_exported_to_pdf_failed": "Échec de l'exportation de la note vers PDF", "grade_exported_to_excel_failed": "Échec de l'exportation de la note vers Excel"}, "success": {"grade_deleted_successfully": "Note supprimée avec succès", "grade_updated_successfully": "Note mise à jour avec succès", "grade_created_successfully": "Note créée avec succès", "grade_exported_to_pdf_successfully": "Note exportée vers PDF avec succès", "grade_exported_to_excel_successfully": "Note exportée vers Excel avec succès"}, "subject": {"manage_grade_for_subject_and_class": "Gérer les notes pour les sujets et classes", "add_grade": "Ajouter une note"}, "select_subject_to_manage_grades": "Sélectionnez une matière pour gerer les notes"}}, "justification": {"title": "Justifications d'Absence", "add_justification": "Ajouter une justification", "edit_justification": "Modifier la justification", "delete_justification": "Supprimer la justification", "student_name": "Nom de l'étudiant", "absence_date": "Date d'absence", "reason": "<PERSON>son", "status": "Statut", "approved": "Approu<PERSON><PERSON>", "pending": "En attente", "rejected": "<PERSON><PERSON><PERSON>"}, "parents": {"title": "Gestion des Parents", "add_parent": "Ajouter un parent", "edit_parent": "Modifier le parent", "delete_parent": "Supprimer le parent", "parent_name": "Nom du parent", "contact_info": "Informations de contact", "relationship": "Relation", "emergency_contact": "Contact d'urgence", "view_parent": "Voir le parent"}, "period": {"title": "Gestion des Périodes", "add_period": "Ajouter une période", "edit_period": "Modifier la période", "delete_period": "Supprimer la période", "period_name": "Nom de la période", "start_time": "<PERSON><PERSON> d<PERSON>", "end_time": "Heure de fin", "duration": "<PERSON><PERSON><PERSON>"}, "resources": {"title": "Ressources", "add_resource": "Ajouter une ressource", "edit_resource": "Modifier la ressource", "delete_resource": "Supprimer la ressource", "resource_name": "Nom de la ressource", "resource_type": "Type de ressource", "availability": "Disponibilité", "description": "Description"}, "school_resources": {"title": "Ressources de l'École", "add_school_resource": "Ajouter une ressource d'école", "edit_school_resource": "Modifier la ressource d'école", "delete_school_resource": "Supprimer la ressource d'école", "resource_name": "Nom de la ressource", "resource_type": "Type de ressource", "price": "Prix", "stock": "Stock", "class_level": "Niveau de classe"}, "staff": {"title": "Gestion du Personnel", "add_staff": "Ajouter un membre du personnel", "edit_staff": "Modifier le membre du personnel", "delete_staff": "Supprimer le membre du personnel", "staff_name": "Nom du personnel", "position": "Poste", "department": "Département", "hire_date": "Date d'embauche", "contact_info": "Informations de contact"}, "subjects": {"title": "Gestion des Matières", "add_subject": "Ajouter une matière", "edit_subject": "Modifier la matière", "delete_subject": "Supprimer la matière", "subject_name": "Nom de la matière", "subject_code": "Code de la matière", "description": "Description", "credits": "Crédits"}, "teacher_assignment": {"title": "Affectation des Enseignants", "assign_teacher": "<PERSON><PERSON><PERSON> un enseignant", "edit_assignment": "Modifier l'affectation", "delete_assignment": "Supprimer l'affectation", "teacher_name": "Nom de l'enseignant", "subject": "<PERSON><PERSON>", "class": "Classe", "academic_year": "<PERSON><PERSON> acadé<PERSON>"}, "teachers": {"title": "Gestion des Enseignants", "add_teacher": "Ajouter un enseignant", "edit_teacher": "Modifier l'enseignant", "delete_teacher": "Su<PERSON><PERSON><PERSON> l'enseignant", "teacher_name": "Nom de l'enseignant", "subject_taught": "<PERSON><PERSON> enseign<PERSON>", "qualification": "Qualification", "experience": "Expérience", "contact_info": "Informations de contact"}, "timetable": {"title": "Emp<PERSON>i du Temps", "create_timetable": "<PERSON><PERSON>er un emploi du temps", "edit_timetable": "Modifier l'emploi du temps", "view_timetable": "Voir l'emploi du temps", "class_schedule": "Horaire de classe", "teacher_schedule": "<PERSON><PERSON><PERSON> de l'enseignant", "time_slot": "Créneau horaire", "subject": "<PERSON><PERSON>", "room": "Salle"}}}, "teacher-dashboard": {"pages": {"dashboard": {"title": "<PERSON><PERSON> <PERSON> b<PERSON> En<PERSON>gnant", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble", "my_classes": "Mes classes", "today_schedule": "Emploi du temps d'aujourd'hui", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "students_count": "Nombre d'étudiants", "subjects_taught": "Matières enseignées", "upcoming_classes": "Cours à venir"}, "classes": {"title": "Mes classes", "class_details": "Détails de la classe", "student_list": "Liste des étudiants", "class_schedule": "Emploi du temps de la classe", "class_performance": "Performance de la classe"}, "attendance": {"title": "Présences", "take_attendance": "<PERSON><PERSON><PERSON> les présences", "attendance_history": "Historique des présences", "mark_present": "Marquer présent", "mark_absent": "<PERSON><PERSON> absent", "attendance_summary": "Résumé des présences"}, "grades": {"title": "Notes", "enter_grades": "Sai<PERSON> les notes", "grade_history": "Historique des notes", "grade_statistics": "Statistiques des notes", "class_average": "Moyenne de la classe", "student_progress": "Progrès de l'étudiant"}, "resources": {"title": "Ressources", "upload_resource": "<PERSON><PERSON><PERSON><PERSON>r une ressource", "my_resources": "<PERSON><PERSON> ressources", "shared_resources": "Ressources partagées", "resource_library": "Bibliothèque de ressources"}}}}, "components": {"chart": {"user_growth_trend": "Tendance de croissance des utilisateurs", "loading_user_stats": "Chargement des statistiques utilisateur...", "failed_to_load_data": "Échec du chargement des données", "select_year": "Sélectionner l'année"}, "performance_table": {"title": "Performance des écoles", "school_name": "Nom de l'école", "metric": "Métrique", "value": "<PERSON><PERSON>", "search_placeholder": "Rechercher une école...", "items_per_page": "Éléments par page", "showing": "Affichage de", "to": "à", "of": "sur", "entries": "entrées", "previous": "Précédent", "next": "Suivant", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "select_metric": "Sélectionner une métrique"}, "data_table": {"search": "Rechercher...", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "bulk_actions": "Actions en lot", "select_all": "<PERSON><PERSON>", "deselect_all": "<PERSON><PERSON>", "delete_selected": "Supprimer la sélection", "no_results": "Aucun résultat trouvé", "loading": "Chargement...", "rows_per_page": "Lignes par page", "page": "Page", "of_pages": "sur {total} pages", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "delete_selected_one": "Supprimer la sélection (1)", "delete_selected_count": "Supprimer la sélection ({count})", "view_details": "Voir les détails", "selected_count": "{selected} sur {total} sélectionnés", "select_all_count": "<PERSON><PERSON> s<PERSON> ({count})", "delete_all_count": "Tout supprimer ({count})", "active_filters": "Filtres actifs", "page_of": "Page {current} sur {total}", "items_per_page": "Éléments par page", "actions": "Actions", "all_items": "Tous les {type}"}, "modals": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "cancel": "Annuler", "save": "Enregistrer", "close": "<PERSON><PERSON><PERSON>", "are_you_sure": "Êtes-vous sûr ?", "this_action_cannot_be_undone": "Cette action ne peut pas être annulée", "delete_confirmation": "Êtes-vous sûr de vouloir supprimer cet élément ?", "delete_confirmation_with_name": "Êtes-vous sûr de vouloir supprimer <strong class=\"font-semibold text-pink-500\">{name}?</strong>", "delete_confirmation_prefix": "Êtes-vous sûr de vouloir supprimer", "bulk_delete_confirmation": "Êtes-vous sûr de vouloir supprimer {count} éléments sélectionnés ?", "delete_all_items": "Supprimer tous les {type}", "delete_selected_items": "Supprimer les {type} sélectionnés", "delete_all_warning": "Ceci supprimera définitivement TOUS les {type} du système. Cette action ne peut pas être annulée !", "delete_selected_warning": "Ceci supprimera définitivement les {type} sélectionnés. Cette action ne peut pas être annulée !", "danger_delete_all": "⚠️ DANGER : Opération de suppression totale", "bulk_delete_operation": "⚠️ Opération de suppression en lot", "about_to_delete_prefix": "Vous êtes sur le point de supprimer", "delete_all_system_warning": "Ceci supprimera TOUS les {type} du système et ne peut pas être annulé !", "enter_password_to_confirm": "Veuillez entrer votre mot de passe pour confirmer cette opération {type} :", "destructive": "destructrice", "bulk": "en lot", "delete_count_items": "Supprimer {count} {type}"}}, "notifications": {"title": "Notifications", "mark_as_read": "Marquer comme lu", "mark_all_read": "Tout marquer comme lu", "no_notifications": "Aucune notification", "new_notification": "Nouvelle notification", "notification_settings": "Paramètres de notification", "email_notifications": "Notifications par email", "push_notifications": "Notifications push"}, "forms": {"validation": {"required_field": "Ce champ est requis", "invalid_email": "<PERSON><PERSON><PERSON> email invalide", "invalid_phone": "Numéro de téléphone invalide", "password_too_short": "Le mot de passe doit contenir au moins 8 caractères", "passwords_dont_match": "Les mots de passe ne correspondent pas", "invalid_format": "Format invalide", "select_at_least_one": "Veuillez sélectionner au moins un élément à supprimer", "no_items_to_delete": "Aucun élément à supprimer", "password_required": "Veuillez entrer votre mot de passe pour confirmer la suppression", "errors": "Erreurs de validation", "fill_required_fields": "Veu<PERSON>z remplir tous les champs obligatoires.", "all_fields_required": "Tous les champs sont obligatoires."}, "placeholders": {"enter_name": "Entrez le nom", "enter_email": "Entrez l'email", "enter_phone": "Entrez le téléphone", "enter_address": "Entrez l'adresse", "select_option": "Sélectionnez une option", "search_placeholder": "Rechercher...", "password": "Mot de passe", "confirm_password": "Confirmer le mot de passe", "full_name": "Nom complet", "role": "R<PERSON><PERSON>", "select_role": "Sélectionner un rôle", "select_school": "Sélectionner une école", "website": "Site web", "principal_name": "Nom du directeur", "established_year": "Année de <PERSON>réation", "password_confirm_delete": "Tapez votre mot de passe pour confirmer la suppression"}}, "messages": {"success": {"saved": "Enregistré avec succès", "deleted": "Supprimé avec succès", "updated": "Mis à jour avec succès", "created": "<PERSON><PERSON><PERSON> avec succès", "sent": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>", "all_items_deleted": "Tous les {type} ont été supprimés avec succès", "items_deleted": "{count} {type} supprimé(s) avec succès", "bulk_delete": "Suppression en lot réussie !", "transaction_saved": "Transaction enregistrée avec succès !", "class_created": "Classe créée avec succès !", "level_created": "Niveau créé avec succès !", "class_deleted": "Classe supprimée avec succès !", "level_deleted": "Niveau de classe supprimé avec succès !", "student_deleted": "Étudiant supprimé avec succès !", "profile_updated": "Profil mis à jour avec succès", "announcement_saved": "L'annonce a été {action} avec succès !", "announcement_deleted": "L'annonce a été supprimée avec succès !"}, "error": {"generic": "Une erreur s'est produite", "network": "<PERSON><PERSON><PERSON> <PERSON>", "unauthorized": "Non autorisé", "forbidden": "Accès interdit", "not_found": "Non trouvé", "server_error": "<PERSON><PERSON><PERSON> du <PERSON>", "invalid_password": "Mot de passe invalide !", "bulk_delete_failed": "La suppression en lot a échoué. Veuillez réessayer.", "loading_data": "Erreur lors du chargement des données", "loading": "Erreur de chargement", "school_id_required": "ID d'école requis", "profile_update_failed": "Échec de la mise à jour du profil", "transaction_save_failed": "Erreur lors de l'enregistrement de la transaction. Veuillez réessayer.", "class_creation_failed": "Une erreur inconnue s'est produite lors de la création de la classe.", "level_creation_failed": "Une erreur inconnue s'est produite lors de la création du niveau de classe.", "class_deletion_failed": "Une erreur inconnue s'est produite lors de la suppression de cette classe.", "level_deletion_failed": "Une erreur inconnue s'est produite lors de la suppression de ce niveau de classe.", "student_deletion_failed": "Une erreur inconnue s'est produite lors de la suppression de l'étudiant.", "import_failed": "Échec de l'importation.", "announcement_save_failed": "Une erreur s'est produite lors de l'enregistrement de l'annonce. Réessayez et si cela persiste, contactez le support.", "announcement_delete_failed": "Une erreur s'est produite lors de la suppression de l'annonce. R<PERSON><PERSON>ez et si cela persiste, contactez le support.", "no_school_associated": "Aucune école associée"}, "confirmation": {"delete": "Êtes-vous sûr de vouloir supprimer cet élément ?", "cancel": "Êtes-vous sûr de vouloir annuler ?", "logout": "Êtes-vous sûr de vouloir vous déconnecter ?"}, "info": {"please_wait": "Veuillez patienter pendant le traitement."}}, "registration": {"title": "Inscription d'Étudiant", "confirming": "Confirmation...", "confirm_payment_register": "Confirmer le Paiement et Inscrire", "steps": {"personal_info": "Informations Personnelles", "personal_info_desc": "Entrez les détails de l'étudiant.", "contact_info": "Informations de Contact", "contact_info_desc": "Fournissez les informations de contact de l'étudiant.", "guardian_details": "<PERSON><PERSON><PERSON> du Parent/<PERSON><PERSON><PERSON>", "guardian_details_desc": "Fournissez les informations du parent/tuteur.", "academic_info": "Informations Académiques", "academic_info_desc": "Détails concernant le parcours académique de l'étudiant.", "emergency_contact": "Contact d'Urgence", "emergency_contact_desc": "<PERSON><PERSON><PERSON>z les informations de contact d'urgence.", "medical_info": "Informations Médicales", "medical_info_desc": "Remplissez tout antécédent médical si applicable.", "consent": "Consentement et Déclaration", "consent_desc": "Acceptez les termes et conditions.", "fee_info": "Informations sur les Frais", "fee_info_desc": "Choisissez la structure des frais et les options de paiement.", "payment_confirmation": "Confirmation de Paiement", "payment_confirmation_desc": "Fournis<PERSON>z une preuve de paiement et confirmation d'inscription."}, "fields": {"student_address": "Adresse de l'Étudiant", "student_phone_optional": "Numéro de Téléphone de l'Étudiant (Optionnel)", "select_class": "Sélectionner une Classe", "previous_school": "École/Institution Précédente", "dob": "Date de Naissance", "search_student": "Rechercher un Étudiant", "search_placeholder": "<PERSON><PERSON><PERSON> le nom ou l'ID de l'étudiant...", "first_name": "Prénom", "last_name": "Nom de Famille", "middle_name": "Deuxième Prénom", "date_of_birth": "Date de Naissance", "nationality": "Nationalité", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Fé<PERSON>n", "select_gender": "Sélectionner le genre", "place_of_birth": "<PERSON><PERSON> de Naissance", "search_guardian": "Rechercher un Tuteur dans le système", "guardian_search_placeholder": "<PERSON><PERSON><PERSON> le nom ou l'email...", "guardian_name": "Nom du Parent/Tuteur", "relationship": "Relation avec l'Étudiant", "mother": "<PERSON><PERSON>", "father": "Père", "brother": "<PERSON><PERSON>", "sister": "<PERSON>œur", "aunty": "<PERSON><PERSON>", "uncle": "Oncle", "grandmother": "Grand-Mère", "grandfather": "Grand-Père", "other": "<PERSON><PERSON>", "select_relationship": "Sélectionner la Relation", "same_address": "<PERSON>ême adresse que l'étudiant", "guardian_phone_required": "Numéro de Téléphone du Tuteur (Obligatoire)", "occupation": "Profession", "transcript_provided": "Cochez la case si des copies de relevés de notes ou bulletins ont été fournies", "same_as_guardian": "Même que les Informations du Tuteur", "health_condition": "État de Santé (Écrivez des notes si l'étudiant a un problème de santé)", "doctor_name": "Nom du Médecin", "doctor_phone_optional": "Numéro de Téléphone du Médecin (Optionnel)", "doctor_phone": "Téléphone du Médecin", "emergency_contact_name": "Nom du Contact d'Urgence", "emergency_contact_phone": "Téléphone du Contact d'Urgence"}, "fees": {"payment_option": "Option de Paiement", "full_payment": "<PERSON><PERSON><PERSON>t", "pay_installments": "Payer par Versements", "apply_scholarship": "Appliquer une Bourse", "scholarship_percentage": "Bourse (%)", "enter_percentage": "Entrez le pourcentage (ex: 20)", "scholarship_applied": "Bourse Appliquée", "total": "Total", "number_installments": "Nombre de Versements", "installment": "Versement", "installments": "Versements", "choose_due_dates": "Choisir les Dates d'Échéance", "installment_amounts": "Montants des Versements", "first_installment_now": "Premier Verse<PERSON> à Payer Maintenant", "select_applicable_fees": "Sélectionner les Frais Applicables", "no_fees_available": "Aucun frais applicable disponible"}, "resources": {"other_school_resources": "Autres Ressources Scolaires", "search_placeholder": "Rechercher des ressources...", "no_resources_found": "<PERSON><PERSON><PERSON> ressource trouvée"}, "summary": {"review_title": "Examiner le Résumé de Soumission", "student_info": "Informations de l'Étudiant", "guardian_info": "Informations du Tuteur", "emergency_contact": "Contact d'Urgence", "medical_info": "Informations Médicales", "none_reported": "Aucun signalé", "fees_resources": "Frais et Ressources", "total_payable": "Total à Payer", "payment_mode": "Mode de Paiement", "payment_dates": "Dates de Paiement", "installment_breakdown": "Répartition des Versements", "consent": "Consentement", "guardian_agreement": "Accord du Tu<PERSON>ur", "agreed": "Accepté", "not_agreed": "Non Accepté", "final_warning": "<PERSON><PERSON><PERSON><PERSON> vous assurer que toutes les informations sont correctes avant de soumettre. Vous ne pourrez pas les modifier par la suite."}, "messages": {"registration_failed": "L'inscription a échoué. Veuillez réessayer.", "registration_successful": "Inscription réussie !", "student_already_exists": "Un étudiant avec le même nom et la même date de naissance existe déjà dans cette école."}, "actions": {"add_new_student": "Cliquez ici si c'est un nouvel élève", "clear_selected_student": "Effacer l’élève sélectionné"}}, "parents": {"manage_invitations": "<PERSON><PERSON><PERSON> les Invitations de Parents", "parent_avatar": "Avatar Parent", "schools": "Écoles", "children": "<PERSON><PERSON><PERSON>", "no_schools": "Aucune École", "unknown_school": "École Inconnue", "no_children": "<PERSON><PERSON><PERSON>", "unknown_student": "Étudiant Inconnu", "add_parent": "Ajouter un Parent", "search_placeholder": "Rechercher des parents...", "no_parents_found": "Aucun parent trouvé.", "parent_invite": "Invitation Parent", "send_invitation": "Envoyer une Invitation", "select_schools": "Sélectionner des écoles", "schools_selected": "{{count}} é<PERSON>le(s) sélectionnée(s)", "select_children": "Sélectionner des enfants", "sending": "Envoi en cours...", "delete_invitation": "Supprimer l'Invitation", "deleting": "Suppression...", "update_parents": "Mettre à Jour les Parents", "search_schools": "Rechercher des écoles...", "search_children": "Rechercher des enfants...", "updating": "Mise à jour...", "update_parent": "Mettre à Jour le Parent", "loading_more": "Chargement de plus de parents...", "reached_end": "Vous avez atteint la fin de la liste !", "messages": {"failed_load_data": "Échec du chargement des données. Veuillez réessayer.", "failed_load_more": "Échec du chargement de plus de parents. Veuillez réessayer.", "registered_successfully": "Parent enregistré avec succès !", "no_parent_selected": "Aucun parent sélectionné pour la suppression ou utilisateur non authentifié.", "deleted_successfully": "Parent supprimé avec succès !", "invitation_sent": "L'invitation a été envoyée avec succès !", "invitation_failed": "Une erreur s'est produite lors de l'envoi de cette invitation. <PERSON><PERSON><PERSON><PERSON> et si cela persiste, contactez le support !", "invitation_deleted": "L'invitation a été supprimée avec succès !", "delete_error": "Une erreur s'est produite lors de la suppression de cette invitation. <PERSON><PERSON><PERSON><PERSON> et si cela persiste, contactez le support !", "enter_password_confirm": "Veuillez entrer votre mot de passe pour confirmer la suppression.", "delete_confirmation": "Êtes-vous sûr de vouloir supprimer cette invitation <strong className=\"font-semibold text-red-500\">{{name}}</strong> ? Cette action est <span className=\"font-semibold\">irréversible</span>.", "enter_password_placeholder": "Entrez votre mot de passe pour confirmer", "parent_updated": "Parent mis à jour avec succès !", "update_error": "Une erreur s'est produite lors de la mise à jour de ce parent. Rées<PERSON>ez et si cela persiste, contactez le support !"}}, "reports": {"global_reports_analytics": "Rapports Globaux et Analytics", "overview_description": "Vue d'ensemble des performances de toutes les écoles", "error_loading_data": "Erreur lors du chargement des données", "this_week": "<PERSON><PERSON> se<PERSON>", "this_month": "<PERSON> mois", "this_quarter": "Ce trimestre", "this_year": "<PERSON><PERSON> an<PERSON>", "export": "Exporter", "total_schools": "Total Écoles", "total_revenue": "<PERSON><PERSON><PERSON>", "popular_plan": "Plan Populaire", "revenue_per_school": "Revenu/École", "active_schools_platform": "Écoles actives sur la plateforme", "revenue_all_schools": "Revenus de toutes les écoles", "most_chosen_plan": "Plan le plus souscrit", "average_revenue_per_school": "Revenu moyen par école", "reports_analytics": "Rapports et Analytics", "no_school_associated": "Aucune école associée", "academic_year_unavailable": "Année académique non disponible", "loading_academic_year": "Chargement de l'année académique...", "loading_data": "Chargement des données...", "analyze_school_performance": "Analysez les performances et l'utilisation de votre école", "student_statistics": "Statistiques des Étudiants", "export_modal": {"missing_school_id": "ID d'école manquant pour l'export", "error": "Erreur lors de l'export", "global_title": "Exporter le Rapport Global", "school_title": "Exporter le Rapport - {{schoolName}}", "school": "École", "format": "Format d'export", "period": "Période", "custom_dates": "Utiliser des dates personnalisées", "start_date": "Date de début", "end_date": "Date de fin", "estimated_size": "<PERSON><PERSON> estim<PERSON>", "success_message": "Export réussi ! Le téléchargement a commencé.", "exporting": "Export en cours..."}}, "analytics": {"schools": "Écoles", "revenue": "<PERSON>en<PERSON>", "global_subscription_overview": "Vue d'ensemble Globale des Souscriptions", "plan_distribution_description": "Distribution des plans à travers toutes les écoles", "showing_demo_data": "Affichage des données de démonstration", "plan_distribution": "Distribution des Plans", "monthly_trends": "Tendances Mensuelles", "subscription_overview": "Vue d'ensemble des Souscriptions", "plan_distribution_trends": "Distribution et tendances des plans", "plan_breakdown": "Répartition par Plan", "monthly_evolution": "Évolution Mensuelle", "credit_usage": "Utilisation des Crédits", "credit_evolution_description": "Évolution de vos crédits au fil du temps", "trend": "Tendance", "balance": "Solde", "used": "Utilisés", "purchased": "Achetés"}, "consent": {"title": "Déclaration de Consentement", "guardian_confirmation": "Le parent ou tuteur présent confirme qu'il est le tuteur légal de l'étudiant nommé dans cette demande. Il affirme que toutes les informations fournies sont vraies et exactes au mieux de sa connaissance.", "understand_accept": "Il comprend et accepte que", "data_processing": "L'école peut collecter, stocker et traiter les données personnelles et étudiantes conformément aux lois sur la protection des données.", "financial_responsibility": "Il est financièrement responsable des frais et accepte le mode de paiement sélectionné (complet ou par versements).", "installment_agreement": "S'il paie par versements, il accepte de payer aux dates d'échéance spécifiées. Le non-respect peut entraîner des pénalités ou des restrictions d'accès.", "policy_amendments": "L'école peut modifier les horaires, frais ou politiques avec un préavis approprié.", "emergency_treatment": "L'école peut chercher un traitement médical d'urgence si le tuteur ne peut être joint.", "school_policies": "Il a lu et accepte de suivre les politiques académiques, comportementales et de sécurité de l'école.", "admin_confirmation": "L'administrateur confirme ci-dessous que le parent/tuteur a examiné cette déclaration et donné son consentement verbal.", "verbal_consent_confirmation": "Je confirme que le parent/tuteur présent a donné son consentement verbal à la déclaration ci-dessus."}, "language": {"french": "Français", "english": "<PERSON><PERSON><PERSON>", "select_language": "Sélectionner la langue", "language_changed": "Langue changée avec succès"}}