/**
 * Script de nettoyage pour l'utilisateur spécifique: <EMAIL>
 * Résout le problème de synchronisation MongoDB/Firebase
 */

const mongoose = require('mongoose');
const { deleteUserCompletely, findAndCleanOrphanUsers, repairMissingFirebaseUIDs } = require('./src/utils/firebaseUserSync');
require('dotenv').config();

const SPECIFIC_EMAIL = '<EMAIL>';

async function cleanupSpecificUser() {
  try {
    console.log('🧹 NETTOYAGE UTILISATEUR SPÉCIFIQUE');
    console.log('='.repeat(50));
    console.log(`📧 Email: ${SPECIFIC_EMAIL}\n`);
    
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie\n');

    // Nettoyer l'utilisateur spécifique
    console.log('🗑️ Suppression complète de l\'utilisateur...');
    const results = await deleteUserCompletely(SPECIFIC_EMAIL, 'email');
    
    console.log('\n📊 RÉSULTATS DE LA SUPPRESSION:');
    console.log('MongoDB:', results.mongodb.success ? '✅ Supprimé' : '❌ Échec');
    if (results.mongodb.user) {
      console.log(`   • Nom: ${results.mongodb.user.name}`);
      console.log(`   • User ID: ${results.mongodb.user.user_id}`);
      console.log(`   • Firebase UID: ${results.mongodb.user.firebaseUid || 'Non défini'}`);
    }
    
    console.log('Firebase:', results.firebase.success ? '✅ Supprimé' : '❌ Échec');
    if (results.firebase.uid) {
      console.log(`   • UID supprimé: ${results.firebase.uid}`);
    }
    if (results.firebase.error) {
      console.log(`   • Erreur: ${results.firebase.error}`);
    }

    // Vérification finale
    console.log('\n🔍 Vérification finale...');
    const finalCheck = await deleteUserCompletely(SPECIFIC_EMAIL, 'email');
    
    if (!finalCheck.mongodb.success && !finalCheck.firebase.success) {
      console.log('✅ Utilisateur complètement supprimé des deux systèmes');
    } else {
      console.log('⚠️ Des traces subsistent encore');
    }

    console.log('\n🎉 NETTOYAGE TERMINÉ !');
    console.log('Vous pouvez maintenant créer un nouvel utilisateur avec cet email.');

  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Connexion MongoDB fermée');
  }
}

async function fullSystemCleanup() {
  try {
    console.log('🔧 NETTOYAGE COMPLET DU SYSTÈME');
    console.log('='.repeat(50));
    
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie\n');

    // 1. Nettoyer l'utilisateur spécifique d'abord
    console.log('1️⃣ Nettoyage de l\'utilisateur spécifique...');
    await deleteUserCompletely(SPECIFIC_EMAIL, 'email');
    
    // 2. Rechercher tous les orphelins
    console.log('\n2️⃣ Recherche d\'utilisateurs orphelins...');
    const orphans = await findAndCleanOrphanUsers();
    
    // 3. Réparer les firebaseUid manquants
    if (orphans.missingUID.length > 0) {
      console.log('\n3️⃣ Réparation des firebaseUid manquants...');
      await repairMissingFirebaseUIDs(orphans);
    }
    
    // 4. Rapport final
    console.log('\n📋 RAPPORT FINAL:');
    console.log('='.repeat(30));
    console.log(`✅ Utilisateur spécifique nettoyé: ${SPECIFIC_EMAIL}`);
    console.log(`✅ Orphelins MongoDB trouvés: ${orphans.mongoOnly.length}`);
    console.log(`✅ FirebaseUID réparés: ${orphans.missingUID.length}`);
    
    console.log('\n💡 RECOMMANDATIONS:');
    console.log('• Les fonctions deleteUserById et deleteMultipleUsers ont été mises à jour');
    console.log('• Elles suppriment maintenant automatiquement de Firebase');
    console.log('• Utilisez ces fonctions pour éviter les problèmes futurs');
    console.log('• En cas de problème, relancez ce script de nettoyage');

  } catch (error) {
    console.error('❌ Erreur lors du nettoyage complet:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Connexion MongoDB fermée');
    console.log('🏁 Nettoyage terminé');
  }
}

// Exécuter selon les arguments
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--full')) {
    fullSystemCleanup()
      .then(() => process.exit(0))
      .catch((error) => {
        console.error('❌ Erreur:', error);
        process.exit(1);
      });
  } else {
    cleanupSpecificUser()
      .then(() => process.exit(0))
      .catch((error) => {
        console.error('❌ Erreur:', error);
        process.exit(1);
      });
  }
}

module.exports = { cleanupSpecificUser, fullSystemCleanup };
