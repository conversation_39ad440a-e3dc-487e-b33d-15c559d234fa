"use client";

import React from "react";
import { User, Settings } from "lucide-react";
import NotificationCard from "@/components/NotificationCard";
import ProfileSettings from "../../super-admin/settings/components/ProfileSettings"; // Reusing ProfileSettings

import { useTranslation } from "@/hooks/useTranslation";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import { useRouter } from "next/navigation";

// Import the useTeacherAdminSettings hook and provider
import {useTeacherAdminSettings} from "@/context/TeacherAdminSettingsContext"; // Adjust path as needed
import useAuth from "@/app/hooks/useAuth"; // Keep useAuth for the Page wrapper
import { useEffect, useState } from "react"; // Keep useState and useEffect for the Page wrapper

const BASE_URL = "/teacher-dashboard";

interface SelectedSchool {
    school_id: string;
    school_name: string;
    access_granted_at: string;
}

// Main TeacherAdminSettingsPageContent component
const TeacherAdminSettingsPageContent: React.FC = () => {
    const { t } = useTranslation();

    // Consume the teacher admin settings context
    const {
        user,
        isLoading,
        profileForm,
        setProfileForm,
        avatarPreviewUrl,
        handleAvatarChange,
        handleProfileSubmit,
        notificationMessage,
        notificationType,
        isNotificationCard,
        setNotification,
        setIsNotificationCard,
        activeTab,
        setActiveTab,
    } = useTeacherAdminSettings();

    if (isLoading)
        return (
            <div className="flex items-center justify-center h-full min-h-[300px]">
                <CircularLoader />
            </div>
        );

    return (
        <>
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    icon={
                        notificationType === "success" ? (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path
                                    d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                                    stroke="#15803d"
                                    strokeWidth="1.5"
                                />
                                <path
                                    d="M7.75 11.9999L10.58 14.8299L16.25 9.16992"
                                    stroke="#15803d"
                                    strokeWidth="1.5"
                                />
                            </svg>
                        ) : (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="10" stroke="#dc2626" strokeWidth="2" />
                                <path
                                    d="M8 8L16 16M16 8L8 16"
                                    stroke="#dc2626"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                />
                            </svg>
                        )
                    }
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                />
            )}

            <div className="flex flex-col md:flex-row gap-8 p-6 w-full max-w-6xl mx-auto">
                <nav className="flex flex-col space-y-2 w-full md:w-1/4">
                    {[
                        { key: "profile", label: t('settings.profile_tab'), icon: <User className="inline mr-2" /> },
                    ].map((tab) => (
                        <button
                            key={tab.key}
                            className={`w-full text-left px-4 py-3 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${activeTab === tab.key
                                    ? "bg-teal text-white shadow-md"
                                    : "bg-background text-foreground hover:bg-background-darker"
                                }`}
                            onClick={() => setActiveTab(tab.key as any)}
                        >
                            {tab.icon} {tab.label}
                        </button>
                    ))}
                </nav>

                <section className="flex-1 w-full md:w-3/4 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
                    {activeTab === "profile" && (
                        <ProfileSettings
                            user={user}
                            profileForm={profileForm}
                            setProfileForm={setProfileForm}
                            avatarPreviewUrl={avatarPreviewUrl}
                            handleAvatarChange={handleAvatarChange}
                            handleProfileSubmit={handleProfileSubmit}
                        />
                    )}
                </section>
            </div>
        </>
    );
};

// Wrapper component to provide the TeacherAdminSettingsContext
// This will be the default export for the page.
export default function TeacherAdminSettingsPage() {
    const { t } = useTranslation();
    const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
    const router = useRouter();
    const { user, logout } = useAuth();

    // Fetch user and selected school
    useEffect(() => {
        if (user) {
            const storedSchool = localStorage.getItem("teacher_selected_school");
            if (storedSchool) {
                const school = JSON.parse(storedSchool);
                setSelectedSchool(school);
            } else {
                router.push("/teacher-dashboard");
            }
        } else {
            router.push("/teacher-dashboard");
        }
    }, [user, router]);

    const navigation = {
        icon: Settings,
        baseHref: `${BASE_URL}/settings`,
        title: t('navigation.settings'),
    };

    const handleSchoolChange = () => {
        localStorage.removeItem("teacher_selected_school");
        router.push("/teacher-dashboard");
    };

    return (
            <TeacherLayout
                navigation={navigation}
                selectedSchool={selectedSchool ? {
                    _id: selectedSchool.school_id,
                    name: selectedSchool.school_name
                } : null}
                onSchoolChange={handleSchoolChange}
                onLogout={logout}
            >
                <TeacherAdminSettingsPageContent />
            </TeacherLayout>
    );
}
