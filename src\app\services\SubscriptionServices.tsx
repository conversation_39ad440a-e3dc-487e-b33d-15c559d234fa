import {
  SubscriptionPlanSchema,
  SchoolSubscriptionSchema,
  CreditPurchaseSchema,
  CreditPurchaseFormSchema
} from '@/app/models/SchoolSubscriptionModel';
import { getTokenFromCookie } from './UserServices';
import { BASE_API_URL } from './AuthContext';

// Données mock pour les plans de souscription
const MOCK_PLANS: SubscriptionPlanSchema[] = [
  {
    _id: 'plan_basic_001',
    plan_name: 'basic',
    display_name: 'Plan Basic',
    description: 'Parfait pour débuter avec la gestion scolaire',
    price_per_credit: 3000,
    currency: 'FCFA',
    chatbot_enabled: false,
    features: [
      {
        name: 'student_management',
        description: 'Inscription, profils et suivi des étudiants',
        enabled: true
      },
      {
        name: 'class_management',
        description: 'Organisation des classes et groupes',
        enabled: true
      },
      {
        name: 'attendance_tracking',
        description: 'Pointage et rapports de présence',
        enabled: true
      },
      {
        name: 'grade_management',
        description: 'Saisie et calcul des notes',
        enabled: true
      },
      {
        name: 'timetable_management',
        description: 'Création et gestion des emplois du temps',
        enabled: true
      }
    ],
    benefits: [
      'Gestion complète des étudiants',
      'Suivi des présences et notes',
      'Emplois du temps flexibles',
      'Rapports de base'
    ],
    limitations: [
      'Pas de chatbot IA',
      'Support par email uniquement',
      'Stockage limité à 5GB'
    ],
    is_active: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  {
    _id: 'plan_custom_002',
    plan_name: 'custom',
    display_name: 'Plan custom',
    description: 'Solution complète avec IA pour une gestion avancée',
    price_per_credit: 2500,
    currency: 'FCFA',
    chatbot_enabled: true,
    features: [
      {
        name: 'student_management',
        description: 'Inscription, profils et suivi des étudiants',
        enabled: true
      },
      {
        name: 'class_management',
        description: 'Organisation des classes et groupes',
        enabled: true
      },
      {
        name: 'attendance_tracking',
        description: 'Pointage et rapports de présence',
        enabled: true
      },
      {
        name: 'grade_management',
        description: 'Saisie et calcul des notes',
        enabled: true
      },
      {
        name: 'timetable_management',
        description: 'Création et gestion des emplois du temps',
        enabled: true
      },
      {
        name: 'ai_chatbot',
        description: 'Assistant IA pour répondre aux questions',
        enabled: true
      },
      {
        name: 'advanced_analytics',
        description: 'Analyses et rapports avancés',
        enabled: true
      },
      {
        name: 'parent_portal',
        description: 'Portail parents avec notifications',
        enabled: true
      }
    ],
    benefits: [
      'Toutes les fonctionnalités Basic',
      'Assistant IA intégré',
      'Analyses avancées',
      'Portail parents',
      'Support prioritaire',
      'Stockage illimité'
    ],
    limitations: [],
    is_active: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  }
];

// Types pour les réponses API
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

interface PriceCalculation {
  credits_amount: number;
  unit_price: number;
  price_per_credit: number;
  subtotal: number;
  discount_percentage: number;
  discount_amount: number;
  total_amount: number;
  total: number;
  currency: string;
}

// Configuration des headers pour les requêtes API
const getAuthHeaders = () => {
  const token = getTokenFromCookie("idToken");
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Services pour les plans de souscription
export const getSubscriptionPlans = async (): Promise<SubscriptionPlanSchema[]> => {
  try {
    const response = await fetch(`${BASE_API_URL}/subscription-plans`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      console.warn('API call failed, using mock data');
      return MOCK_PLANS;
    }

    const result: ApiResponse<SubscriptionPlanSchema[]> = await response.json();
    return (result.data ?? MOCK_PLANS);
    } catch (error) {
    console.error('Error fetching subscription plans:', error);
    return MOCK_PLANS;
  }
};

export const getSubscriptionPlanById = async (planId: string): Promise<SubscriptionPlanSchema | null> => {
  try {
    const response = await fetch(`${BASE_API_URL}/subscription-plans/${planId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      const mockPlan = MOCK_PLANS.find(plan => plan._id === planId);
      return mockPlan || null;
    }

    const result: ApiResponse<SubscriptionPlanSchema> = await response.json();
    return result.data || null;
  } catch (error) {
    console.error('Error fetching subscription plan:', error);
    const mockPlan = MOCK_PLANS.find(plan => plan._id === planId);
    return mockPlan || null;
  }
};

// Services pour les souscriptions d'école
export const getSchoolSubscription = async (schoolId: string): Promise<SchoolSubscriptionSchema | null> => {
  try {
    const response = await fetch(`${BASE_API_URL}/school-subscription/${schoolId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    // Le backend retourne { subscription: {...}, message: "..." }
    return result.subscription || null;
  } catch (error) {
    console.error('Error fetching school subscription:', error);
    return null;
  }
};

export const updateSchoolSubscription = async (
  schoolId: string, 
  subscriptionData: Partial<SchoolSubscriptionSchema>
): Promise<SchoolSubscriptionSchema | null> => {
  try {
    const response = await fetch(`${BASE_API_URL}/school-subscription/${schoolId}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(subscriptionData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<SchoolSubscriptionSchema> = await response.json();
    return result.data || null;
  } catch (error) {
    console.error('Error updating school subscription:', error);
    return null;
  }
};

// Services pour le calcul de prix
export const calculatePrice = async (
  planName: string, 
  creditsAmount: number
): Promise<{ calculation: PriceCalculation }> => {
  try {
    const response = await fetch(`${BASE_API_URL}/subscription-plans/pricing/calculate?plan_name=${planName}&credits_amount=${creditsAmount}`, {
      method: 'get',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error calculating price:', error);
    
    // Fallback calculation
    const plan = MOCK_PLANS.find(p => p.plan_name === planName);
    const unitPrice = plan?.price_per_credit || 3000;
    const subtotal = creditsAmount * unitPrice;
    
    // Simple discount logic
    let discountPercentage = 0;
    if (creditsAmount >= 100) discountPercentage = 15;
    else if (creditsAmount >= 50) discountPercentage = 10;
    else if (creditsAmount >= 20) discountPercentage = 5;
    
    const discountAmount = (subtotal * discountPercentage) / 100;
    const totalAmount = subtotal - discountAmount;

    return {
      calculation: {
        credits_amount: creditsAmount,
        price_per_credit: unitPrice,
        unit_price: unitPrice,
        subtotal,
        discount_percentage: discountPercentage,
        discount_amount: discountAmount,
        total: totalAmount,
        total_amount: totalAmount,
        currency: 'FCFA'
      }
    };
  }
};

// Services pour l'achat de crédits
export const initiateCreditPurchase = async (purchaseData: CreditPurchaseFormSchema): Promise<any> => {
  try {
    const response = await fetch(`${BASE_API_URL}/credit-purchase/initiate`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(purchaseData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error initiating credit purchase:', error);
    throw error;
  }
};

export const checkCreditPurchaseStatus = async (transactionId: string): Promise<any> => {
  try {
    console.log('🔍 Appel API pour vérifier le statut:', `${BASE_API_URL}/credit-purchase/payment/${transactionId}/status`);

    const response = await fetch(`${BASE_API_URL}/credit-purchase/payment/${transactionId}/status`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    console.log('📡 Réponse HTTP:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Erreur HTTP:', errorText);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('📦 Données reçues:', data);
    return data;
  } catch (error) {
    console.error('Error checking credit purchase status:', error);
    throw error;
  }
};

// Fonction alternative utilisant la route school-credit-payment
export const checkSchoolCreditPaymentStatus = async (transactionId: string): Promise<any> => {
  try {
    console.log('🔍 Appel API alternatif:', `${BASE_API_URL}/school-credit-payment/status/${transactionId}`);

    const response = await fetch(`${BASE_API_URL}/school-credit-payment/status/${transactionId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    console.log('📡 Réponse HTTP (alternatif):', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Erreur HTTP (alternatif):', errorText);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('📦 Données reçues (alternatif):', data);
    return data;
  } catch (error) {
    console.error('Error checking school credit payment status:', error);
    throw error;
  }
};

// Récupérer l'historique des achats de crédits d'une école
export const getCreditPurchaseHistory = async (schoolId: string, limit: number = 20, skip: number = 0): Promise<any> => {
  try {
    const response = await fetch(`${BASE_API_URL}/credit-purchase/school/${schoolId}/history?limit=${limit}&skip=${skip}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching credit purchase history:', error);
    throw error;
  }
};

// Récupérer les achats de crédits en attente d'une école
export const getPendingCreditPurchases = async (schoolId: string): Promise<CreditPurchaseSchema[]> => {
  try {
    const history = await getCreditPurchaseHistory(schoolId, 50, 0);

    // Filtrer les achats en attente
    const pendingPurchases = history.purchases?.filter((purchase: CreditPurchaseSchema) =>
      purchase.payment_status === 'pending'
    ) || [];

    return pendingPurchases;
  } catch (error) {
    console.error('Error fetching pending credit purchases:', error);
    return [];
  }
};

// Utilitaires
export const formatCurrency = (amount: number, currency: string = 'FCFA'): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: currency === 'FCFA' ? 'XAF' : currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export const getDiscountText = (creditsAmount: number): string => {
  if (creditsAmount >= 100) return '15% de réduction';
  if (creditsAmount >= 50) return '10% de réduction';
  if (creditsAmount >= 20) return '5% de réduction';
  return '';
};

export const formatCredits = (amount: number): string => {
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K`;
  }
  return amount.toString();
};

// Services pour les statistiques et rapports
export const getCreditUsageStats = async (schoolId: string, period?: string): Promise<any> => {
  try {
    const url = period
      ? `${BASE_API_URL}/school-subscription/${schoolId}/stats?period=${period}`
      : `${BASE_API_URL}/school-subscription/${schoolId}/stats`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching credit usage stats:', error);
    throw error;
  }
};

// Alias pour getSubscriptionStats (compatibilité)
export const getSubscriptionStats = getCreditUsageStats;

export const deductCredits = async (schoolId: string, amount: number, reason: string): Promise<any> => {
  try {
    const response = await fetch(`${BASE_API_URL}/school-subscription/${schoolId}/credits/deduct`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ amount, reason })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deducting credits:', error);
    throw error;
  }
};

// Interfaces pour les analytics
export interface UsageAnalyticsData {
  date: string;
  credits_purchased: number;
  credits_used: number;
  credits_balance: number;
  students_registered: number;
}

export interface UsageAnalyticsResponse {
  school_id: string;
  period: string;
  data: UsageAnalyticsData[];
  summary: {
    total_purchased: number;
    total_used: number;
    current_balance: number;
    total_students: number;
    total_revenue: number;
  };
}

export interface SubscriptionDistributionData {
  plan_type: string;
  count: number;
  revenue: number;
  avg_credits: number;
  color: string;
}

export interface MonthlyTrendData {
  month: string;
  basic: number;
  premium: number;
  enterprise: number;
  revenue: number;
}

export interface SubscriptionDistributionResponse {
  distribution: SubscriptionDistributionData[];
  monthly_trends: MonthlyTrendData[];
  summary: {
    total_subscriptions: number;
    total_revenue: number;
    most_popular_plan: string;
  };
}

// Récupérer les données d'utilisation des crédits pour les graphiques
export async function getCreditUsageAnalytics(
  schoolId: string,
  period: 'week' | 'month' | 'quarter' | 'year' = 'month'
): Promise<UsageAnalyticsResponse> {
  const response = await fetch(
    `${BASE_API_URL}/school-subscription/${schoolId}/analytics/usage?period=${period}`,
    {
      method: 'GET',
      headers: getAuthHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error('Failed to fetch credit usage analytics');
  }

  return response.json();
}

// Récupérer la distribution des souscriptions
export async function getSubscriptionDistribution(schoolId?: string): Promise<SubscriptionDistributionResponse> {
  const url = schoolId
    ? `${BASE_API_URL}/school-subscription/${schoolId}/analytics/distribution`
    : `${BASE_API_URL}/school-subscription/analytics/distribution`;

  const response = await fetch(url, {
    method: 'GET',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error('Failed to fetch subscription distribution');
  }

  return response.json();
}

// Interface pour les types de plans
export interface PlanType {
  name: string;
  displayName: string;
  color: string;
}

// Récupérer les types de plans de souscription disponibles
export async function getSubscriptionPlanTypes(): Promise<PlanType[]> {
  try {
    const response = await fetch(`${BASE_API_URL}/subscription-plans/types/list`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch plan types: ${response.status}`);
    }

    const data = await response.json();
    return data.planTypes || [];
  } catch (error: any) {
    console.error('Error fetching subscription plan types:', error);
    // Fallback vers des types par défaut en cas d'erreur
    return [
      { name: 'basic', displayName: 'Basic', color: '#3B82F6' },
      { name: 'standard', displayName: 'Standard', color: '#10B981' },
      { name: 'custom', displayName: 'Custom', color: '#F59E0B' }
    ];
  }
}

// Interface pour les informations de l'école
export interface SchoolInfo {
  name: string;
  plan_type: string;
  credits_balance: number;
  subscription_status: string;
}

// Récupérer les informations de l'école via la route existante
export async function getSchoolInfo(schoolId: string): Promise<SchoolInfo> {
  try {
    // Utiliser la route existante getSchoolSubscription qui populate déjà les infos de l'école
    const subscription = await getSchoolSubscription(schoolId);

    if (subscription && subscription.school_id) {
      return {
        name: (subscription.school_id as any)?.name || 'Mon École',
        plan_type: subscription.plan_type || 'basic',
        credits_balance: subscription.credits_balance || 0,
        subscription_status: subscription.status || 'active'
      };
    }

    // Fallback si pas de souscription
    return {
      name: 'Mon École',
      plan_type: 'basic',
      credits_balance: 0,
      subscription_status: 'active'
    };
  } catch (error: any) {
    console.error('Error fetching school info:', error);
    // Fallback vers des données par défaut en cas d'erreur
    return {
      name: 'Mon École',
      plan_type: 'basic',
      credits_balance: 0,
      subscription_status: 'active'
    };
  }
}
