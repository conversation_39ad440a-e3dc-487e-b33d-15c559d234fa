"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Loader2 } from 'lucide-react';
import { ExamTypeSchema, ExamTypeCreateSchema, ExamTypeUpdateSchema } from '@/app/models/ExampType';
import useAuth from '@/app/hooks/useAuth';
import { useTranslation } from '@/hooks/useTranslation';

interface ExamTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ExamTypeCreateSchema | ExamTypeUpdateSchema) => Promise<void>;
  examType?: ExamTypeSchema | null;
  isSubmitting?: boolean;
}

const ExamTypeModal: React.FC<ExamTypeModalProps> = ({
  isOpen,
  onClose,
  onSave,
  examType,
  isSubmitting = false
}) => {
  const { user } = useAuth();
  const { t, tDashboard } = useTranslation();
  const [formData, setFormData] = useState({
    type: '',
    school_id: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when modal opens or examType changes
  useEffect(() => {
    if (isOpen) {
      if (examType) {
        // Edit mode
        setFormData({
          type: examType.type || '',
          school_id: examType.school_id || ''
        });
      } else {
        // Create mode
        setFormData({
          type: '',
          school_id: user?.school_ids?.[0] || ''
        });
      }
      setErrors({});
    }
  }, [isOpen, examType, user]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.type.trim()) {
      newErrors.type = tDashboard('school-admin', 'examtype', 'exam_type_required');
    }

    if (!formData.school_id.trim()) {
      newErrors.school_id = tDashboard('school-admin', 'examtype', 'school_id_required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (examType) {
        // Update mode
        const updateData: ExamTypeUpdateSchema = {
          _id: examType._id,
          type: formData.type,
          school_id: formData.school_id
        };
        await onSave(updateData);
      } else {
        // Create mode
        const createData: ExamTypeCreateSchema = {
          type: formData.type,
          school_id: formData.school_id
        };
        await onSave(createData);
      }
    } catch (error) {
      console.error('Error saving exam type:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative bg-widget rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <h2 className="text-xl font-semibold text-text">
              {examType ? tDashboard('school-admin', 'examtype', 'edit_exam_type_modal') : tDashboard('school-admin', 'examtype', 'add_exam_type_modal')}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              disabled={isSubmitting}
            >
              <X className="w-5 h-5 text-text" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            {/* Exam Type Field */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-text mb-2">
                {tDashboard('school-admin', 'examtype', 'exam_type')} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="type"
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 bg-widget text-text ${
                  errors.type ? 'border-red-500' : 'border-stroke'
                }`}
                placeholder={tDashboard('school-admin', 'examtype', 'exam_type_placeholder')}
                disabled={isSubmitting}
              />
              {errors.type && (
                <p className="mt-1 text-sm text-red-500">{errors.type}</p>
              )}
            </div>

            {/* School ID Field (hidden, auto-filled) */}
            <input
              type="hidden"
              value={formData.school_id}
            />

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-text bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                disabled={isSubmitting}
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-teal hover:bg-teal-600 rounded-lg transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isSubmitting ? t('common.saving') : t('common.save')}</span>
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ExamTypeModal;
