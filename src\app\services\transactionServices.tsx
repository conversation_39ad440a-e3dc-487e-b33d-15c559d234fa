import { BASE_API_URL } from "./AuthContext";
import { PayementSchema } from "../models/PayementModel";

export async function initiateTransaction(payementData: PayementSchema) {

    try {
        const response = await fetch(`${BASE_API_URL}/payment/Initiatepay`,
            {
                method: 'POST',
                body: JSON.stringify(payementData),
                headers: {
                    'Content-Type': 'application/json'
                },
            }
        )
        if (!response.ok) {
            throw new Error("Failed to initiate transaction");
        }
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error initiating transaction:", error);
        throw new Error("Failed to initiate transaction");
    }
}   