{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "yes": "Yes", "no": "No", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "select": "Select", "clear": "Clear", "reset": "Reset", "apply": "Apply", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "time": "Time", "description": "Description", "total": "Total", "amount": "Amount", "price": "Price", "quantity": "Quantity", "example_title": "Example: {title}", "unknown": "Unknown", "deleting": "Deleting...", "continue": "Continue", "notification": "Notification", "update": "Update", "create": "Create", "retry": "Retry", "sending": "Sending...", "view_all": "View all", "manage": "Manage", "not_available": "N/A", "gender": "Gender", "mandatory": "mandatory", "full_name": "Full Name", "phone_number": "Phone Number", "transaction_id": "Transaction ID", "search_placeholder": "Search...", "no_results_found": "No results found", "load_more": "Load more", "go_back": "Go back", "user_id": "User ID", "role": "Role", "payment_status": {"completed": "Completed", "pending": "Pending", "failed": "Failed", "refunded": "Refunded", "cancelled": "Cancelled", "unknown": "Unknown"}, "credits": "Credits", "showing_count": "Showing {filtered} of {total} {type}", "created": "created", "updated": "updated", "view_plans": "View Plans", "filters": "Filters", "clear_filters": "Clear Filters", "all_terms": "All Terms", "all_sequences": "All Sequences", "term": "Term", "sequence": "Sequence", "no_data_available": "No data available", "since_last_month": "Since last month", "average_grade": "Average Grade", "number_of_students": "Number of Students", "saving": "Saving...", "missing_required_information": "Missing required information"}, "navigation": {"dashboard": "Dashboard", "schools": "Schools", "users": "Users", "students": "Students", "teachers": "Teachers", "parents": "Parents", "classes": "Classes", "subjects": "Subjects", "grades": "Grades", "attendance": "Attendance", "schedule": "Schedule", "reports": "Reports", "settings": "Settings", "profile": "Profile", "logout": "Logout", "notifications": "Notifications", "credits": "Credits", "subscriptions": "Subscriptions", "payments": "Payments", "analytics": "Analytics", "refunds": "Refunds", "school": "School", "staff": "Staff", "people_management": "People Management", "academic_records": "Academic Records", "terms": "Terms", "timetable": "Timetable", "teacher_assignment": "Teacher Assignment", "periods": "Periods", "exam_types": "Exam Types", "discipline": "Discipline", "communications": "Communications", "announcements": "Announcements", "resources": "Resources", "financial": "Financial", "fee_types": "Fee Types", "fee_transactions": "Fee Transactions", "buy_credit": "Buy Credit", "reports_analytics": "Reports & Analytics"}, "dashboard": {"top_classes_performance": "Top Classes Performance", "performance_overview_scale": "Performance overview (Scale: 0-20)", "priority_announcements": "Priority Announcements", "no_announcements_available": "No announcements available", "create_first_announcement": "Create your first announcement", "failed_to_load_announcements": "Failed to load announcements", "super-admin": {"pages": {"dashboard": {"title": "Super Admin Dashboard", "welcome": "Welcome, {name}", "overview": "Overview", "statistics": "Statistics", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "total_schools": "Total Schools", "total_students": "Total Students", "total_teachers": "Total Teachers", "total_users": "Total Users", "active_subscriptions": "Active Subscriptions", "revenue": "Revenue (XAF)", "growth": "Growth"}, "schools": {"title": "School Management", "add_school": "Add School", "edit_school": "Edit School", "school_name": "School Name", "school_code": "School Code", "school_type": "School Type", "contact_person": "Contact Person", "subscription_plan": "Subscription Plan", "registration_date": "Registration Date", "last_activity": "Last Activity", "view_details": "View Details", "manage_credits": "Manage Credits", "view_analytics": "View Analytics", "principal_name": "Principal Name", "established_year": "Established Year", "website": "Website"}, "credits": {"title": "Credit Management", "available_credits": "Available Credits", "used_credits": "Used Credits", "total_credits": "Total Credits", "credit_history": "Credit History", "purchase_credits": "Purchase Credits", "credit_usage": "Credit Usage", "remaining_credits": "Remaining Credits", "credit_balance": "Credit Balance", "per_student": "per student", "per_message": "per message", "overview": "Overview of credits and analytics by school", "search_placeholder": "Search by school name or address...", "balance": "Balance", "revenue": "Revenue", "efficiency": "Efficiency", "remaining": "Remaining", "view_details": "View Details", "manage_title": "Credit Details", "manage_description": "Credit management and detailed analytics", "missing_school_id": "Missing school ID", "select_school_message": "Please select a school to view its details.", "back_to_list": "Back to list", "status": "Status", "current_balance": "Current Balance", "since_beginning": "Since beginning", "average_revenue_per_student": "Average revenue per student", "autonomy": "Autonomy", "estimated_days_remaining": "Estimated days remaining", "usage_metrics": "Usage Metrics", "registered_students": "Registered Students", "purchased_credits": "Purchased Credits", "purchase_count": "Purchase Count", "average_purchase": "Average Purchase", "recent_transactions": "Recent Transactions", "no_transactions_found": "No transactions found", "all_transactions": "All Transactions", "subscription_details": "Subscription Details", "plan_type": "Plan Type", "start_date": "Start Date", "activated": "activated", "new_transaction": "New Credit Transaction", "academic_year": "Academic Year", "select_academic_year": "Select academic year", "payment_method": "Payment Method", "amount_paid": "Amount <PERSON>", "send_credit": "Send Credit", "total_revenue": "Total Revenue"}, "subscriptions": {"title": "Subscription Management", "plan_basic": "Basic", "plan_standard": "Standard", "plan_custom": "Custom", "plan_features": "Plan Features", "subscription_status": "Subscription Status", "renewal_date": "Renewal Date", "upgrade_plan": "Upgrade Plan", "downgrade_plan": "Downgrade Plan", "cancel_subscription": "Cancel Subscription"}, "settings": {"title": "Settings", "profile_tab": "Profile Settings", "credit_tab": "Credit Settings", "general_tab": "General Settings", "profile_title": "Profile Settings", "change_avatar": "Change Avatar", "update_profile": "Update Profile", "credit_title": "Credit Settings", "resell_price_per_credit": "Resell Price per Credit", "buy_price_per_credit": "Buy Price per Credit", "update_credit_settings": "Update Credit Settings", "general_title": "General Settings", "platform_name": "Platform Name", "support_email": "Support Email", "default_language": "Default Language", "maintenance_mode": "Maintenance Mode", "maintenance_message": "Maintenance Message", "maintenance_message_placeholder": "We'll be back soon!", "update_general_settings": "Update General Settings", "credit_updated": "Credit settings updated successfully", "credit_update_failed": "Failed to update credit settings", "general_updated": "General settings updated successfully", "general_update_failed": "Failed to update general settings"}, "reports": {"title": "Reports", "generate_report": "Generate Report", "export_data": "Export Data", "date_range": "Date Range", "report_type": "Report Type"}, "users": {"title": "User Management", "add_user": "Add User", "edit_user": "Edit User", "user_role": "User Role", "user_status": "User Status", "last_login": "Last Login", "account_created": "Account Created", "all_roles": "All Roles", "filter_by_role": "Filter by Role", "user_details": "User Details", "delete_user": "Delete User", "bulk_delete": "Bulk Delete", "selected_users": "Selected Users"}, "parents": {"title": "Parent Management", "invite_parent": "<PERSON><PERSON><PERSON>", "parent_details": "Parent Details", "children": "Children", "contact_info": "Contact Information"}, "refunds": {"title": "Refund Management", "refund_request": "Refund Request", "refund_status": "Refund Status", "refund_amount": "Refund Amount", "process_refund": "Process Refund", "refund_reason": "Refund Reason"}, "subscription-plans": {"title": "Subscription Plans", "description": "Manage subscription plans and their features", "create_plan": "Create Plan", "edit_plan": "Edit Plan", "plan_details": "Plan Details", "plan_features": "Plan Features", "plan_pricing": "Plan Pricing", "active_plans": "Active Plans", "new_plan": "New Plan", "search_placeholder": "Search by name, description...", "all_plans": "All plans", "active": "Active", "inactive": "Inactive", "popular": "Popular", "total_plans": "Total plans", "popular_plans": "Popular plans", "with_chatbot": "With chatbot", "no_plans_found": "No plans found", "no_plans_match_criteria": "No plans match your search criteria.", "create_first_plan": "Start by creating your first subscription plan.", "min": "Min", "max": "Max", "chatbot": "<PERSON><PERSON><PERSON>", "features": "features", "order": "Order", "basic_info": "Basic Information", "display_name": "Display Name", "display_name_placeholder": "Basic Plan", "technical_name": "Technical Name", "technical_name_placeholder": "basic (auto-generated if empty)", "description_placeholder": "Plan description...", "pricing": "Pricing", "price_per_credit": "Price per credit (XAF)", "minimum_purchase": "Minimum purchase", "maximum_purchase": "Maximum purchase", "unlimited": "Unlimited", "enable_chatbot": "Enable chatbot for this plan", "chatbot_credits_per_purchase": "Chatbot credits per purchase", "feature_placeholder": "Feature...", "options": "Options", "recommended_for": "Recommended for", "recommended_for_placeholder": "Small schools (1-100 students)", "display_order": "Display order", "active_plan": "Active plan", "popular_plan": "Popular plan", "contact_required": "Contact required", "updating_plan": "Updating plan...", "creating_plan": "Creating plan...", "plan_updated": "Plan updated!", "plan_created": "Plan created!", "plan_updated_success": "The subscription plan has been successfully updated.", "plan_created_success": "The subscription plan has been successfully created."}, "classes": {"title": "Class Management", "search_placeholder": "Search schools by name or address...", "no_schools_found": "No schools found matching your search criteria.", "manage_classes": "Manage Classes", "manage_classes_of": "Manage Classes of", "class_level": "Class Level", "class_name": "Class Name", "class_code": "Class Code", "class_level_name": "Class Level Name", "add_new_class": "Add New Class", "add_new_level": "Add New Level", "all_class_levels": "All Class Levels", "edit_class": "Edit Class", "edit_level": "Edit Class Level", "select_level": "Select a level", "level_name_required": "Class Level name is required."}, "students": {"title": "Student Management", "school_id": "School ID", "school_name": "School Name", "student_id": "Student ID", "student_name": "Student Name", "birthday": "Birthday", "place_of_birth": "Place of Birth", "class_level": "Class Level", "parent_name": "Parent(s) Name", "registered": "Registered", "no_class": "No class", "no_guardian": "No guardian", "manage_students": "Manage Students", "manage_students_of": "Manage Students of", "popup_blocked_message": "Popup blocked! Please allow popups to view the student list.", "import_complete": "Import complete: {successful}/{total} students imported.", "register_student": "Register A Student", "upload_csv": "Upload CSV List", "print_student_list": "Print Student List", "print_id_cards": "Print ID Cards"}}}, "school-admin": {"pages": {"dashboard": {"title": "School Dashboard", "welcome": "Welcome, {name}", "overview": "School Overview", "statistics": "School Statistics", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "total_students": "Total Students", "total_teachers": "Total Teachers", "total_classes": "Total Classes", "attendance_rate": "Attendance Rate", "academic_performance": "Academic Performance", "current_credit_balance": "Current Credit Balance", "no_school_associated": "No school associated with this account"}, "school": {"title": "School Information", "school_details": "School Details", "school_name": "School Name", "school_address": "School Address", "contact_info": "Contact Information", "edit_school": "Edit School"}, "announcements": {"title": "Announcements", "page_title": "Announcements", "page_subtitle": "Manage school announcements and communications", "create_announcement": "Create Announcement", "add_new_announcement": "Add New Announcement", "edit_announcement": "Edit Announcement", "delete_announcement": "Delete Announcement", "announcement_title": "Announcement Title", "announcement_content": "Announcement Content", "publish_date": "Publish Date", "target_audience": "Target Audience", "priority": "Priority", "status": "Status", "published": "Published", "draft": "Draft", "archived": "Archived", "search_placeholder": "Search announcements...", "filters": "Filters", "selected": "selected", "delete_selected": "Delete Selected", "all_priorities": "All Priorities", "urgent": "<PERSON><PERSON>", "high": "High", "medium": "Medium", "low": "Low", "all_status": "All Status", "all_audiences": "All Audiences", "everyone": "Everyone", "teachers": "Teachers", "parents": "Parents", "students": "Students", "clear_filters": "Clear Filters", "no_announcements_found": "No announcements found", "get_started_message": "Get started by creating your first announcement.", "adjust_filters_message": "Try adjusting your search or filter criteria.", "create_first_announcement": "Create First Announcement", "showing_count": "Showing {filtered} of {total} announcements", "delete_all": "Delete All", "content_required": "Content", "content_placeholder": "Enter announcement content", "expires_at": "Expires At", "expires_at_optional": "Expires At (Optional)", "publish_immediately": "Publish Immediately", "save_as_draft": "Save as Draft", "cancel": "Cancel", "create": "Create", "update": "Update", "create_announcement_action": "Create Announcement", "update_announcement_action": "Update Announcement", "delete_confirmation_title": "Delete Announcement", "delete_confirmation_message": "Are you sure you want to delete this announcement?", "delete_warning": "This action cannot be undone.", "continue": "Continue", "enter_password_confirm": "Please enter your password to confirm the deletion of:", "password_placeholder": "Enter your password", "delete": "Delete", "delete_success": "Announcement has been deleted successfully!", "delete_error": "There was an error trying to delete the announcement. Try again and if this persists, contact support.", "password_required_alert": "Please enter your password to confirm deletion.", "delete_all_title": "Delete All Announcements", "delete_selected_title": "Delete Selected Announcements", "delete_all_message": "Are you sure you want to delete all announcements? This action cannot be undone.", "delete_selected_message": "Are you sure you want to delete the selected announcements? This action cannot be undone.", "all": "All", "view_details": "Announcement Details", "back_to_announcements": "Back to Announcements", "no_announcement_id": "No announcement ID provided", "failed_to_load": "Failed to load announcement details", "announcement_not_found": "Announcement not found", "priority_label": "{priority} Priority", "target_audience_label": "Target Audience", "created_label": "Created", "published_label": "Published", "expires_label": "Expires", "content_title": "Content", "expired_warning": "This announcement has expired on {date}"}, "terms": {"title": "Terms Management", "add_term": "Add Term", "edit_term": "Edit Term", "delete_term": "Delete Term", "term_name": "Term Name", "start_date": "Start Date", "end_date": "End Date", "current_term": "Current Term", "active": "Active", "inactive": "Inactive", "set_as_current": "Set as Current", "last_term": "Last Term"}, "discipline": {"title": "Discipline Records", "add_record": "Add Record", "edit_record": "Edit Record", "delete_record": "Delete Record", "student_name": "Student Name", "incident_type": "Incident Type", "incident_date": "Incident Date", "description": "Description", "action_taken": "Action Taken", "severity": "Severity", "low": "Low", "medium": "Medium", "high": "High", "discipline_records_management": "Discipline Records Management", "discipline_id": "Discipline ID", "student": "Student", "comments": "Comments", "created_at": "Created At", "unknown_student": "Unknown Student", "no_comments": "No comments", "invalid_date": "Invalid Date", "add_new_discipline_record": "Add New Discipline Record", "failed_to_fetch": "Failed to fetch discipline records", "record_updated_successfully": "Discipline record updated successfully", "record_created_successfully": "Discipline record created successfully", "failed_to_save": "Failed to save discipline record", "user_email_not_found": "User email not found", "record_deleted_successfully": "Discipline record deleted successfully", "records_deleted_successfully": "{count} discipline records deleted successfully", "invalid_password": "Invalid password", "failed_to_delete": "Failed to delete discipline record(s)", "delete_discipline_record": "Delete Discipline Record", "delete_selected_records": "Delete Selected Discipline Records", "delete_confirmation_single": "Are you sure you want to delete the discipline record \"{disciplineId}\"? This action cannot be undone.", "delete_confirmation_multiple": "Are you sure you want to delete {count} selected discipline records? This action cannot be undone.", "student_required": "Student is required", "school_id_required": "School ID is required", "edit_discipline_record": "Edit Discipline Record", "add_discipline_record": "Add New Discipline Record", "select_student": "Select a student", "comments_placeholder": "Enter discipline details and comments..."}, "reports": {"title": "Reports & Analytics", "generate_report": "Generate Report", "student_reports": "Student Reports", "financial_reports": "Financial Reports", "attendance_reports": "Attendance Reports", "academic_reports": "Academic Reports", "export_data": "Export Data"}, "buy_credit": {"title": "Buy Credit", "page_title": "Credit Management", "page_subtitle": "Manage your subscription and purchase credits for your school", "current_balance": "Current Balance", "purchase_credits": "Purchase Credits", "credit_packages": "Credit Packages", "transaction_history": "Transaction History", "usage_analytics": "Usage Analytics", "error_title": "Error", "subscription_not_found": "Subscription data not found", "student_stats_error": "Error loading student statistics", "retry": "Retry", "debug_test": "Debug Test", "view_history": "View History", "buy_more_credits": "Buy More Credits", "available_credits": "Available Credits", "credits_used": "Credits Used", "total_purchased": "Total Purchased", "efficiency_score": "Efficiency Score", "subscription_overview": "Subscription Overview", "subscription_type": "Subscription Type", "subscription_status": "Subscription Status", "next_billing": "Next Billing", "monthly_limit": "Monthly Limit", "usage_this_month": "Usage This Month", "student_statistics": "Student Statistics", "total_students": "Total Students", "active_students": "Active Students", "inactive_students": "Inactive Students", "recent_activity": "Recent Activity", "no_recent_activity": "No recent activity", "pending_purchases": "Pending Purchases", "no_pending_purchases": "No pending purchases", "purchase_date": "Purchase Date", "amount": "Amount", "status": "Status", "processing": "Processing", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "low_balance_title": "Low Credit Balance", "low_balance_message": "You only have {credits} remaining. Consider recharging to continue using all features.", "recharge_now": "Recharge Now", "pending_payment_title": "Pending Payment", "pending_payment_message": "You have {count} pending payment(s). Click to finalize your credit purchase.", "finalize_payment": "Finalize ({credits})", "total_paid": "Total Paid", "registered_students": "Registered Students", "low_balance": "Low Balance", "normal_balance": "Normal Balance", "efficiency": "Efficiency: {score}%", "equivalent": "Equivalent: {credits} credits purchased", "stats_error": "Error", "stats_loading": "...", "of_total": "{rate}% of total", "total_students_count": "({count} total students)", "registered_payments": "Registered Payments", "current_plan": "Current Plan", "features": "Features", "features_enabled": "{count} enabled", "quick_actions": "Quick Actions", "buy_credits_action": "Buy Credits", "recharge_balance": "Recharge your balance", "detailed_history": "Detailed History", "view_all_transactions": "View all transactions", "change_plan": "Change Plan", "discover_offers": "Discover our offers", "recent_transactions": "Recent Transactions", "view_all": "View All", "no_recent_transactions": "No recent transactions", "credit_purchase": "Credit Purchase", "today": "Today", "yesterday": "Yesterday", "days_ago": "{days} days ago", "finalize": "Finalize", "view_complete_history": "View complete history", "error_loading_transactions": "Error loading transactions", "credits_purchased": "Credits Purchased", "modal": {"buy_credits_title": "Buy Credits", "credits_to_buy": "Number of credits to buy", "custom_amount": "Custom amount", "summary": "Summary", "credits_label": "Credits:", "unit_price": "Unit price:", "subtotal": "Subtotal:", "taxes": "Taxes:", "total": "Total:", "billing_info": "Billing Information", "full_name": "Full Name", "email_address": "Email Address", "phone_number": "Phone Number", "organization": "Organization (optional)", "proceed_payment": "Proceed to Payment", "checking_payment": "Checking payment...", "checking_status": "Checking your payment status", "payment_initiated": "Payment initiated successfully!", "redirect_message": "You will be redirected to the payment page"}, "history": {"title": "Credits Purchase History", "back_to_credits": "Back to Credits", "search_placeholder": "Search by transaction ID...", "filter_by_status": "Filter by Status", "filter_by_date": "Filter by Date", "all_statuses": "All Statuses", "last_30_days": "Last 30 Days", "last_90_days": "Last 90 Days", "this_year": "This Year", "export_csv": "Export CSV", "no_purchases_found": "No purchases found", "no_purchases_message": "No credit purchases have been made yet.", "adjust_filters": "Try adjusting your filters or search term.", "transaction_id": "Transaction ID", "credits_purchased": "Credits Purchased", "total_amount": "Total Amount", "purchase_date": "Purchase Date", "payment_method": "Payment Method", "view_details": "View Details", "loading_history": "Loading history...", "error_loading": "Error loading history", "try_again": "Try Again", "reason_not_specified": "Reason not specified", "purchase_history_title": "Purchase History", "purchase_history_subtitle": "View your credit purchase history", "export": "Export", "completed_transactions": "Completed", "pending_transactions": "Pending", "cancelled_transactions": "Cancelled", "total_credits": "Total Credits", "efficiency_rate": "Efficiency Rate", "credits_obtained": "Credits Obtained", "completed_transactions_only": "Completed transactions only", "total_transactions": "Total transactions", "all_categories": "All categories", "search_by_id": "Search by ID...", "all_dates": "All dates", "today": "Today", "this_week": "This week", "this_month": "This month", "transaction": "Transaction", "date": "Date", "credits": "Credits", "amount": "Amount", "status": "Status", "actions": "Actions", "cancellation_reason": "Cancellation reason:"}, "success": {"checking_payment": "Checking payment...", "checking_message": "We are verifying your payment status. Please wait.", "attempt_count": "Attempt {count}/10", "payment_successful": "Payment Successful!", "success_message": "Your credit purchase has been processed successfully.", "payment_failed": "Payment Failed", "failed_message": "Your payment could not be processed. Please try again.", "payment_pending": "Payment Pending", "pending_message": "Your payment is being processed. This may take a few minutes.", "payment_expired": "Payment Expired", "expired_message": "Your payment session has expired. Please start over.", "back_to_credits": "Back to Credits", "retry_payment": "Retry Payment", "credits_purchased_label": "Credits purchased:", "total_amount_label": "Total amount:", "transaction_id_label": "Transaction ID:", "payment_error_message": "An error occurred while processing your payment.", "go_to_dashboard": "Go to Dashboard", "check_again": "Check Again", "try_new_purchase": "Try New Purchase", "back_to_dashboard": "Back to Dashboard"}}, "settings": {"title": "Settings", "profile_settings": "Profile Settings", "general_settings": "General Settings", "preferences": "Preferences", "credit_settings": "Credit Settings", "language": "Language", "timezone": "Timezone", "currency": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "Notifications", "grading_scale": "Grading Scale", "grading_system": "Grading System"}, "attendance": {"title": "Attendance Management", "page_title": "Manage Attendance", "page_subtitle": "Select a class to view or enter grades.", "mark_attendance": "Mark Attendance", "attendance_report": "Attendance Report", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused", "view_attendance": "View Attendance", "attendance_summary": "Attendance Summary", "search_classes": "Search classes...", "current_term": "Current Term: {term}", "no_classes_found": "No classes found", "no_classes_available": "No classes available", "adjust_search_terms": "Try adjusting your search terms", "classes_will_appear": "Classes will appear here once they are created", "failed_to_load_classes": "Failed to load classes data", "total_students": "Total Students", "manage_attendance": "Manage Attendance", "subjects": {"page_title": "Manage Attendance - {className}", "page_subtitle": "Select a subject to manage attendance.", "loading_title": "Loading...", "not_assigned": "Not assigned", "no_subjects_available": "No subjects available", "subjects_will_appear": "Subjects will appear here once they are created and assigned to teachers", "breadcrumb_attendance": "Attendance"}, "student_attendance": {"attendance_for": "Attendance for {className}", "pick_a_date": "Pick a date", "select_schedule": "Select Schedule", "no_schedules": "No schedules available", "search_students": "Search students...", "mark_all_present": "<PERSON>", "mark_all_absent": "<PERSON> Absent", "mark_all_late": "<PERSON>", "mark_all_excused": "<PERSON>d", "save_attendance": "Save Attendance", "saving": "Saving...", "no_students_found": "No students found", "no_students_message": "No students are enrolled in this class.", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused", "attendance_saved": "Attendance saved successfully!", "attendance_save_failed": "Failed to save attendance"}}, "classes": {"title": "Class Management", "add_class": "Add Class", "edit_class": "Edit Class", "delete_class": "Delete Class", "class_name": "Class Name", "class_capacity": "Class Capacity", "assigned_teacher": "Assigned Teacher", "schedule": "Schedule", "view_class": "View Class", "class_details": "Class Details", "no_school_id": "No school ID found for this user.", "notification": "Notification", "add_new_class": "Add New Class", "all_levels": "All Levels", "class_level": "Class Level", "class_code": "Class Code", "view": "View", "delete": "Delete", "add_new_level": "Add New Level", "class_level_name": "Class Level Name", "class_created_success": "Class created successfully!", "class_creation_failed": "Failed to create class", "invalid_password": "Invalid password", "class_deleted": "Class deleted", "class_deletion_failed": "Failed to delete class", "classes_deleted": "Classes deleted", "bulk_deletion_failed": "Bulk deletion failed", "level_created_success": "Level created successfully!", "level_deleted": "Level deleted", "delete_all_classes": "Delete All Classes", "delete_selected_classes": "Delete Selected Classes", "delete_confirmation": "Are you sure you want to delete {count} class(es)?", "classes": "classes", "student_id": "Student ID", "full_name": "Full Name", "age": "Age", "gender": "Gender", "status": "Status", "not_specified": "Not specified", "class_updated_success": "Class updated successfully!", "error_updating_class": "Error updating class.", "invalid_password_error": "Invalid Password!", "class_deleted_success": "Class deleted successfully!", "error_deleting_class": "Error deleting class.", "subjects_for_class": "Subjects for {className}", "no_description": "No description", "no_subjects_found": "No subjects found for this class.", "students_of_class": "Students of {className}", "school": "School", "modals": {"delete_class": "Delete Class", "delete_class_confirmation": "Are you sure you want to delete the class \"{className}\"? This action is irreversible.", "enter_password_confirm": "Enter your password to confirm", "deleting": "Deleting...", "class_deleted_successfully": "Class has been Deleted Successfully!", "error_deleting_class": "There was an error deleting this class. Try again and if this persist contact support!", "password_required": "Please enter your password to confirm deletion.", "update_class": "Update Class", "class_name": "Class Name", "class_code": "Class Code", "class_level": "Class Level", "select_level": "Select level", "class_updated_successfully": "Class has been sent Successfully!", "error_updating_class": "There was an error updating this class. Try again and if this persist contact support!", "create_level": "Create Level", "level_name": "Level Name", "level_name_required": "Level name is required", "level_created_successfully": "Level has been created successfully!", "error_creating_level": "There was an error creating this level. Try again and if this persist contact support!", "delete_level": "Delete Level", "delete_level_confirmation": "Are you sure you want to delete the level \"{levelName}\"? This action is irreversible.", "level_deleted_successfully": "Level has been deleted successfully!", "error_deleting_level": "There was an error deleting this level. Try again and if this persist contact support!"}}, "examtype": {"title": "Exam Types", "add_exam_type": "Add Exam Type", "edit_exam_type": "Edit Exam Type", "delete_exam_type": "Delete Exam Type", "exam_type_name": "Exam Type Name", "description": "Description", "weight": "Weight", "exam_type_management": "Exam Type Management", "exam_type": "Exam Type", "created_at": "Created At", "invalid_date": "Invalid Date", "add_new_exam_type": "Add New Exam Type", "failed_to_fetch": "Failed to fetch exam types", "exam_type_updated_successfully": "Exam type updated successfully", "exam_type_created_successfully": "Exam type created successfully", "failed_to_save": "Failed to save exam type", "user_email_not_found": "User email not found", "exam_type_deleted_successfully": "Exam type deleted successfully", "exam_types_deleted_successfully": "{count} exam types deleted successfully", "invalid_password": "Invalid password", "failed_to_delete": "Failed to delete exam type(s)", "delete_selected_exam_types": "Delete Selected Exam Types", "delete_confirmation_single": "Are you sure you want to delete the exam type \"{examType}\"? This action cannot be undone.", "delete_confirmation_multiple": "Are you sure you want to delete {count} selected exam types? This action cannot be undone.", "exam_type_required": "Exam type is required", "school_id_required": "School ID is required", "edit_exam_type_modal": "Edit Exam Type", "add_exam_type_modal": "Add New Exam Type", "exam_type_placeholder": "e.g., Midterm, Final, Quiz"}, "fees": {"title": "Fee Types", "fee_types_management": "Fee Types Management", "fee_type": "Fee Type", "amount": "Amount", "created_at": "Created At", "invalid_date": "Invalid Date", "add_new_fee_type": "Add New Fee Type", "failed_to_fetch": "Failed to fetch fee types", "fee_updated_successfully": "Fee type updated successfully", "fee_created_successfully": "Fee type created successfully", "failed_to_save": "Failed to save fee type", "user_email_not_found": "User email not found", "fee_deleted_successfully": "Fee type deleted successfully", "fees_deleted_successfully": "{count} fee types deleted successfully", "invalid_password": "Invalid password", "failed_to_delete": "Failed to delete fee type(s)", "delete_fee_type": "Delete Fee Type", "delete_selected_fee_types": "Delete Selected Fee Types", "delete_confirmation_single": "Are you sure you want to delete this fee type? This action cannot be undone.", "delete_confirmation_multiple": "Are you sure you want to delete {count} selected fee types? This action cannot be undone.", "tuition_fee": "<PERSON>ition <PERSON>e", "registration_fee": "Registration Fee", "library_fee": "Library Fee", "laboratory_fee": "Laboratory Fee", "sports_fee": "Sports Fee", "technology_fee": "Technology Fee", "transportation_fee": "Transportation Fee", "examination_fee": "Examination Fee", "activity_fee": "Activity Fee", "uniform_fee": "Uniform Fee", "textbook_fee": "Textbook Fee", "fee_type_required": "Fee type is required", "amount_required": "Amount is required", "valid_amount_required": "Please enter a valid positive amount", "school_id_required": "School ID is required", "edit_fee_type": "Edit Fee Type", "add_fee_type": "Add New Fee Type", "fee_type_placeholder": "Select or type fee type"}, "grades": {"title": "Manage Grades", "manage_grades": "Manage Grades", "select_class_subtitle": "Select a class to view or enter grades.", "search_classes": "Search classes...", "current_term": "Current Term: {term}", "no_school_id": "No school ID found", "failed_to_load_classes": "Failed to load classes data", "no_classes_found": "No classes found", "no_classes_available": "No classes available", "adjust_search_terms": "Try adjusting your search terms", "classes_will_appear": "Classes will appear here once they are created", "total_grades": "Total Grades", "classes": {"no_teacher_assigned": "No teacher assigned", "class_average": "Class Average", "errors": {"failed_to_load_grades": "Failed to load grades data", "failed_to_load_class_data": "Failed to load class data", "no_subjects_available": "Aucune matière disponible pour cette classe", "no_subjects_available_explanation": "Subjects will appear here once they are created and assigned to teachers", "select_grades_to_delete": "Please select a grade to delete", "failed_to_submit_grade": "Failed to delete grade(s)", "failed_to_verify_password": "User email not found. Deletion failed.", "grade_exported_to_pdf_failed": "Failed to export grade to PDF", "grade_exported_to_excel_failed": "Failed to export grade to Excel"}, "success": {"grade_deleted_successfully": "Grade deleted successfully", "grade_updated_successfully": "Grade updated successfully", "grade_created_successfully": "Grade created successfully", "grade_exported_to_pdf_successfully": "Grade exported to PDF successfully", "grade_exported_to_excel_successfully": "Grade exported to Excel successfully"}, "subject": {"manage_grade_for_subject_and_class": "Manage grades for this subject and class.", "add_grade": "Add Grade"}, "select_subject_to_manage_grades": "Select a subject to manage grades."}}, "justification": {"title": "Absence Justifications", "add_justification": "Add Justification", "edit_justification": "Edit Justification", "delete_justification": "Delete Justification", "student_name": "Student Name", "absence_date": "Absence Date", "reason": "Reason", "status": "Status", "approved": "Approved", "pending": "Pending", "rejected": "Rejected"}, "parents": {"title": "Parent Management", "add_parent": "Add Parent", "edit_parent": "<PERSON> Parent", "delete_parent": "Delete Parent", "parent_name": "Parent Name", "contact_info": "Contact Information", "relationship": "Relationship", "emergency_contact": "Emergency Contact", "view_parent": "View Parent"}, "period": {"title": "Period Management", "add_period": "Add Period", "edit_period": "Edit Period", "delete_period": "Delete Period", "period_name": "Period Name", "start_time": "Start Time", "end_time": "End Time", "duration": "Duration"}, "resources": {"title": "Resources", "add_resource": "Add Resource", "edit_resource": "Edit Resource", "delete_resource": "Delete Resource", "resource_name": "Resource Name", "resource_type": "Resource Type", "availability": "Availability", "description": "Description"}, "school_resources": {"title": "School Resources", "add_school_resource": "Add School Resource", "edit_school_resource": "Edit School Resource", "delete_school_resource": "Delete School Resource", "resource_name": "Resource Name", "resource_type": "Resource Type", "price": "Price", "stock": "Stock", "class_level": "Class Level"}, "staff": {"title": "Staff Management", "add_staff": "Add Staff", "edit_staff": "Edit Staff", "delete_staff": "Delete Staff", "staff_name": "Staff Name", "position": "Position", "department": "Department", "hire_date": "Hire Date", "contact_info": "Contact Information"}, "subjects": {"title": "Subject Management", "add_subject": "Add Subject", "edit_subject": "Edit Subject", "delete_subject": "Delete Subject", "subject_name": "Subject Name", "subject_code": "Subject Code", "description": "Description", "credits": "Credits"}, "teacher_assignment": {"title": "Teacher Assignment", "assign_teacher": "Assign Teacher", "edit_assignment": "Edit Assignment", "delete_assignment": "Delete Assignment", "teacher_name": "Teacher Name", "subject": "Subject", "class": "Class", "academic_year": "Academic Year"}, "teachers": {"title": "Teacher Management", "add_teacher": "Add Teacher", "edit_teacher": "Edit Teacher", "delete_teacher": "Delete Teacher", "teacher_name": "Teacher Name", "subject_taught": "Subject Taught", "qualification": "Qualification", "experience": "Experience", "contact_info": "Contact Information"}, "timetable": {"title": "Timetable", "create_timetable": "Create Timetable", "edit_timetable": "Edit Timetable", "view_timetable": "View Timetable", "class_schedule": "Class Schedule", "teacher_schedule": "Teacher Schedule", "time_slot": "Time Slot", "subject": "Subject", "room": "Room"}}}, "teacher-dashboard": {"pages": {"dashboard": {"title": "Teacher Dashboard", "welcome": "Welcome, {name}", "overview": "Overview", "my_classes": "My Classes", "today_schedule": "Today's Schedule", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "students_count": "Students Count", "subjects_taught": "Subjects Taught", "upcoming_classes": "Upcoming Classes"}, "classes": {"title": "My Classes", "class_details": "Class Details", "student_list": "Student List", "class_schedule": "Class Schedule", "class_performance": "Class Performance"}, "attendance": {"title": "Attendance", "take_attendance": "Take Attendance", "attendance_history": "Attendance History", "mark_present": "<PERSON>", "mark_absent": "<PERSON>", "attendance_summary": "Attendance Summary"}, "grades": {"title": "Grades", "enter_grades": "Enter Grades", "grade_history": "Grade History", "grade_statistics": "Grade Statistics", "class_average": "Class Average", "student_progress": "Student Progress"}, "resources": {"title": "Resources", "upload_resource": "Upload Resource", "my_resources": "My Resources", "shared_resources": "Shared Resources", "resource_library": "Resource Library"}}}}, "components": {"chart": {"user_growth_trend": "User Growth Trend", "loading_user_stats": "Loading user stats...", "failed_to_load_data": "Failed to load data", "select_year": "Select Year"}, "performance_table": {"title": "School Performance", "school_name": "School Name", "metric": "Metric", "value": "Value", "search_placeholder": "Search for a school...", "items_per_page": "Items per page", "showing": "Showing", "to": "to", "of": "of", "entries": "entries", "previous": "Previous", "next": "Next", "no_data": "No data available", "select_metric": "Select Metric"}, "data_table": {"search": "Search...", "filter": "Filter", "export": "Export", "bulk_actions": "Bulk Actions", "select_all": "Select All", "deselect_all": "Deselect All", "delete_selected": "Delete Selected", "no_results": "No results found", "loading": "Loading...", "rows_per_page": "Rows per page", "page": "Page", "of_pages": "of {total} pages", "no_data": "No data available", "delete_selected_one": "Delete Selected (1)", "delete_selected_count": "Delete Selected ({count})", "view_details": "View Details", "selected_count": "{selected} of {total} selected", "select_all_count": "Select All ({count})", "delete_all_count": "Delete All ({count})", "active_filters": "Active filters", "page_of": "Page {current} of {total}", "items_per_page": "Items per page", "actions": "Actions", "all_items": "All {type}"}, "modals": {"create": "Create", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "close": "Close", "are_you_sure": "Are you sure?", "this_action_cannot_be_undone": "This action cannot be undone", "delete_confirmation": "Are you sure you want to delete this item?", "delete_confirmation_with_name": "Are you sure you want to delete <strong class=\"font-semibold text-pink-500\">{name}?</strong>", "delete_confirmation_prefix": "Are you sure you want to delete", "bulk_delete_confirmation": "Are you sure you want to delete {count} selected items?", "delete_all_items": "Delete All {type}", "delete_selected_items": "Delete Selected {type}", "delete_all_warning": "This will permanently delete ALL {type} in the system. This action cannot be undone!", "delete_selected_warning": "This will permanently delete the selected {type}. This action cannot be undone!", "danger_delete_all": "⚠️ DANGER: Delete All Operation", "bulk_delete_operation": "⚠️ Bulk Delete Operation", "about_to_delete_prefix": "You are about to delete", "delete_all_system_warning": "This will delete ALL {type} in the system and cannot be undone!", "enter_password_to_confirm": "Please enter your password to confirm this {type} operation:", "destructive": "destructive", "bulk": "bulk", "delete_count_items": "Delete {count} {type}"}}, "notifications": {"title": "Notifications", "mark_as_read": "<PERSON> <PERSON>", "mark_all_read": "<PERSON> as <PERSON>", "no_notifications": "No notifications", "new_notification": "New Notification", "notification_settings": "Notification Settings", "email_notifications": "Email Notifications", "push_notifications": "Push Notifications"}, "forms": {"validation": {"required_field": "This field is required", "invalid_email": "Invalid email address", "invalid_phone": "Invalid phone number", "password_too_short": "Password must be at least 8 characters", "passwords_dont_match": "Passwords do not match", "invalid_format": "Invalid format", "select_at_least_one": "Please select at least one item to delete", "no_items_to_delete": "No items to delete", "password_required": "Please enter your password to confirm deletion", "errors": "Validation errors", "fill_required_fields": "Please fill in all required fields.", "all_fields_required": "All fields are required."}, "placeholders": {"enter_name": "Enter name", "enter_email": "Enter email", "enter_phone": "Enter phone", "enter_address": "Enter address", "select_option": "Select an option", "search_placeholder": "Search...", "password": "Password", "confirm_password": "Confirm Password", "full_name": "Full Name", "role": "Role", "select_role": "Select Role", "select_school": "Select School", "website": "Website", "principal_name": "Principal Name", "established_year": "Established Year", "password_confirm_delete": "Type password to confirm delete"}}, "messages": {"success": {"saved": "Successfully saved", "deleted": "Successfully deleted", "updated": "Successfully updated", "created": "Successfully created", "sent": "Successfully sent", "all_items_deleted": "All {type} have been successfully deleted", "items_deleted": "{count} {type} successfully deleted", "bulk_delete": "Bulk delete was successful!", "transaction_saved": "Transaction saved successfully!", "class_created": "Class created successfully!", "level_created": "Level created successfully!", "class_deleted": "Class Deleted successfully!", "level_deleted": "Class level deleted successfully!", "student_deleted": "Student Deleted successfully!", "profile_updated": "Profile updated successfully", "announcement_saved": "Announcement has been {action} successfully!", "announcement_deleted": "Announcement has been deleted successfully!"}, "error": {"generic": "An error occurred", "network": "Network error", "unauthorized": "Unauthorized", "forbidden": "Access forbidden", "not_found": "Not found", "server_error": "Server error", "invalid_password": "Invalid Password!", "bulk_delete_failed": "Bulk delete failed. Please try again.", "loading_data": "Error loading data", "loading": "Loading error", "school_id_required": "School ID required", "profile_update_failed": "Failed to update profile", "transaction_save_failed": "Error saving transaction. Please try again.", "class_creation_failed": "An unknown error occurred while creating the class.", "level_creation_failed": "An unknown error occurred while creating a class level.", "class_deletion_failed": "An unknown error occurred while deleting this class.", "level_deletion_failed": "An unknown error occurred while deleting this class level.", "student_deletion_failed": "An unknown error occurred while deleting the Student.", "import_failed": "Import failed.", "announcement_save_failed": "There was an error trying to save the announcement. Try again and if this persists, contact support.", "announcement_delete_failed": "There was an error trying to delete the announcement. Try again and if this persists, contact support.", "no_school_associated": "No school associated"}, "confirmation": {"delete": "Are you sure you want to delete this item?", "cancel": "Are you sure you want to cancel?", "logout": "Are you sure you want to logout?"}, "info": {"please_wait": "Please wait while processing."}}, "registration": {"title": "Student Registration", "confirming": "Confirming...", "confirm_payment_register": "Confirm Payment & Register", "steps": {"personal_info": "Personal Information", "personal_info_desc": "Enter the student's details.", "contact_info": "Contact Information", "contact_info_desc": "Provide the student's contact info.", "guardian_details": "Parent/Guardian Details", "guardian_details_desc": "Provide information for parent/guardian.", "academic_info": "Academic Information", "academic_info_desc": "Details regarding the student's academic background.", "emergency_contact": "Emergency Contact", "emergency_contact_desc": "Provide emergency contact information.", "medical_info": "Medical Information", "medical_info_desc": "Fill in any medical history if applicable.", "consent": "Consent and Declaration", "consent_desc": "Agree to terms and conditions.", "fee_info": "Fee Information", "fee_info_desc": "Choose the fee structure and payment options.", "payment_confirmation": "Payment Confirmation", "payment_confirmation_desc": "Provide proof of payment and registration confirmation."}, "fields": {"student_address": "Student Address", "student_phone_optional": "Student Phone Number (Optional)", "select_class": "Select Class", "previous_school": "Previous School/Institution", "dob": "DOB", "search_student": "Search Student", "search_placeholder": "Type student name or ID...", "first_name": "First Name", "last_name": "Last Name", "middle_name": "Middle Name", "date_of_birth": "Date of Birth", "nationality": "Nationality", "male": "Male", "female": "Female", "select_gender": "Select gender", "place_of_birth": "Place Of Birth", "search_guardian": "Search Guardian in the system", "guardian_search_placeholder": "Type name or email...", "guardian_name": "Parent/Guardian Name", "relationship": "Relationship With Student", "mother": "Mother", "father": "Father", "brother": "Brother", "sister": "Sister", "aunty": "Aunty", "uncle": "Uncle", "grandmother": "Grand Mother", "grandfather": "Grand Father", "other": "Other", "select_relationship": "Select Relationship", "same_address": "Same address as student", "guardian_phone_required": "Guardian Phone Number (Required)", "occupation": "Occupation", "transcript_provided": "Check the box if copies of transcripts or report cards were provided", "same_as_guardian": "Same as Guardian Info", "health_condition": "Health Condition (Write notes if student have a health condition)", "doctor_name": "Doctor's Name", "doctor_phone_optional": "Doctor's Phone Number (Optional)", "doctor_phone": "Doctor Phone", "emergency_contact_name": "Emergency Contact Name", "emergency_contact_phone": "Emergency Contact Phone"}, "fees": {"payment_option": "Payment Option", "full_payment": "Full Payment", "pay_installments": "Pay in Installments", "apply_scholarship": "Apply Scholarship", "scholarship_percentage": "Scholarship (%)", "enter_percentage": "Enter percentage (e.g., 20)", "scholarship_applied": "Scholarship Applied", "total": "Total", "number_installments": "Number of Installments", "installment": "Installment", "installments": "Installments", "choose_due_dates": "<PERSON>ose Due Dates", "installment_amounts": "Installment Amounts", "first_installment_now": "First Installment to be Paid Now", "select_applicable_fees": "Select Applicable Fees", "no_fees_available": "No applicable fees available"}, "resources": {"other_school_resources": "Other School Resources", "search_placeholder": "Search resources...", "no_resources_found": "No resources found"}, "summary": {"review_title": "Review Submission Summary", "student_info": "Student Info", "guardian_info": "Guardian Info", "emergency_contact": "Emergency Contact", "medical_info": "Medical Info", "none_reported": "None reported", "fees_resources": "Fees & Resources", "total_payable": "Total Payable", "payment_mode": "Payment Mode", "payment_dates": "Payment Dates", "installment_breakdown": "Installment Breakdown", "consent": "Consent", "guardian_agreement": "Guardian Agreement", "agreed": "Agreed", "not_agreed": "Not Agreed", "final_warning": "Please ensure all information is correct before submitting. You will not be able to modify it afterward."}, "messages": {"registration_failed": "Registration failed. Please try again.", "registration_successful": "Registration successful!", "student_already_exists": "A student with the same name and date of birth already exists in this school."}, "actions": {"add_new_student": "click here if new student", "clear_selected_student": "Clear selected student"}}, "parents": {"manage_invitations": "Manage Parent Invitations", "parent_avatar": "<PERSON><PERSON>", "schools": "Schools", "children": "Children", "no_schools": "No Schools", "unknown_school": "Unknown School", "no_children": "No Children", "unknown_student": "Unknown Student", "add_parent": "Add Parent", "search_placeholder": "Search parents...", "no_parents_found": "No parents found.", "parent_invite": "<PERSON><PERSON>", "send_invitation": "Send Invitation", "select_schools": "Select schools", "schools_selected": "{{count}} school(s) selected", "select_children": "Select children", "sending": "Sending...", "delete_invitation": "Delete Invitation", "deleting": "Deleting...", "update_parents": "Update Parents", "search_schools": "Search schools...", "search_children": "Search children...", "updating": "Updating...", "update_parent": "Update Parent", "loading_more": "Loading more parents...", "reached_end": "You've reached the end of the list!", "messages": {"failed_load_data": "Failed to load data. Please try again.", "failed_load_more": "Failed to load more parents. Please try again.", "registered_successfully": "Parent registered successfully!", "no_parent_selected": "No parent selected for deletion or user not authenticated.", "deleted_successfully": "Parent deleted successfully!", "invitation_sent": "Invitation has been sent Successfully!", "invitation_failed": "There was an error sending this invitation. Try again and if this persist contact support!", "invitation_deleted": "Invitation has been Deleted Successfully!", "delete_error": "There was an error deleting this invitation. Try again and if this persist contact support!", "enter_password_confirm": "Please enter your password to confirm deletion.", "delete_confirmation": "Are you sure you want to delete this Invitation <strong className=\"font-semibold text-red-500\">{{name}}</strong>? This action is <span className=\"font-semibold\">irreversible</span>.", "enter_password_placeholder": "Enter your password to confirm", "parent_updated": "Parent has been Updated Successfully!", "update_error": "There was an error Updating this parent. Try again and if this persist contact support!"}}, "reports": {"global_reports_analytics": "Global Reports and Analytics", "overview_description": "Overview of all schools' performance", "error_loading_data": "Error loading data", "this_week": "This week", "this_month": "This month", "this_quarter": "This quarter", "this_year": "This year", "export": "Export", "total_schools": "Total Schools", "total_revenue": "Total Revenue", "popular_plan": "Popular Plan", "revenue_per_school": "Revenue/School", "active_schools_platform": "Active schools on the platform", "revenue_all_schools": "Revenue from all schools", "most_chosen_plan": "Most subscribed plan", "average_revenue_per_school": "Average revenue per school", "reports_analytics": "Reports and Analytics", "no_school_associated": "No school associated", "academic_year_unavailable": "Academic year unavailable", "loading_academic_year": "Loading academic year...", "loading_data": "Loading data...", "analyze_school_performance": "Analyze your school's performance and usage", "student_statistics": "Student Statistics", "export_modal": {"missing_school_id": "Missing school ID for export", "error": "Error during export", "global_title": "Export Global Report", "school_title": "Export Report - {{schoolName}}", "school": "School", "format": "Export format", "period": "Period", "custom_dates": "Use custom dates", "start_date": "Start date", "end_date": "End date", "estimated_size": "Estimated size", "success_message": "Export successful! Download has started.", "exporting": "Exporting..."}}, "analytics": {"schools": "Schools", "revenue": "Revenue", "global_subscription_overview": "Global Subscription Overview", "plan_distribution_description": "Plan distribution across all schools", "showing_demo_data": "Showing demo data", "plan_distribution": "Plan Distribution", "monthly_trends": "Monthly Trends", "subscription_overview": "Subscription Overview", "plan_distribution_trends": "Plan distribution and trends", "plan_breakdown": "Plan Breakdown", "monthly_evolution": "Monthly Evolution", "credit_usage": "Credit Usage", "credit_evolution_description": "Evolution of your credits over time", "trend": "Trend", "balance": "Balance", "used": "Used", "purchased": "Purchased"}, "consent": {"title": "Consent Declaration", "guardian_confirmation": "The parent or guardian present confirms that they are the lawful guardian of the student named in this application. They affirm that all provided information is true and accurate to the best of their knowledge.", "understand_accept": "They understand and accept that", "data_processing": "The school may collect, store, and process personal and student data in accordance with data protection laws.", "financial_responsibility": "They are financially responsible for the fees and agree to the selected mode of payment (full or installment).", "installment_agreement": "If paying by installments, they agree to pay by the specified due dates. Failure to do so may lead to penalties or access restrictions.", "policy_amendments": "The school may amend schedules, fees, or policies with due notice.", "emergency_treatment": "The school may seek emergency medical treatment if the guardian cannot be reached.", "school_policies": "They have read and agree to follow the school's academic, behavioral, and safety policies.", "admin_confirmation": "The admin is confirming below that the parent/guardian has reviewed this declaration and given verbal consent.", "verbal_consent_confirmation": "I confirm that the parent/guardian present has given verbal consent to the declaration above."}, "language": {"french": "French", "english": "English", "select_language": "Select Language", "language_changed": "Language changed successfully"}}