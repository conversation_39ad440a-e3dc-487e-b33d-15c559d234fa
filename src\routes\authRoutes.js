const express = require('express');
const authController = require('../controllers/authController'); // Updated controller import
const staffController = require('../controllers/staffController');


const router = express.Router();

// router.post('/register', registerUser); // POST /api/auth/register
router.post('/login', authController.loginUser);       // POST /api/auth/login
router.post('/forgot-password', authController.forgotPassword); // POST /api/auth/forgot-password
router.post('/reset-password', authController.resetPassword); // POST /api/auth/reset-password
router.post('/resend-code', authController.resendCode); // POST /api/auth/resend-code
router.post('/invite-parent',authController.sendInvitation)
router.post('/resend-invite-parent',authController.resendInvitation)
router.get('/redirect',authController.redirectToApp)
router.post('/get-code',authController.getCode)
router.post('/verify-code',authController.verifyCode)
router.post('/verify-password',authController.verifyPassword)
router.post('/reset-password-token', staffController.resetPasswordWithToken) // POST /api/auth/reset-password-token (for staff/teachers)
// router.post('/signout', signOut);      // POST /api/auth/signout

module.exports = router;