// routes/schoolRoutes.js
const express = require('express');
const schoolController = require('../controllers/schoolController');
const { authenticate, authorize, checkSubscription,uploadSchoolLogoById } = require('../middleware/middleware');
const { uploadSchoolLogo } = require('../utils/uploaders')

const router = express.Router();
// router.get('/test', schoolController.testSchoolResponse);

// GET /schools to fetch all schools
router.get('/get-schools', authenticate, checkSubscription, authorize(['super', 'parent', 'teacher']), schoolController.getAllSchools);

// GET /school by id
router.get('/get-school/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), schoolController.getSchoolById);

router.get('/get-school_id/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), schoolController.getSchoolBy_id);

// POST /schools to create a new school
router.post('/create-school', authenticate, authorize(['super']), schoolController.createSchool);

// PUT /schools/:id to update a specific school
router.put('/update-school/:id', authenticate, authorize(['admin', 'super']), schoolController.updateSchoolById);

// DELETE /schools/:id to delete a specific school
router.delete('/delete-school/:id', authenticate, authorize(['super']), schoolController.deleteSchoolById);

//DELETE multiple schools
router.delete('/delete-schools', authenticate, authorize(['super']), schoolController.deleteMultipleSchools);

//DELETE ALL schools
router.delete('/delete-all-schools', authenticate, authorize(['super']), schoolController.deleteAllSchools);

router.get(
    '/total-schools',
    authenticate,
    checkSubscription,
    authorize(['super']),
    schoolController.getTotalSchools
);

// New route: GET total schools created this month and percentage change from last month
router.get(
    '/schools-count-change',
    authenticate,
    checkSubscription,
    authorize(['super']),
    schoolController.getSchoolCountWithChange
);

router.get(
  '/performance',
  authenticate,
  checkSubscription,
  authorize(['super']),
  schoolController.getSchoolsPerformance
);

router.post(
  '/upload-logo/:id',  // id refers to school_id
  authenticate,
  authorize(['admin', 'super', 'school_admin']), // Add authorization
  uploadSchoolLogo.single('logo'),  // Use the multer uploadSchoolLogo middleware here
  schoolController.uploadSchoolLogoById  // After the file is uploaded, call the controller to save it
);

module.exports = router;
