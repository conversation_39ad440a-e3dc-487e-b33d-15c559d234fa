"use client";

import React, { Suspense, useEffect, useState } from "react";
import { Percent, Search, Users, Filter } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import { motion } from "framer-motion";
import { ClassGradesSkeleton } from "@/components/skeletons";
import { getClassesBySchool } from "@/app/services/ClassServices";
import { getStudentsByClassAndSchool } from "@/app/services/StudentServices";
import { getCurrentTerm } from "@/app/services/TermServices";
import ClassGradeCard from "@/components/grades/ClassGradeCard";
import { useToast, ToastContainer } from "@/components/ui/Toast";
import { useRouter } from "next/navigation";
import { useTranslation } from "@/hooks/useTranslation";

// Navigation will be defined inside the component to access translation

interface ClassWithStudentCount {
  _id: string;
  class_id: string;
  name: string;
  class_code: string;
  studentCount: number;
}

export default function GradesPage() {
  const { logout, user } = useAuth();
  const { t, tDashboard } = useTranslation();
  const { toasts, removeToast, showSuccess, showError } = useToast();
  const router = useRouter();

  const navigation = {
    icon: Percent,
    baseHref: "/school-admin/grades",
    title: tDashboard('school-admin', 'grades', 'title')
  };

  // State management
  const [classes, setClasses] = useState<ClassWithStudentCount[]>([]);
  const [filteredClasses, setFilteredClasses] = useState<ClassWithStudentCount[]>([]);
  const [currentTerm, setCurrentTerm] = useState<string>("Premier Trimestre");
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);

  const schoolId = user?.school_ids?.[0] || user?.school_id;

  // Fetch classes and student counts
  useEffect(() => {
    const fetchClassesData = async () => {
      if (!schoolId) {
        showError(t('common.error'), tDashboard('school-admin', 'grades', 'no_school_id'));
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch current term
        try {
          const termResponse = await getCurrentTerm(schoolId.toString());
          setCurrentTerm(termResponse.term.name);
        } catch (error) {
          console.log("No current term found, using default");
        }

        // Fetch classes
        const classesResponse = await getClassesBySchool(schoolId.toString());

        // Fetch student count for each class
        const classesWithCounts = await Promise.all(
          classesResponse.map(async (cls: any) => {
            try {
              const students = await getStudentsByClassAndSchool(cls._id, schoolId.toString());
              return {
                _id: cls._id,
                class_id: cls.class_id,
                name: cls.name,
                class_code: cls.class_code,
                studentCount: students.length
              };
            } catch (error) {
              console.error(`Error fetching students for class ${cls.name}:`, error);
              return {
                _id: cls._id,
                class_id: cls.class_id,
                name: cls.name,
                class_code: cls.class_code,
                studentCount: 0
              };
            }
          })
        );

        setClasses(classesWithCounts);
        setFilteredClasses(classesWithCounts);
      } catch (error) {
        console.error("Error fetching classes data:", error);
        showError(t('common.error'), tDashboard('school-admin', 'grades', 'failed_to_load_classes'));
      } finally {
        setLoading(false);
      }
    };

    fetchClassesData();
  }, [schoolId]);

  // Filter classes based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredClasses(classes);
    } else {
      const filtered = classes.filter(cls =>
        cls.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cls.class_code.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredClasses(filtered);
    }
  }, [searchTerm, classes]);

  const handleClassClick = (classId: string) => {
    router.push(`/school-admin/grades/class?classId=${classId}`);
  };

  if (loading) {
    return (
        <SchoolLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={logout}
        >
          <ClassGradesSkeleton />
        </SchoolLayout>
    );
  }

  return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">{tDashboard('school-admin', 'grades', 'manage_grades')}</h1>
              <p className="text-foreground/60">
                {tDashboard('school-admin', 'grades', 'select_class_subtitle')}
              </p>
            </div>
          </div>

          {/* Search and filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder={tDashboard('school-admin', 'grades', 'search_classes')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
              <Filter className="w-4 h-4" />
              <span>{t('dashboard.school-admin.pages.grades.current_term', { term: currentTerm })}</span>
            </div>
          </div>

          {/* Classes Grid */}
          {filteredClasses.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                {searchTerm ? tDashboard('school-admin', 'grades', 'no_classes_found') : tDashboard('school-admin', 'grades', 'no_classes_available')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {searchTerm
                  ? tDashboard('school-admin', 'grades', 'adjust_search_terms')
                  : tDashboard('school-admin', 'grades', 'classes_will_appear')
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredClasses.map((cls) => (
                <ClassGradeCard
                  key={cls._id}
                  classId={cls._id}
                  className={cls.name}
                  classCode={cls.class_code}
                  studentCount={cls.studentCount}
                  lastTerm={currentTerm}
                  onClick={() => handleClassClick(cls.class_id)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Toast Container */}
        <ToastContainer toasts={toasts} onClose={removeToast}/>
      </SchoolLayout>
  );
}
