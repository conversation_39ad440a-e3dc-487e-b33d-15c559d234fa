"use client";

import React, { useEffect, useState, useMemo } from "react";
import { useSearchParams } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import { getStudentsByClassAndSchool } from "@/app/services/StudentServices";
import { StudentSchema } from "@/app/models/StudentModel";
import {
  Check,
  X,
  Clock,
  BookOpen,
  Search,
  Calendar as CalendarIcon,
} from "lucide-react";
import { getScheduleByClassSubjectAndSchool } from "@/app/services/ClassScheduleServices";
import { PopulatedClassSchedule } from "@/app/models/ClassSchedule";

// Date Picker Imports
import { format } from "date-fns";
import { DayPicker } from "react-day-picker";
import "react-day-picker/dist/style.css";
import * as Popover from "@radix-ui/react-popover";
import { createOrUpdateAttendance, getAttendanceBySchedules } from "@/app/services/AttendanceServices";
import {
  AttendancePopulatedSchema,
  AttendanceStatus,
} from "@/app/models/Attendance";
import { useAcademicYearContext } from "@/context/AcademicYearContext";
import { ClassSchema } from "@/app/models/ClassModel";
import { getClassById } from "@/app/services/ClassServices";
import { useTranslation } from "@/hooks/useTranslation";

interface StudentAttendanceProps {
  schoolId: string;
  classIdRaw: string;
  subjectId: string;
}

const ATTENDANCE_STATUSES: AttendanceStatus[] = [
  "Present",
  "Absent",
  "Late",
  "Excused",
];

const STATUS_COLORS: { [key in AttendanceStatus]: string } = {
  Present: "bg-green-100 border-green-400 text-green-800",
  Absent: "bg-red-100 border-red-400 text-red-800",
  Late: "bg-yellow-100 border-yellow-400 text-yellow-800",
  Excused: "bg-blue-100 border-blue-400 text-blue-800",
};

const STATUS_ICONS: { [key in AttendanceStatus]: JSX.Element } = {
  Present: <Check size={18} className="text-green-600" />,
  Absent: <X size={18} className="text-red-600" />,
  Late: <Clock size={18} className="text-yellow-600" />,
  Excused: <BookOpen size={18} className="text-blue-600" />,
};

const StudentAttendance: React.FC<StudentAttendanceProps> = ({ schoolId, classIdRaw, subjectId }) => {

  const { allAcademicYears, currentAcademicYear } = useAcademicYearContext();
  const { t, tDashboard } = useTranslation();
  const [classCode, classDbId] = classIdRaw?.split("__") || [];
  const [schedules, setSchedules] = useState<PopulatedClassSchedule[]>([]);
  const [selectedScheduleId, setSelectedScheduleId] = useState<string | null>(null);

  const [students, setStudents] = useState<StudentSchema[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<StudentSchema[]>([]);
  const [attendance, setAttendance] = useState<AttendancePopulatedSchema[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isScheduleLoading, setIsScheduleLoading] = useState(true);
  const [isAttendanceLoading, setIsAttendanceLoading] = useState(true);
  const [IsSubmitting, setIsSubmitting] = useState(false);
  const [classInfo, setClassInfo] = useState<ClassSchema | null>(null);

  const today = new Date();
  const todayDayName = today.toLocaleDateString("en-US", { weekday: "long" }); // e.g. "Monday"
  // Helper to get next and previous date for a given day
  const getNextAndPreviousDateOfWeekday = (dayOfWeek: string): { nextDate: Date; prevDate: Date } => {
    const daysMap: Record<string, number> = {
      Sunday: 0,
      Monday: 1,
      Tuesday: 2,
      Wednesday: 3,
      Thursday: 4,
      Friday: 5,
      Saturday: 6,
    };

    const today = new Date();
    const todayIndex = today.getDay(); // 0-6
    const targetIndex = daysMap[dayOfWeek];

    const nextDate = new Date(today);
    const prevDate = new Date(today);

    let diff = (targetIndex - todayIndex + 7) % 7;
    diff = diff === 0 ? 7 : diff; // force next week if same day
    nextDate.setDate(today.getDate() + diff);

    let backDiff = (todayIndex - targetIndex + 7) % 7;
    backDiff = backDiff === 0 ? 7 : backDiff; // force previous week if same day
    prevDate.setDate(today.getDate() - backDiff);

    return { nextDate, prevDate };
  };

  const attendanceMap = useMemo(() => {
    const map: { [studentId: string]: AttendanceStatus } = {};
    attendance.forEach((record) => {
      map[record.student_id._id] = record.status;
    });
    return map;
  }, [attendance]);
  const formatDateToISO = (date?: Date): string => {
    if (!date) return "";
    return format(date, 'yyyy-MM-dd');
  };
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  const totalPages = Math.ceil(filteredStudents.length / itemsPerPage);

  const paginatedStudents = useMemo(() => {
    const start = (currentPage - 1) * itemsPerPage;
    return filteredStudents.slice(start, start + itemsPerPage);
  }, [filteredStudents, currentPage]);

  const fetchClass = async () => {
    try {
      const classData = await getClassById(classDbId)
      setClassInfo(classData)
    } catch (error) {
      console.error("Error fetching students:", error);
    }
  }
  //console.log("class ",classInfo)
  // Fetch students by class and school
  const fetchStudentsByClassAndSchool = async () => {
    setIsLoading(true);
    try {
      if (!classDbId || !schoolId) {
        setStudents([]);
        setFilteredStudents([]);
        setIsLoading(false); // <-- FIX HERE
        return;
      }

      const data = await getStudentsByClassAndSchool(classDbId, schoolId);
      if (data) {
        setStudents(data);
        setFilteredStudents(data);

        // Initialize attendance as Absent for all students
        const initialAttendanceArray: AttendancePopulatedSchema[] = data.map(
          (student) => ({
            _id: "", // no _id yet
            school_id: schoolId,
            student_id: student,
            schedule_id: schedules[0] || ({} as PopulatedClassSchedule),
            status: "Absent",
            academic_year: currentAcademicYear || "",
            date: formatDateToISO(selectedDate) || formatDateToISO(new Date()),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
        );
        setAttendance(initialAttendanceArray);
      } else {
        console.error("Failed to fetch students.");
      }
    } catch (error) {
      console.error("Error fetching students:", error);
    } finally {
      setIsLoading(false);
    }
  };
  // console.log("schedule", schedules)

  // Fetch schedules for the class, subject, and school
  const fetchScheduleByClassSubjectSchool = async () => {
    setIsScheduleLoading(true);
    try {
      if (!schoolId || !classDbId || !subjectId) {
        setSchedules([]);
        return;
      }
      const schedulesData = await getScheduleByClassSubjectAndSchool(
        schoolId,
        classDbId,
        subjectId
      );
      setSchedules(schedulesData);
    } catch (error) {
      console.error("Error fetching schedule:", error);
    } finally {
      setIsScheduleLoading(false);
    }
  };

  // Fetch attendance data by schedules and selected date
  const fetchAttendanceBySchedules = async () => {
    setIsAttendanceLoading(true);
    try {
      if (!schoolId || schedules.length === 0 || !selectedDate) {
        setAttendance([]);
        return;
      }
      const scheduleIdsToFetch = selectedScheduleId
        ? [selectedScheduleId]
        : schedules.map((schedule) => schedule._id);
      //console.log(formatDateToISO(selectedDate) || formatDateToISO(new Date()))
      const attendanceData = await getAttendanceBySchedules({
        school_id: schoolId,
        schedule_ids: scheduleIdsToFetch,
        date: formatDateToISO(selectedDate) || formatDateToISO(new Date()),
      });
      console.log("Attendance Data:", attendanceData);

      setAttendance(attendanceData);

    } catch (error) {
      console.error("Error fetching attendance:", error);
    } finally {
      setIsAttendanceLoading(false);
    }
  };


  useEffect(() => {
    if (schedules.length > 0 && !selectedScheduleId) {
      const todaySchedule = schedules.find(
        (s) => s.day_of_week?.toLowerCase() === todayDayName.toLowerCase()
      );
      if (todaySchedule) {
        setSelectedScheduleId(todaySchedule._id);
      } else {
        // fallback: use the first available schedule
        setSelectedScheduleId(schedules[0]._id);
      }
    }
  }, [schedules, todayDayName, selectedScheduleId]);

  // Effect: Fetch schedules on class/subject/school change
  useEffect(() => {
    if (schoolId && classDbId && subjectId) {
      fetchScheduleByClassSubjectSchool();
    }
  }, [schoolId, classDbId, subjectId]);

  // Effect: Fetch students when class or school changes
  useEffect(() => {
    if (schoolId && classDbId) {
      fetchStudentsByClassAndSchool();
      fetchClass();
    }
  }, [schoolId, classDbId]);

  // Effect: Fetch attendance when schedules change
  useEffect(() => {
    if (schoolId && students.length > 0 && schedules.length > 0 && selectedDate && selectedScheduleId) {
      fetchAttendanceBySchedules();
    }
  }, [schoolId, students, schedules, selectedDate, selectedScheduleId, currentAcademicYear]);


  // Update attendance on date change (optional)
  useEffect(() => {
    // Optionally refetch or filter attendance by selectedDate here
    // For now, just resetting attendance statuses to Absent on date change for demonstration
    setAttendance((prev) =>
      prev.map((record) => ({
        ...record,
        date: formatDateToISO(selectedDate) || formatDateToISO(new Date()),
      }))
    );
  }, [selectedDate]);

  // Handle search filter
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // reset pagination on search
    const lower = value.toLowerCase();
    const filtered = students.filter((s) =>
      `${s.first_name} ${s.last_name}`.toLowerCase().includes(lower)
    );
    setFilteredStudents(filtered);
  };


  // Handle individual attendance status change
  const handleStatusChange = (studentId: string, status: AttendanceStatus) => {
    setAttendance((prev) => {
      const existingIndex = prev.findIndex((a) => a.student_id._id === studentId);
      if (existingIndex !== -1) {
        const updated = [...prev];
        updated[existingIndex] = {
          ...updated[existingIndex],
          status,
          updatedAt: new Date().toISOString(),
        };
        return updated;
      } else {
        const student = students.find((s) => s._id === studentId);
        const schedule = schedules[0] || ({} as PopulatedClassSchedule);
        return [
          ...prev,
          {
            _id: "",
            school_id: schoolId || "",
            student_id: student || {
              _id: studentId,
              first_name: "",
              last_name: "",
              name: "", // <-- add this
            },
            schedule_id: schedule,
            status,
            academic_year: currentAcademicYear || "",
            date: formatDateToISO(selectedDate) || formatDateToISO(new Date()),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ];
      }
    });
  };


  // Handle mark all attendance status
  const handleMarkAll = (status: AttendanceStatus) => {
    const schedule = schedules.find(s => s._id === selectedScheduleId) || schedules[0] || ({} as PopulatedClassSchedule);
    const updated: AttendancePopulatedSchema[] = students.map((student) => ({
      _id: "",
      school_id: schoolId || "",
      student_id: student,
      schedule_id: schedule,
      status,
      academic_year: currentAcademicYear || "",
      date: formatDateToISO(selectedDate) || formatDateToISO(new Date()),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }));
    setAttendance(updated);
  };

  // Date picker select handler
  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    setIsDatePickerOpen(false);
  };

  const handleSubmit = async () => {
    // Basic validation before submission
    if (!selectedDate || !schoolId || !currentAcademicYear) {
      //toast.error("Please ensure a date, school, and academic year are selected.");
      return;
    }
    //console.log("School id fired",schoolId)

    if (attendance.length === 0) {
      //toast.error("No attendance records to submit. Please mark attendance for students.");
      return;
    }

    setIsSubmitting(true); // Start submission loading state
    try {
      // Prepare attendance data for backend: only send IDs for populated fields
      const attendanceToSubmit = attendance.map(record => {
        const base = {
          school_id: schoolId,
          student_id: record.student_id._id,
          schedule_id: record.schedule_id._id,
          date: formatDateToISO(selectedDate) || formatDateToISO(new Date()),
          status: record.status,
          academic_year: record.academic_year,
        };
        if (record._id) {
          return { _id: record._id, ...base }; // For update
        } else {
          return base; // For create, no _id sent
        }
      });


      console.log("Submitting attendance records:", attendanceToSubmit);

      const response = await createOrUpdateAttendance(attendanceToSubmit as any); // Type assertion if needed
      //toast.success("Attendance submitted successfully!");
      console.log("Submission response:", response);

      // After successful submission, refetch attendance to get any _id's assigned by the backend
      // for newly created records and ensure the state is fully synchronized.
      fetchAttendanceBySchedules();

    } catch (error) {
      console.error("Failed to submit attendance:", error);
      //toast.error("Failed to submit attendance. Please try again.");
    } finally {
      setIsSubmitting(false); // End submission loading state
    }
  };
  const classDatesModifiers = useMemo(() => {
    const previousDates: Date[] = [];
    const upcomingDates: Date[] = [];

    schedules.forEach((schedule) => {
      const { prevDate, nextDate } = getNextAndPreviousDateOfWeekday(schedule.day_of_week || "");
      previousDates.push(prevDate);
      upcomingDates.push(nextDate);
    });

    return { previousDates, upcomingDates };
  }, [schedules]);


  return (
    <div className="min-h-screen ">
      {/* GLOBAL STYLES FOR REACT-DAY-PICKER - BEST PLACED IN A GLOBAL CSS FILE */}
      <style jsx global>{`
        .rdp {
          --rdp-cell-size: 38px;
          font-family: system-ui, sans-serif;
          color: #1f2937;
        }

        .rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
          background-color: #e0e7ff;
        }

        .rdp-day_selected {
          background-color: #4f46e5 !important;
          color: white !important;
          border-radius: 0.375rem;
        }

        .rdp-day_today {
          font-weight: 700;
          color: #4f46e5;
        }

        /* 👇 Add these for your modifiers */
        .rdp-day-previous {
          background-color: #f3f4f6 !important; /* Tailwind gray-100 */
          color: #9ca3af !important; /* Tailwind gray-400 */
        }

        .rdp-day-upcoming {
          background-color: #e5e7eb !important; /* Tailwind gray-200 */
          color: #6b7280 !important; /* Tailwind gray-500 */
        }
      `}</style>


      <div className="bg-widget shadow-lg rounded-xl p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6 pb-4 border-b border-gray-200">
          <h1 className="text-3xl font-extrabold text-foreground">
            {t('dashboard.school-admin.attendance.student_attendance.attendance_for', {
              className: classInfo?.name || "N/A"
            })}
          </h1>
          <div className="flex flex-wrap gap-3 items-center">
            <Popover.Root open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
              <Popover.Trigger asChild>
                <button
                  className="jsx-10b26df968ac7be6 jsx-10b26df968ac7be6 inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2 border border-gray-300 hover:bg-gray-100 hover:text-gray-900 h-10 px-4 py-2 w-[240px] text-left gap-2"
                >
                  <CalendarIcon size={18} className="mr-2 h-4 w-4 text-gray-600" />
                  {selectedDate ? (
                    format(selectedDate, "PPP")
                  ) : (
                    <span className="text-gray-500">{t('dashboard.school-admin.pages.attendance.student_attendance.pick_a_date')}</span>
                  )}
                </button>
              </Popover.Trigger>
              <Popover.Portal>
                <Popover.Content
                  sideOffset={8}
                  className="z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4"
                >
                  <DayPicker
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleDateSelect}
                    initialFocus
                    modifiers={{
                      previous: classDatesModifiers.previousDates,
                      upcoming: classDatesModifiers.upcomingDates,
                    }}
                    modifiersClassNames={{
                      previous: 'rdp-day-previous',
                      upcoming: 'rdp-day-upcoming',
                    }}
                  />

                  <Popover.Arrow className="fill-white" />
                </Popover.Content>
              </Popover.Portal>
            </Popover.Root>

            <div className="relative">
              <input
                type="text"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="bg-background pl-9 pr-4 py-2 border border-gray-300 rounded-lg text-base focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
            </div>

            <button
              onClick={() => handleMarkAll("Present")}
              className="bg-[#4f46e5] text-white px-5 py-2 rounded-lg text-base font-medium shadow-md transition-transform transform hover:scale-105"
            >
              Mark All Present
            </button>
            <button
              onClick={() => handleMarkAll("Absent")}
              className="bg-gray-600 hover:bg-gray-700 text-white px-5 py-2 rounded-lg text-base font-medium shadow-md transition-transform transform hover:scale-105"
            >
              Mark All Absent
            </button>
          </div>
        </div>
        {/* Schedule Filter Cards */}
        <div className="flex overflow-x-auto gap-4 py-4 px-2 mb-6 border-b border-gray-200">
          {schedules.map((schedule) => {
            const isSelected = selectedScheduleId === schedule._id;
            const periodNumber = schedule.period_number ?? schedule.period_id?.period_number ?? "N/A";
            const startTime = schedule.period_id?.start_time ?? "N/A";
            const endTime = schedule.period_id?.end_time ?? "N/A";
            const teacherName = schedule.teacher_id?.name ?? "Unknown Teacher";
            const dayOfWeek = schedule.day_of_week ?? "Unknown Day";
            const isToday = schedule.day_of_week === todayDayName;

            return (
              <button
                key={schedule._id}
                onClick={() =>
                  setSelectedScheduleId(schedule._id === selectedScheduleId ? null : schedule._id)
                }
                className={`flex-shrink-0 w-64 p-4 rounded-lg border shadow-sm transition
                ${isSelected ? "bg-indigo-100 border-teal dark:bg-black" : "bg-background border-gray-300 hover:border-teal"}
                ${isToday ? "animate-pulse-today" : ""} // <--- Add this line for the pulse animation
                // Or if you prefer the border glow:
                // ${isToday ? "animate-border-glow-today" : ""}
              `}
                title={`${schedule.subject_id.name} - Period ${periodNumber}`}
              >
                {/* Most Prominent: Period */}
                <div className="text-teal font-extrabold text-2xl mb-1">
                  Period {periodNumber as string}
                </div>

                {/* Moderately Prominent: Day */}
                <div
                  className={`text-gray-700 dark:text-white font-medium text-base mb-2 
                  ${isToday ? "text-green-600 font-semibold" : ""}`}
                >
                  {dayOfWeek} {isToday && "(Today)"}
                </div>

                {/* Subject */}
                <h3 className="font-semibold text-md dark:text-white mb-1">
                  {schedule.subject_id.name}
                </h3>

                {/* Time Slot */}
                <p className="text-sm dark:text-white mb-1">
                  {startTime} - {endTime}
                </p>

                {/* Teacher */}
                <p className="text-sm text-gray-600">Teacher: {teacherName}</p>

                {/* Date */}
                {/* <p className="text-xs text-gray-400 mt-1">
                  Date: {selectedDate ? format(selectedDate, "PPP") : "N/A"}
                </p> */}
                {(() => {
                  const { nextDate, prevDate } = getNextAndPreviousDateOfWeekday(dayOfWeek);
                  return (
                    <>
                      <p className="text-xs text-gray-500">
                        Previosly: {format(prevDate, "PPP")}
                      </p>
                      <p className="text-xs text-gray-500">
                        Upcoming: {format(nextDate, "PPP")}
                      </p>
                    </>
                  );
                })()}
              </button>
            );
          })}
        </div>


        {(isLoading || isScheduleLoading || isAttendanceLoading) && (
          <div className="fixed inset-0 bg-white bg-opacity-70 flex flex-col justify-center items-center z-50">
            <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-teal"></div>
            <p className="mt-4 text-teal font-semibold text-lg">Loading data...</p>
          </div>
        )}
        {isLoading ? (
          <div className="flex justify-center items-center h-48">
            {/* <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-indigo-500"></div> */}
            <p className="ml-4 text-gray-600">...</p>
          </div>
        ) : filteredStudents.length === 0 && !searchTerm ? (
          <div className="text-center py-10 text-gray-500">
            <p className="text-lg mb-2">No students found for this class.</p>
            <p>Please ensure students are assigned to this class in your system.</p>
          </div>
        ) : filteredStudents.length === 0 && searchTerm ? (
          <div className="text-center py-10 text-gray-500">
            <p className="text-lg mb-2">No students found matching "{searchTerm}".</p>
            <p>Try adjusting your search term.</p>
          </div>
        ) : (

          <div className="overflow-x-auto relative shadow-sm sm:rounded-lg">
            <table className="min-w-full divide-y bg-gray-50 dark:bg-gray-800">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="py-3 px-6 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider rounded-tl-lg">
                    Student Name
                  </th>
                  {ATTENDANCE_STATUSES.map((status) => (
                    <th
                      key={status}
                      className="py-3 px-6 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {status}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-gray-50 dark:bg-black">
                {paginatedStudents.map((student) => (
                  <tr
                    key={student._id}
                    className="hover:bg-gray-100 dark:hover:bg-gray-900 transition-colors duration-150 border-b"
                  >
                    <td className="py-3 px-6 whitespace-nowrap text-sm font-medium text-foreground">
                      {student.first_name} {student.last_name}
                    </td>
                    {ATTENDANCE_STATUSES.map((status) => (
                      <td
                        key={status}
                        className="py-3 px-6 whitespace-nowrap text-center"
                      >
                        <label className="inline-flex items-center cursor-pointer">
                          <input
                            type="radio"
                            name={`status-${student._id}`}
                            value={status}
                            checked={attendanceMap[student._id] === status}
                            onChange={() => handleStatusChange(student._id, status)}
                            className="hidden"
                          />
                          <div
                            className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all duration-200
                              ${attendanceMap[student._id] === status
                                ? STATUS_COLORS[status] + " shadow-md"
                                : "bg-gray-100 border-gray-300 hover:bg-gray-200"
                              }`}
                            title={`Mark as ${status}`}
                          >
                            {attendanceMap[student._id] === status
                              ? STATUS_ICONS[status]
                              : null}
                          </div>
                        </label>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            <div className="flex justify-center items-center gap-4 p-6">
              <button
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                className="px-4 py-2 bg-background text-foreground dark:border rounded hover:bg-gray-600 disabled:opacity-50 cursor-pointer"
              >
                Prev
              </button>

              <span className="text-sm text-foreground">
                Page {currentPage} of {totalPages}
              </span>

              <button
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                className="px-4 py-2 bg-background text-foreground dark:border rounded hover:bg-gray-600 disabled:opacity-50 cursor-pointer"
              >
                Next
              </button>
            </div>

          </div>
        )}

        <div className="mt-8 flex justify-end">
          <button
            onClick={handleSubmit} // Calls the submit function
            className="bg-teal cursor-pointer text-white px-8 py-3 rounded-lg text-lg font-semibold shadow-xl transition-transform transform hover:scale-105"
            // Disable button if submitting, or if any data is still loading, or if no attendance records are present
            disabled={IsSubmitting || isLoading || isScheduleLoading || isAttendanceLoading || attendance.length === 0}
          >
            {IsSubmitting ? "Submitting..." : "Submit Attendance"}
          </button>
        </div>

        {/* <div className="mt-8 bg-gray-50 p-4 rounded-lg border border-gray-200 text-gray-700">
          <h3 className="text-md font-bold mb-2">Current Attendance Overview:</h3>
          <pre className="text-xs font-mono bg-gray-100 p-3 rounded-md overflow-auto max-h-48">
            {JSON.stringify(
              { date: selectedDate ? format(selectedDate, "yyyy-MM-dd") : null, attendance },
              null,
              2
            )}
          </pre>
        </div> */}
      </div>
    </div>
  );
}

export default StudentAttendance;
