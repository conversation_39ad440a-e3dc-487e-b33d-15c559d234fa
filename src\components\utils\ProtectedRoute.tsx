"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import CircularLoader from "../widgets/CircularLoader";

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, allowedRoles = [] }) => {
  const { isAuthenticated, loading, setRedirectAfterLogin, user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    if (loading) return;

    // User not logged in
    if (!isAuthenticated) {
      setRedirectAfterLogin(pathname);
      router.push("/login");
      return;
    }

    // Role validation
    if (user && allowedRoles.length > 0) {
      const hasRequiredRole = allowedRoles.some(role =>
        user.role?.toLowerCase() === role.toLowerCase()
      );
      if (!hasRequiredRole) {
        router.push("/unauthorized");
        return;
      }
    }

    setAuthorized(true);
  }, [loading, isAuthenticated, router, pathname, setRedirectAfterLogin, user, allowedRoles]);

  if (loading || !authorized) {
    return (
      <div className="flex justify-center items-center h-screen w-full absolute top-0 left-0 z-50">
        <CircularLoader size={40} color="teal" />
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
