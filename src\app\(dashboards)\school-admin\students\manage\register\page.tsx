"use client";

import React, { useCallback, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import NotificationCard from "@/components/NotificationCard";
import { ArrowLeft, ArrowRight, GraduationCap } from "lucide-react";
import { motion } from "framer-motion";
import CustomInput from "@/components/inputs/CustomInput";
import CustomPhoneInput from "@/components/inputs/CustomPhoneInput";
import CustomSelect from "@/components/inputs/CustomSelect";
import CustomCheckboxInput from "@/components/inputs/CustomCheckBoxInput";
import CustomTextarea from "@/components/inputs/CustomTextarea";
import { FeeSchema } from "@/app/models/FeesModel";
import { getFeesBySchoolId } from "@/app/services/FeesServices";
import { getSchoolResourcesBySchoolId } from "@/app/services/SchoolResourcesServices";
import { SchoolResourceSchema } from "@/app/models/SchoolResources";
import FeesAndResourcesSection from "@/components/utils/Fees&ResourcesSection";
import ConsentDeclaration from "@/components/utils/ConsentDeclaration";
import RegistrationSummary from "@/components/utils/RegistrationSummary";
import { getClassLevelById, getClassLevelsBySchoolId } from "@/app/services/ClassLevels";
import { ClassLevelSchema } from "@/app/models/ClassLevel";
import { createStudent } from "@/app/services/StudentServices";
import { createFeePayment } from "@/app/services/FeePaymentServices";
import { AcademicYearSchema } from "@/app/models/AcademicYear";
import { getAcademicYears } from "@/app/services/AcademicYearServices";
import FeedbackPopup from "@/components/utils/FeedbackPopUp";
import CircularLoader from "@/components/widgets/CircularLoader";
import GuardianDetailsForm from "@/app/(dashboards)/super-admin/students/components/GuardianDetailsForm";
import StudentDetailsForm from "@/app/(dashboards)/super-admin/students/components/StudentDetailsForm";
import { registerParent } from "@/app/services/UserServices";
import { useRouter } from "next/navigation";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import useAcademicYear from "@/components/utils/useAcademicYear";
import { useAcademicYearContext } from "@/context/AcademicYearContext";
import { createRegistrationDraft, deleteRegistrationDraft, getRegistrationDrafts, updateRegistrationDraft } from "@/app/services/RegistrationDraftServices";
import { RegistrationDraftSchema } from "@/app/models/RegistrationDraft";
import useAuth from "@/app/hooks/useAuth";
import { getSettings } from "@/app/services/Settings";

interface FormData {
    _id?: string;
    firstName: string;
    lastName: string;
    middleName?: string;
    dateOfBirth?: string;
    nationality?: string;
    gender?: string;
    place_of_birth?: string;
    address?: string;
    student_phone?: string;
    student_country_code?: string;
    guardian_address?: string;
    guardian_phone?: string;
    guardian_country_code?: string;
    guardian_name?: string;
    guardian_occupation?: string;
    guardian_email?: string;
    guardian_relationship?: string;
    emergency_contact_name?: string;
    emergency_contact_phone?: string;
    emergency_contact_country_code?: string;
    emergency_contact_relationship?: string;
    previous_school?: string;
    class_level: string;
    guardian_agreed_to_terms: boolean;
    transcript_reportcard: boolean;
    health_condition?: string;
    doctors_name?: string;
    doctors_phone?: string;
    doctor_country_code?: string;
    registered?: boolean; // ✅ Made property required
    selectedFees: string[];         // ✅ required for fee checkboxes
    selectedResources: string[];    // ✅ required for resource checkboxes
    paymentMode: "full" | "installment";
    installments: number;
    installmentDates: string[];
    applyScholarship: boolean;
    scholarshipAmount: number;
    status?: "enrolled" | "graduated" | "dropped" | "not enrolled";
    scholarshipPercentage: number; // ✅ Made property required
}
const steps = [
    { title: "Personal Information", description: "Enter the student's details." },
    { title: "Contact Information", description: "Provide the student's contact info." },
    { title: "Parent/Guardian Details", description: "Provide information for parent/guardian." },
    { title: "Academic Information", description: "Details regarding the student's academic background." },
    { title: "Emergency Contact", description: "Provide emergency contact information." },
    { title: "Medical Information", description: "Fill in any medical history if applicable." },
    { title: "Consent and Declaration", description: "Agree to terms and conditions." },
    { title: "Fee Information", description: "Choose the fee structure and payment options." },
    { title: "Payment Confirmation", description: "Provide proof of payment and registration confirmation." },
];

function StepIndicator({ steps, currentStep }: { steps: any[]; currentStep: number }) {

    return (
        <div className="flex overflow-x-auto custom-scrollbar gap-4 my-6">
            {steps.map((step, index) => {
                const isActive = index === currentStep;
                const isCompleted = index < currentStep;

                return (
                    <div key={index} className="flex flex-col items-center text-center flex-shrink-0 w-24">
                        <div
                            className={`rounded-full w-10 h-10 flex items-center justify-center text-white font-bold ${isActive ? "bg-gray-800" : isCompleted ? "bg-teal" : "bg-gray-500"
                                }`}
                        >
                            {index + 1}
                        </div>
                        <span className={`mt-1 text-xs ${isActive ? "font-semibold text-foreground" : "text-foreground"}`}>
                            {step.title.split(" ")[0]}
                        </span>
                    </div>
                );
            })}
        </div>
    );
}

function RegistrationContent({
    steps,
    currentStep,
    formData,
    handleChange,
    handleNext,
    handleBack,
    setStudentCountryCode,
    setGuardianCountryCode,
    setEmergencyContactCountryCode,
    setDoctorCountryCode,
    sameAddressAsChild,
    setSameAddressAsChild,
    sameEmergencyAsGuardian,
    setSameEmergencyAsGuardian,
    setIsNotificationCard,
    setNotificationMessage,
    setNotificationType,
    handleDeleteDraft,

}: {
    steps: any[];
    currentStep: number;
    formData: FormData;
    handleChange: (e: React.ChangeEvent<any>) => void;
    handleNext: () => void;
    handleBack: () => void;
    setStudentCountryCode: (value: string) => void;
    setGuardianCountryCode: (value: string) => void;
    setEmergencyContactCountryCode: (value: string) => void;
    setDoctorCountryCode: (value: string) => void;
    sameAddressAsChild: boolean;
    setSameAddressAsChild: (value: boolean) => void;
    setIsNotificationCard: (value: boolean) => void;
    setNotificationMessage: (value: string) => void;
    setNotificationType: (value: "success" | "error" | "info" | "warning") => void;
    sameEmergencyAsGuardian: boolean;
    setSameEmergencyAsGuardian: (value: boolean) => void;
    handleDeleteDraft: () => void;

}) {
    const searchParams = useSearchParams();
    const schoolId = searchParams.get("schoolId");
    const [feeList, setFeeList] = useState<FeeSchema[]>([]);
    const [feeLoading, setFeeLoading] = useState(false);
    const [classLevels, setClassLevels] = useState<ClassLevelSchema[]>([])
    const [isCompleted, setIsCompleted] = useState(false);
    const [resourceList, setResourceList] = useState<SchoolResourceSchema[]>([]);
    const [resourceLoading, setResourceLoading] = useState(true);
    const [classLoading, setClassLoading] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
    const [submitMessage, setSubmitMessage] = useState("");



    interface Student {
        student_id: string;
        first_name: string;
        last_name: string;
        class_level: string;
    }
    const [schoolData, setSchoolData] = useState<any>(null);
    const [paymentItems, setPaymentItems] = useState<{ description: string; amount: number }[]>([]);
    const [student, setStudent] = useState<Student | null>(null);
    const [paymentData, setPaymentData] = useState<any>(null);

    const receiptData = {
        student: student!,
        school: {
            name: schoolData?.name,
            logoUrl: schoolData?.logo || "/assets/logo.png",
            email: schoolData?.email,
            phone_number: schoolData?.phone_number
        },
        paymentItems: paymentItems,
        receiptId: paymentData?.receipt_number,
        date: new Date(),
        taxRate: 0,
        applyScholarship: formData.applyScholarship,
        scholarshipPercentage: Number(formData.scholarshipPercentage) || 0,
        installments: formData.paymentMode === "installment" ? formData.installments : undefined,
        installmentDates: formData.paymentMode === "installment" ? formData.installmentDates : undefined,
        paidInstallmentNumber: 1,
        //   paidInstallmentNumber: paymentData?.installments?.filter((i: any) => i.paid).length || 0,

    };
    const router = useRouter();

    const clearSubmitStatus = () => {
        setSubmitStatus(null);
        setSubmitMessage("");
        router.replace(`/school-admin/students`);
    };

    const classOptions = classLevels.map((level) => ({
        label: level.name,
        value: level._id,
    }));
    const { currentAcademicYear } = useAcademicYearContext();

    const fetchClassLevel = async () => {
        try {
            setClassLoading(true);
            if (!schoolId) {
                throw new Error("School ID is required to fetch fees.");
            }
            const level = await getClassLevelsBySchoolId(schoolId); // Replace with dynamic if needed
            setClassLevels(level);
        } catch (error) {
            console.error("Error fetching fees:", error);
        } finally {
            setClassLoading(true);
        }
    };
    const fetchFees = async () => {
        try {
            setFeeLoading(true);

            if (!schoolId) {
                throw new Error("School ID is required to fetch fees.");
            }

            const settings = await getSettings();
            // Define the mandatory Scholarify fee
            if (settings) {
                const fees = await getFeesBySchoolId(schoolId);
                const SCHOLARIFY_FEE: FeeSchema = {
                    _id: "507f1f77bcf86cd799439011",
                    fee_type: "Scholarify Fee",
                    amount: settings?.credit?.resell_price_per_credit,
                    school_id: schoolId,
                };

                // Append it if not already in the list (defensive check)
                const feeListWithScolafify = fees.some(fee => fee._id === SCHOLARIFY_FEE._id)
                    ? fees
                    : [...fees, SCHOLARIFY_FEE];

                setFeeList(feeListWithScolafify);
            }


        } catch (error) {
            console.error("Error fetching fees:", error);
        } finally {
            setFeeLoading(false);
        }
    };

    const fetchResources = async () => {
        try {
            if (!schoolId) {
                throw new Error("School ID is required to fetch fees.");
            } const resources = await getSchoolResourcesBySchoolId(schoolId);
            setResourceList(resources);
        } catch (error) {
            console.error("Error loading school resources:", error);
        } finally {
            setResourceLoading(false);
        }
    };


    // console.log("academic year:",acadamicYear);
    useEffect(() => {

        fetchFees();
        fetchResources();
        fetchClassLevel();
    }, [currentStep]);
    // console.log("here is fee list :",feeList);

    const renderStepContent = () => {
        switch (currentStep) {
            case 0:
                return (
                    <StudentDetailsForm
                        formData={formData}
                        handleChange={handleChange}
                        schoolId={schoolId || ""}
                    />

                );
            case 1:
                return (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <CustomInput
                            label="Student Address"
                            id="address"
                            placeholder="(Optional)"
                            name="address"
                            value={formData.address || ""}
                            onChange={handleChange}
                        />
                        <CustomPhoneInput
                            label="Student Phone Number (Optional)"
                            id="student_phone"
                            name="student_phone"
                            value={formData.student_phone || ''}
                            onChange={handleChange}
                            countryCode={formData.student_country_code || ''}
                            countryCodeName="student_country_code"   // <-- THIS IS CRUCIAL
                            onCountryCodeChange={handleChange}       // pass same handleChange to update formData
                            required={false}
                        />
                    </div>
                );

            case 2:
                return (
                    <GuardianDetailsForm
                        formData={formData}
                        handleChange={handleChange}
                        sameAddressAsChild={sameAddressAsChild}
                        setSameAddressAsChild={setSameAddressAsChild}
                        countryCode={formData.guardian_country_code || ""}
                        setCountryCode={setGuardianCountryCode}
                    />
                );
            case 3:
                return (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <CustomSelect
                            label="Select Class"
                            id="class_level"
                            name="class_level"
                            value={formData.class_level || ""}
                            onChange={handleChange}
                            required
                            options={classOptions}
                            placeholder="Select Class"
                        />
                        <CustomInput
                            label="Previous School/Institution"
                            id="previous_school"
                            placeholder="(Optional)"
                            name="previous_school"
                            value={formData.previous_school || ""}
                            onChange={handleChange}
                        />
                        <CustomCheckboxInput
                            label="Check the box if copies of transcripts or report cards were provided"
                            id="transcript_reportcard"
                            name="transcript_reportcard"
                            checked={formData.transcript_reportcard}
                            onChange={handleChange}
                        />
                    </div>
                );
            case 4:
                return (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <CustomCheckboxInput
                            label="Same as Guardian Info"
                            id="sameEmergencyAsGuardian"
                            name="sameEmergencyAsGuardian"
                            checked={sameEmergencyAsGuardian}
                            onChange={(e) => setSameEmergencyAsGuardian(e.target.checked)}
                        />

                        <CustomInput
                            label="Emergency Contact Name"
                            id="emergency_contact_name"
                            placeholder="(Required)"
                            name="emergency_contact_name"
                            value={formData.emergency_contact_name || ""}
                            onChange={handleChange}
                            required
                        />
                        <CustomSelect
                            label="Select Relationship"
                            id="emergency_contact_relationship"
                            name="emergency_contact_relationship"
                            value={formData.emergency_contact_relationship || ""}
                            onChange={handleChange}
                            required
                            options={[
                                { label: "Mother", value: "Mother" },
                                { label: "Father", value: "Father" },
                                { label: "Brother", value: "Brother" },
                                { label: "Sister", value: "Sister" },
                                { label: "Aunty", value: "Aunty" },
                                { label: "Uncle", value: "Uncle" },
                                { label: "Grand Mother", value: "Grand Mother" },
                                { label: "Grand Father", value: "Grand Father" },
                                { label: "Other", value: "Other" },
                            ]}
                            placeholder="Select Relationship"
                        />

                        <CustomPhoneInput
                            label="Emergency Contact Phone"
                            id="emergency_contact_phone"
                            name="emergency_contact_phone"
                            value={formData.emergency_contact_phone || ''}
                            onChange={handleChange}
                            countryCode={formData.emergency_contact_country_code || ""}
                            countryCodeName="emergency_contact_country_code" // <-- THIS IS CRUCIAL
                            onCountryCodeChange={handleChange}
                        />
                    </div>
                );
            case 5:
                return (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <CustomTextarea
                            label="Health Condition (Write notes if student have a health condition)"
                            id="health_condition"
                            name="health_condition"
                            value={formData.health_condition || ""}
                            onChange={handleChange}
                        />
                        <CustomInput
                            label="Doctor's Name"
                            id="doctors_name"
                            placeholder="(Optional)"
                            name="doctors_name"
                            value={formData.doctors_name || ""}
                            onChange={handleChange}
                        />
                        <CustomPhoneInput
                            label="Doctor's Phone Number (Optional)"
                            id="doctors_phone"
                            name="doctors_phone"
                            value={formData.doctors_phone || ''}
                            onChange={handleChange}
                            countryCode={formData.doctor_country_code || ""}
                            onCountryCodeChange={handleChange}
                            countryCodeName="doctor_country_code" // <-- THIS IS CRUCIAL
                        />
                    </div>
                );
            case 6:
                return (
                    <ConsentDeclaration
                        checked={formData.guardian_agreed_to_terms}
                        onChange={handleChange}
                    />
                );
            case 7:

                return (
                    <div className="space-y-6">
                        <FeesAndResourcesSection
                            formData={formData}
                            handleChange={handleChange}
                            feeList={feeList}
                            feeLoading={feeLoading}
                            resourceList={resourceList}
                            resourceLoading={resourceLoading} applyScholarship={false} scholarshipAmount={0} />
                    </div>
                );
            case 8:
                return (
                    <RegistrationSummary
                        formData={formData}
                        feeList={feeList}
                        resourceList={resourceList}
                    />
                );
            default:
                return null;
        }
    };
    interface Fee {
        amount?: number;
    }

    interface Resource {
        amount?: number;
    }

    function calculateTotalFee(selectedFees: Fee[], selectedResources: Resource[], scholarshipPercentage: number): number {
        const feesTotal = selectedFees.reduce((sum, fee) => sum + (fee.amount || 0), 0);
        const resourcesTotal = selectedResources.reduce((sum, res) => sum + (res.amount || 0), 0);
        const grossTotal = feesTotal + resourcesTotal;
        const discount = (grossTotal * scholarshipPercentage) / 100;
        return grossTotal - discount;
    }
    function formatPhoneNumberToE164(countryCode: string, localNumber: string): string {
        const cleanedCountryCode = countryCode.replace(/\D/g, '');
        const cleanedLocalNumber = localNumber.replace(/\D/g, '');
        return `+${cleanedCountryCode}${cleanedLocalNumber}`;
    }


    const handleSubmit = async () => {
        setIsSubmitting(true);
        setSubmitStatus(null);
        console.log("To be submitted:", formData);

        try {
            const totalFee = calculateTotalFee(
                formData.selectedFees.map((feeId) => feeList.find((fee) => fee._id === feeId)!).filter(Boolean) as Fee[],
                formData.selectedResources.map((resourceId) => resourceList.find((resource) => resource._id === resourceId)!).filter(Boolean) as Resource[],
                formData.scholarshipPercentage || 0
            );

            const studentData = {

                school_id: searchParams.get("schoolId") || "",
                class_level: formData.class_level || "",

                first_name: formData.firstName,
                last_name: formData.lastName,
                middle_name: formData.middleName || "",
                name: `${formData.firstName} ${formData.middleName} ${formData.lastName}`,
                date_of_birth: formData.dateOfBirth || "",
                nationality: formData.nationality || "Cameroonian",
                gender: formData.gender || "",
                place_of_birth: formData.place_of_birth || "",
                address: formData.address || "",
                phone: formData.student_country_code && formData.student_phone
                    ? formatPhoneNumberToE164(formData.student_country_code, formData.student_phone)
                    : "",

                guardian_name: formData.guardian_name || "",
                guardian_phone: formData.guardian_country_code && formData.guardian_phone
                    ? formatPhoneNumberToE164(formData.guardian_country_code, formData.guardian_phone)
                    : "",
                guardian_address: formData.guardian_address || "",
                guardian_email: formData.guardian_email || "",
                guardian_relationship: formData.guardian_relationship as "Mother" | "Father" | "Brother" | "Sister" | "Aunty" | "Uncle" | "Grand Mother" | "Grand Father" | "Other" | undefined,
                guardian_occupation: formData.guardian_occupation || "",

                emergency_contact_name: formData.emergency_contact_name || "",
                emergency_contact_phone: formData.emergency_contact_country_code && formData.emergency_contact_phone
                    ? formatPhoneNumberToE164(formData.emergency_contact_country_code, formData.emergency_contact_phone)
                    : "",
                emergency_contact_relationship: formData.emergency_contact_relationship as "Mother" | "Father" | "Brother" | "Sister" | "Aunty" | "Uncle" | "Grand Mother" | "Grand Father" | "Other" | undefined,

                previous_school: formData.previous_school || "",
                transcript_reportcard: formData.transcript_reportcard || false,

                health_condition: formData.health_condition || "",
                doctors_name: formData.doctors_name || "",
                doctors_phone: formData.doctor_country_code && formData.doctors_phone
                    ? formatPhoneNumberToE164(formData.doctor_country_code, formData.doctors_phone)
                    : "",

                selectedFees: formData.selectedFees || [],
                selectedResources: formData.selectedResources || [],
                paymentMode: formData.paymentMode || "full",
                installments: formData.installments || 1,
                installmentDates: formData.installmentDates || [new Date().toISOString().split("T")[0]],
                applyScholarship: formData.applyScholarship || false,
                scholarshipAmount: formData.scholarshipAmount || 0,
                scholarshipPercentage: formData.scholarshipPercentage || 0,
                registered: true,
                avatar: "",
                fees: totalFee,
                guardian_agreed_to_terms: formData.guardian_agreed_to_terms || false,
                enrollement_date: "",
                status: formData.status || "not enrolled",
            };

            const data = await createStudent(studentData);
            //console.log(data);
            // Do something with `student` or show success message
            //console.log("create student data",data)
            if (data.ok) {
                const numberOfInstallments = formData.installments || 1;
                const amountPerInstallment = Number((totalFee / numberOfInstallments).toFixed(2));

                // Helper to generate dates if not provided
                const generateInstallmentDates = (count: number): Date[] => {
                    const dates: Date[] = [];
                    const today = new Date();

                    for (let i = 0; i < count; i++) {
                        const dueDate = new Date(today);
                        dueDate.setMonth(today.getMonth() + i); // Spread over next few months
                        dates.push(dueDate);
                    }

                    return dates;
                };

                // Use provided dates or generate them
                const installmentDates: Date[] =
                    Array.isArray(formData.installmentDates) && formData.installmentDates.length === numberOfInstallments
                        ? formData.installmentDates.map((d: any) => new Date(d))
                        : generateInstallmentDates(numberOfInstallments);

                const installments =
                    formData.paymentMode === "installment"
                        ? installmentDates.map((dueDate, index) => ({
                            amount: amountPerInstallment,
                            dueDate: dueDate.toISOString().split("T")[0],
                            paid: index === 0, // Only first installment is paid
                        }))
                        : [];

                const feePaymentData = {
                    student_id: (data.student as { _id: string })._id,
                    school_id: searchParams.get("schoolId") || "",
                    class_level: formData.class_level || "",
                    academic_year: currentAcademicYear,

                    selectedFees: formData.selectedFees || [],
                    selectedResources: formData.selectedResources || [],

                    paymentMode: formData.paymentMode || "full",
                    totalAmount: totalFee,

                    installments,
                };
                const paymentData = await createFeePayment(feePaymentData);
                setPaymentData(paymentData);
                if (paymentData) {

                    const schoolData = await getSchoolBy_id(searchParams.get("schoolId") || "");
                    const paymentItems = [
                        ...formData.selectedFees.map((feeId) => {
                            const fee = feeList.find((f) => f._id === feeId);
                            return fee ? { description: fee.fee_type, amount: fee.amount } : null;
                        }),
                        ...formData.selectedResources.map((resourceId) => {
                            const res = resourceList.find((r) => r._id === resourceId);
                            return res ? { description: res.name, amount: res.price } : null;
                        }),
                    ].filter(Boolean) as { description: string; amount: number }[];
                    setPaymentItems(paymentItems);
                    const class_level = await getClassLevelById(formData.class_level);
                    const student: Student = {
                        student_id: (data.student as { student_id: string }).student_id,
                        first_name: formData.firstName,
                        last_name: formData.lastName,
                        class_level: class_level.name || "Not Assigned",
                    };
                    setStudent(student);

                    const school: { name: string, logoUrl: string, email: string, phone_number: string } = {
                        name: schoolData.name || "Your School Name",
                        logoUrl: schoolData.logo || "/assets/logo.png",
                        email: schoolData.email || "",
                        phone_number: schoolData.phone_number || "",
                    };
                    setSchoolData(school);

                    const parentPayload = {
                        name: formData.guardian_name,
                        phone: formData.guardian_country_code && formData.guardian_phone
                            ? formatPhoneNumberToE164(formData.guardian_country_code, formData.guardian_phone)
                            : "",
                        email: formData.guardian_email,
                        address: formData.guardian_address || "",
                        school_ids: [searchParams.get("schoolId") || ""],
                        student_ids: [(data.student as { _id: string })._id], // link newly created student
                    };

                    const parentData = await registerParent(parentPayload);
                    if (!parentData) {
                        setSubmitStatus("failure");
                        setNotificationMessage("Registration failed. Please try again.");
                        setSubmitMessage("Registration failed. Please try again.");
                        setNotificationType("error");
                        setIsNotificationCard(true);
                        setIsSubmitting(false);
                    }
                    await deleteRegistrationDraft(schoolId as string, formData._id as string); // Assuming `draftId` is available and you have this API set up
                    setIsCompleted(true);
                    setSubmitStatus("success");
                    setNotificationMessage("Registration successful!");
                    setSubmitMessage("Registration successful!");
                    setNotificationType("success");
                    setIsNotificationCard(true);
                    setIsSubmitting(false);
                }

            }

        } catch (error) {
            console.error("Error During Registration:", error);

            let errorMessage =
                error instanceof Error
                    ? error.message
                    : "An unknown error occurred while creating the student.";

            if (
                typeof error === "object" &&
                error !== null &&
                "response" in error &&
                typeof (error as any).response === "object" &&
                (error as any).response?.status === 409
            ) {
                errorMessage = "A student with the same name and date of birth already exists in this school.";
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }

            setSubmitStatus("failure");
            setNotificationMessage(errorMessage);
            setSubmitMessage(errorMessage)
            setNotificationType("error");
            setIsNotificationCard(true);
            setIsSubmitting(false);
        }
    };




    return (
        <div>
            {formData._id && (
                <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 1 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                    type="button"
                    onClick={handleDeleteDraft}
                    className="px-4 py-2 border border-red-500 text-red-600 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center gap-2 disabled:opacity-50"
                >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete Draft
                </motion.button>
            )}
            <StepIndicator steps={steps} currentStep={currentStep} />
            <div className="p-4 shadow-md rounded-md">
                <h2 className="text-xl font-semibold mb-4">{steps[currentStep].title}</h2>
                {renderStepContent()}
                <div className="mt-6 gap-6 flex">
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 1 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                        type="button"
                        onClick={handleBack}
                        disabled={currentStep === 0}
                        className="px-4 py-2 border border-gray-500 text-foreground rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2 disabled:opacity-50"
                    >
                        <ArrowLeft className="w-4 h-4" />
                        Back
                    </motion.button>

                    {currentStep < steps.length - 1 ? (
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 1 }}
                            transition={{ type: 'spring', stiffness: 300 }}
                            onClick={handleNext}
                            type="button"
                            className="px-4 py-2 border border-gray-500 text-foreground rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                        >
                            Next
                            <ArrowRight className="w-4 h-4" />
                        </motion.button>
                    ) : (
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 1 }}
                            transition={{ type: 'spring', stiffness: 300 }}
                            type="button"
                            onClick={handleSubmit}
                            className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 flex items-center gap-2"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? (
                                <>
                                    <CircularLoader size={18} color="teal-500" />
                                    Confirming...
                                </>
                            ) : (
                                "Confirm Payment & Register"
                            )}
                        </motion.button>
                    )}
                </div>
            </div>
            {submitStatus && (
                <FeedbackPopup
                    status={submitStatus === "failure" ? "error" : "success"}
                    message={submitMessage}
                    onClose={clearSubmitStatus}
                    receiptData={receiptData}
                />
            )}
        </div>
    );

}

export default function ViewParentPage() {
    const searchParams = useSearchParams();
    const schoolId = searchParams.get("schoolId");
    const { user } = useAuth();
    const router = useRouter();
    const [formData, setFormData] = useState<FormData>({
        firstName: "",
        lastName: "",
        middleName: "",
        dateOfBirth: "",
        nationality: 'Cameroonian',
        gender: "",
        place_of_birth: "",
        address: "",
        student_phone: "",
        guardian_phone: "",
        student_country_code: "+237",
        guardian_country_code: "+237",
        emergency_contact_country_code: "+237",
        doctor_country_code: "+237",
        guardian_name: "",
        guardian_address: "",
        guardian_occupation: "",
        guardian_email: "",
        guardian_relationship: "",
        previous_school: "",
        class_level: "",
        guardian_agreed_to_terms: false,
        transcript_reportcard: false,
        emergency_contact_name: "",
        emergency_contact_phone: "",
        emergency_contact_relationship: "",
        health_condition: "",
        doctors_name: "",
        doctors_phone: "",
        registered: false,
        selectedFees: [],
        selectedResources: [],
        paymentMode: "full",
        installments: 1,
        installmentDates: [new Date().toISOString().split("T")[0]],
        applyScholarship: false,
        scholarshipAmount: 0,
        scholarshipPercentage: 0, // Ensure default value is always provided
        status: "not enrolled",
    });

    const [student_country_code, setStudentCountryCode] = useState("+237");
    const [guardian_country_code, setGuardianCountryCode] = useState("+237");
    const [emergency_contact_country_code, setEmergencyContactCountryCode] = useState("+237");
    const [doctor_country_code, setDoctorCountryCode] = useState("+237");

    const [currentStep, setCurrentStep] = useState(0);
    const [isDraftLoaded, setIsDraftLoaded] = useState(false);
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");
    const [sameAddressAsChild, setSameAddressAsChild] = useState(false);
    const [sameEmergencyAsGuardian, setSameEmergencyAsGuardian] = useState(false);
    // ✅ Add validateStep here
    const validateStep = () => {
        switch (currentStep) {
            case 0:
                return (
                    formData.firstName?.trim() &&
                    formData.lastName?.trim() &&
                    formData.dateOfBirth?.trim() &&
                    formData.nationality?.trim() &&
                    formData.gender?.trim()
                );
            case 1:
                return true;
            case 2:
                return (
                    formData.guardian_name?.trim() &&
                    formData.guardian_relationship?.trim() &&
                    formData.guardian_address?.trim() &&
                    formData.guardian_phone?.trim()
                );
            case 3:
                return !!formData.class_level;
            case 4:
                return (
                    formData.emergency_contact_name?.trim() &&
                    formData.emergency_contact_relationship?.trim() &&
                    formData.emergency_contact_phone?.trim()
                );
            case 5:
                return true;
            case 6:
                return formData.guardian_agreed_to_terms === true;
            case 7:
                return Array.isArray(formData.selectedFees) && formData.selectedFees.length > 0;
            case 8:
                return true;
            default:
                return false;
        }
    };
    // Prefill parent address if same as child
    useEffect(() => {
        if (sameAddressAsChild) {
            setFormData((prev) => ({
                ...prev,
                guardian_address: prev.address || "", // student address -> guardian address
            }));
        } else {
            setFormData((prev) => ({
                ...prev,
                guardian_address: "", // clear guardian address when unchecked
            }));
        }
    }, [sameAddressAsChild]);
    // Prefill emergency contact from guardian info
    useEffect(() => {
        if (sameEmergencyAsGuardian) {
            setFormData((prev) => ({
                ...prev,
                emergency_contact_name: prev.guardian_name || "",
                emergency_contact_relationship: prev.guardian_relationship || "",
                emergency_contact_phone: prev.guardian_phone || "",
                emergency_contact_country_code: prev.guardian_country_code || "",
            }));
        } else {
            setFormData((prev) => ({
                ...prev,
                emergency_contact_name: "",
                emergency_contact_relationship: "",
                emergency_contact_phone: "",
                emergency_contact_country_code: "",

            }));
        }
    }, [sameEmergencyAsGuardian]);

    const handleChange = useCallback((e: React.ChangeEvent<any>) => {
        const { name, type, value, checked } = e.target;

        if (name === "installmentDates") {
            setFormData((prev) => ({
                ...prev,
                installmentDates: value,
            }));
        } else if (name === "installments") {
            const count = parseInt(value, 10);
            const today = new Date();
            const newDates = Array.from({ length: count }, (_, i) => {
                const date = new Date(today);
                date.setMonth(today.getMonth() + i);
                return date.toISOString().split("T")[0];
            });

            setFormData((prev) => ({
                ...prev,
                installments: count,
                installmentDates: newDates,
            }));
        }
        if ((name === "selectedFees" || name === "selectedResources") && type === "checkbox") {

            setFormData((prev) => {
                const key = name as "selectedFees" | "selectedResources";
                let updated = checked
                    ? [...prev[key], value]
                    : prev[key].filter((id) => id !== value);

                if (name === "selectedFees") {
                    const mandatoryFeeId = "507f1f77bcf86cd799439011";
                    if (!updated.includes(mandatoryFeeId)) {
                        updated = [...updated, mandatoryFeeId];
                    }
                }

                return {
                    ...prev,
                    [key]: updated,
                };
            });
        }
        else {
            setFormData((prev) => ({
                ...prev,
                [name]: type === "checkbox" ? checked : value,
            }));
        }
    }, []);
    const fetchDraft = async () => {
        const draft = await getRegistrationDrafts(schoolId as string, user?._id as string);
        console.log("Draft fetched:", draft);

        // If draft is an array and has at least one element, handle it as an array
        if (draft && Array.isArray(draft) && draft.length > 0) {
            const draftData = draft[0]; // Get the first draft
            setFormData({
                ...draftData,
                status: "not enrolled",
                dateOfBirth: draftData.dateOfBirth instanceof Date
                    ? draftData.dateOfBirth.toISOString().split("T")[0]
                    : draftData.dateOfBirth || "",
                installmentDates: Array.isArray(draftData.installmentDates)
                    ? draftData.installmentDates.map((d: any) =>
                        d instanceof Date ? d.toISOString().split("T")[0] : typeof d === "string" ? d : ""
                    )
                    : [],
            });
            setCurrentStep((draftData as any).currentStep || 0);
        }
        // If it's not an array, check if it's a valid object
        else if (draft && typeof draft === "object" && draft !== null) {
            const draftData = draft as Partial<FormData>;
            setFormData((prev) => ({
                ...prev,
                ...draftData,
                dateOfBirth: (typeof draftData.dateOfBirth === "object" && draftData.dateOfBirth !== null && (draftData.dateOfBirth as any) instanceof Date)
                    ? (draftData.dateOfBirth as Date).toISOString().split("T")[0]
                    : draftData.dateOfBirth || prev.dateOfBirth,
                installmentDates: Array.isArray(draftData.installmentDates)
                    ? draftData.installmentDates.map((d: any) =>
                        d instanceof Date ? d.toISOString().split("T")[0] : typeof d === "string" ? d : ""
                    )
                    : prev.installmentDates,
            }));
            setCurrentStep((draftData as any).currentStep || 0);
        }
        // If no draft found, initialize default values
        else {
            setFormData((prev) => ({ ...prev, currentStep: 1 }));
        }

        setIsDraftLoaded(true); // Set draft loaded flag
    };

    useEffect(() => {


        fetchDraft();
    }, [schoolId, user]);

    const handleDeleteDraft = () => {
        setShowDeleteConfirmation(true);
    };

    const confirmDeleteDraft = async () => {
        if (!schoolId || !formData._id) {
            return;
        }
        try {
            await deleteRegistrationDraft(schoolId as string, formData._id as string);
            setNotificationMessage("Draft deleted successfully.");
            setNotificationType("success");
            setIsNotificationCard(true);
            setShowDeleteConfirmation(false);

            // Reset form data and redirect
            setTimeout(() => {
                router.push(`/school-admin/students/manage/register?schoolId=${schoolId}`);
            }, 1500);
        } catch (error) {
            let errorMessage =
                error instanceof Error
                    ? error.message
                    : "An unknown error occurred while deleting the draft.";

            setNotificationMessage(errorMessage);
            setNotificationType("error");
            setIsNotificationCard(true);
            setShowDeleteConfirmation(false);
        }
    };

    const cancelDeleteDraft = () => {
        setShowDeleteConfirmation(false);
    };

    const handleNext = useCallback(async () => {
        // Validate the current step's data

        if (formData?.registered) {
            setNotificationMessage("Student is already registered.");
            setIsNotificationCard(true);
            setNotificationType('error');
            return;
        }
        if (!validateStep()) {
            setNotificationMessage("Please fill all required fields before proceeding.");
            setIsNotificationCard(true);
            setNotificationType('error');
            return;
        }

        // Save the draft data to the backend when moving to the next step
        try {
            // Assuming you have the draft ID (for update) or an empty draft (for create)
            // Ensure gender is one of the valid types
            const validGender: "Male" | "Female" | "Other" | undefined =
                formData.gender === "Male" || formData.gender === "Female" || formData.gender === "Other"
                    ? formData.gender
                    : undefined; // Set undefined if invalid gender value is provided

            // Convert installmentDates strings to Date objects
            const validInstallmentDates: Date[] = formData.installmentDates
                ? formData.installmentDates.map((date) => new Date(date))  // Convert each date string to Date object
                : [];

            // Prepare the draft data
            // Omit _id when creating a new draft to satisfy the RegistrationDraftSchema type
            const draftData = {
                ...formData,
                status: formData.status as "draft" | "enrolled" | "not enrolled" | "pending payment",
                currentStep: currentStep + 1,
                dateOfBirth: formData.dateOfBirth ? new Date(formData.dateOfBirth) : null,
                gender: validGender,
                user_id: user?._id,
                school_id: schoolId as string,
                installmentDates: validInstallmentDates,
            };

            // If it's a new draft, create it (omit _id)
            if (!draftData._id) {
                const { _id, ...draftDataWithoutId } = draftData;
                const createdDraft = await createRegistrationDraft(schoolId as string, draftDataWithoutId as RegistrationDraftSchema);
                if (createdDraft) {
                    setNotificationMessage("Draft created successfully.");
                    setIsNotificationCard(true);
                    setNotificationType('success');
                    fetchDraft();
                } else {
                    setNotificationMessage("Error creating draft.");
                    setIsNotificationCard(true);
                    setNotificationType('error');
                }
            } else {
                // If it's an existing draft, update it
                const updatedDraft = await updateRegistrationDraft(schoolId as string, draftData._id as string, draftData as RegistrationDraftSchema);
                if (updatedDraft) {
                    setNotificationMessage("Draft updated successfully.");
                    setIsNotificationCard(true);
                    setNotificationType('success');
                    fetchDraft();
                } else {
                    setNotificationMessage("Error updating draft.");
                    setIsNotificationCard(true);
                    setNotificationType('error');
                }
            }

            // Proceed to the next step
            setCurrentStep((prev) => (prev < steps.length - 1 ? prev + 1 : prev));

        } catch (error) {
            console.error("Error while saving draft:", error);
            setNotificationMessage("An error occurred while saving the draft.");
            setIsNotificationCard(true);
            setNotificationType('error');
        }
    }, [formData, currentStep, steps.length, schoolId]);

    const handleBack = useCallback(() => {

        setCurrentStep((prev) => Math.max(prev - 1, 0));
    }, []);

    return (
        <SchoolLayout
            navigation={{
                icon: GraduationCap,
                title: "Student Registration",
                baseHref: "/school-admin/students"
            }}
            showGoPro={true}
            onLogout={() => console.log("Logout")}
        >
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                />
            )}
            <RegistrationContent
                steps={steps}
                currentStep={currentStep}
                formData={formData}
                handleChange={handleChange}
                handleNext={handleNext}
                handleBack={handleBack}
                setStudentCountryCode={setStudentCountryCode}
                setGuardianCountryCode={setGuardianCountryCode}
                setDoctorCountryCode={setDoctorCountryCode}
                setEmergencyContactCountryCode={setEmergencyContactCountryCode}
                sameAddressAsChild={sameAddressAsChild}
                setSameAddressAsChild={setSameAddressAsChild}
                sameEmergencyAsGuardian={sameEmergencyAsGuardian}
                setSameEmergencyAsGuardian={setSameEmergencyAsGuardian}
                setIsNotificationCard={setIsNotificationCard}
                setNotificationMessage={setNotificationMessage}
                setNotificationType={setNotificationType}
                handleDeleteDraft={handleDeleteDraft}
            />

            {/* Delete Confirmation Dialog */}
            {showDeleteConfirmation && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                        <div className="flex items-center gap-3 mb-4">
                            <div className="flex-shrink-0">
                                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div>
                                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                                    Delete Registration Draft
                                </h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    Are you sure you want to delete this registration draft? This action cannot be undone.
                                </p>
                            </div>
                        </div>
                        <div className="flex gap-3 justify-end">
                            <button
                                onClick={cancelDeleteDraft}
                                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={confirmDeleteDraft}
                                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
                            >
                                Delete Draft
                            </button>
                        </div>
                    </div>
                </div>
            )}

        </SchoolLayout>
    );
}
