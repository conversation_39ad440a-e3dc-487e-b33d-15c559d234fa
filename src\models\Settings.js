const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  general: {
    platform_name: { type: String, required: true },
    support_email: { type: String, required: true },
    default_language: { type: String, required: true, default: 'en' },
    maintenance_mode: { type: Boolean, default: false },
    maintenance_message: { type: String, default: '' },
  },
  credit: {
    resell_price_per_credit: { type: Number, required: false, min: 0 },
    buy_price_per_credit: { type: Number, required: false, min: 0 },
  },
}, {
  timestamps: true,
});

const Settings = mongoose.models.Settings || mongoose.model('Settings', settingsSchema);
module.exports = Settings;
