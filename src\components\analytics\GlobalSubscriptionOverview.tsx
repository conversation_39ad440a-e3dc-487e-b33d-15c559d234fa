"use client";

import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>hart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from 'recharts';
import {
  Crown,
  Users,
  TrendingUp,
  DollarSign,
  Calendar,
  Award,
  Target
} from 'lucide-react';
import { motion } from 'framer-motion';
import { getSubscriptionDistribution, SubscriptionDistributionResponse, getSubscriptionPlanTypes, PlanType } from '@/app/services/SubscriptionServices';
import { useTranslation } from '@/hooks/useTranslation';

interface SubscriptionData {
  plan_type: string;
  count: number;
  revenue: number;
  color: string;
}

interface MonthlyData {
  month: string;
  basic: number;
  premium: number;
  enterprise: number;
  revenue: number;
}

interface GlobalSubscriptionOverviewProps {
  className?: string;
}

const GlobalSubscriptionOverview: React.FC<GlobalSubscriptionOverviewProps> = ({
  className = ''
}) => {
  const { t } = useTranslation();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData[]>([]);
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [planTypes, setPlanTypes] = useState<PlanType[]>([]);

  // Données de démonstration pour fallback
  const generateDemoData = () => {
    const subscriptions: SubscriptionData[] = [
      { plan_type: 'Basic', count: 45, revenue: 135000, color: '#3B82F6' },
      { plan_type: 'Standard', count: 28, revenue: 168000, color: '#10B981' },
      { plan_type: 'Custom', count: 12, revenue: 144000, color: '#F59E0B' }
    ];

    const monthly: MonthlyData[] = [
      { month: 'Jan', basic: 10, premium: 5, enterprise: 2, revenue: 85000 },
      { month: 'Fév', basic: 12, premium: 7, enterprise: 3, revenue: 102000 },
      { month: 'Mar', basic: 15, premium: 8, enterprise: 4, revenue: 125000 },
      { month: 'Avr', basic: 18, premium: 10, enterprise: 5, revenue: 148000 }
    ];

    return { subscriptions, monthly };
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Charger les types de plans et les données de distribution en parallèle
        const [planTypesData, response] = await Promise.all([
          getSubscriptionPlanTypes(),
          getSubscriptionDistribution()
        ]);

        setPlanTypes(planTypesData);
        setSubscriptionData(response.distribution);
        setMonthlyData(response.monthly_trends);

      } catch (err: any) {
        console.error('Error fetching global subscription distribution:', err);
        // Fallback vers les données de démonstration en cas d'erreur
        const { subscriptions, monthly } = generateDemoData();
        setSubscriptionData(subscriptions);
        setMonthlyData(monthly);
        setError(err.message || t('reports.error_loading_data'));
      } finally {
        setLoading(false);
      }
    };

    fetchData(); // Données globales pour super-admin
  }, []);

  const totalSubscriptions = subscriptionData.reduce((sum, item) => sum + item.count, 0);
  const totalRevenue = subscriptionData.reduce((sum, item) => sum + item.revenue, 0);
  const mostPopular = subscriptionData.reduce((prev, current) => 
    (prev.count > current.count) ? prev : current, subscriptionData[0]
  );

  // Fonction pour générer les barres dynamiquement basées sur les types de plans
  const renderBars = () => {
    if (planTypes.length === 0) {
      // Fallback vers les barres par défaut si les types ne sont pas encore chargés
      return (
        <>
          <Bar dataKey="basic" fill="#3B82F6" name="Basic" />
          <Bar dataKey="premium" fill="#10B981" name="Standard" />
          <Bar dataKey="enterprise" fill="#F59E0B" name="Custom" />
        </>
      );
    }

    return planTypes.map((planType) => (
      <Bar
        key={planType.name}
        dataKey={planType.name.toLowerCase()}
        fill={planType.color}
        name={planType.displayName}
      />
    ));
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 dark:text-white">{data.plan_type}</p>
          <p className="text-sm text-blue-600">{t('analytics.schools')}: {data.count}</p>
          <p className="text-sm text-green-600">{t('analytics.revenue')}: {data.revenue?.toLocaleString()} FCFA</p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
            <Crown className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('analytics.global_subscription_overview')}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('analytics.plan_distribution_description')}
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <p className="text-sm text-yellow-700 dark:text-yellow-300">
            {error} - {t('analytics.showing_demo_data')}
          </p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">{t('reports.total_schools')}</p>
              <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{totalSubscriptions}</p>
            </div>
            <Users className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600 dark:text-green-400">{t('reports.total_revenue')}</p>
              <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                {totalRevenue.toLocaleString()} FCFA
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-600 dark:text-orange-400">{t('reports.popular_plan')}</p>
              <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                {mostPopular?.plan_type || 'N/A'}
              </p>
            </div>
            <Award className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Distribution Pie Chart */}
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            {t('analytics.plan_distribution')}
          </h4>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={subscriptionData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ plan_type, count }) => `${plan_type}: ${count}`}
                >
                  {subscriptionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Monthly Trends */}
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            {t('analytics.monthly_trends')}
          </h4>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                {renderBars()}
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default GlobalSubscriptionOverview;
