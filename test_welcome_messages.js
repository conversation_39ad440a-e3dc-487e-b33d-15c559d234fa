/**
 * Script de test pour le système de messages de bienvenue
 * Teste l'envoi d'emails et SMS selon les différents rôles
 */

const mongoose = require('mongoose');
const WelcomeMessageService = require('./src/services/WelcomeMessageService');
require('dotenv').config();

// Données de test pour différents rôles
const testUsers = [
  {
    user: {
      _id: new mongoose.Types.ObjectId(),
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+237123456789'
    },
    role: 'super',
    schoolName: 'Système Scholarify',
    resetToken: 'test-token-super-123',
    accessCode: null,
    isNewUser: true
  },
  {
    user: {
      _id: new mongoose.Types.ObjectId(),
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+237987654321'
    },
    role: 'admin',
    schoolName: 'École Primaire Les Palmiers',
    resetToken: 'test-token-admin-456',
    accessCode: null,
    isNewUser: true
  },
  {
    user: {
      _id: new mongoose.Types.ObjectId(),
      first_name: 'Pierre',
      last_name: 'Durand',
      email: '<EMAIL>',
      phone: '+237555666777'
    },
    role: 'teacher',
    schoolName: 'Collège Saint-Joseph',
    resetToken: 'test-token-teacher-789',
    accessCode: 'ABC12345',
    isNewUser: true
  },
  {
    user: {
      _id: new mongoose.Types.ObjectId(),
      first_name: 'Sophie',
      last_name: 'Bernard',
      email: '<EMAIL>',
      phone: null // Pas de téléphone pour tester le cas sans SMS
    },
    role: 'dean_of_studies',
    schoolName: 'Lycée Technique de Douala',
    resetToken: 'test-token-dean-101',
    accessCode: null,
    isNewUser: true
  }
];

async function testWelcomeMessages() {
  console.log('🧪 Test du système de messages de bienvenue\n');
  
  console.log('📋 Configuration:');
  console.log(`   EMAIL_USER: ${process.env.EMAIL_USER || 'NON CONFIGURÉ'}`);
  console.log(`   EMAIL_PASS: ${process.env.EMAIL_PASS ? 'CONFIGURÉ' : 'NON CONFIGURÉ'}`);
  console.log(`   FRONTEND_URL: ${process.env.FRONTEND_URL || 'NON CONFIGURÉ'}`);
  console.log(`   VONAGE_API_KEY: ${process.env.VONAGE_API_KEY ? 'CONFIGURÉ' : 'NON CONFIGURÉ'}`);
  console.log('');

  const results = [];

  for (const testData of testUsers) {
    console.log(`\n🔄 Test pour ${testData.user.first_name} ${testData.user.last_name} (${testData.role})`);
    console.log(`   École: ${testData.schoolName}`);
    console.log(`   Email: ${testData.user.email}`);
    console.log(`   Téléphone: ${testData.user.phone || 'Non fourni'}`);
    
    try {
      // Test des URLs de redirection
      const dashboardUrl = WelcomeMessageService.getDashboardUrl(testData.role);
      console.log(`   URL Dashboard: ${dashboardUrl}`);
      
      // Test du nom d'affichage du rôle
      const roleDisplay = WelcomeMessageService.getRoleDisplayName(testData.role);
      console.log(`   Rôle affiché: ${roleDisplay}`);
      
      // Test du message de bienvenue
      const welcomeMessage = WelcomeMessageService.getWelcomeMessage(
        testData.role, 
        testData.user.first_name, 
        testData.schoolName
      );
      console.log(`   Message: ${welcomeMessage.substring(0, 100)}...`);
      
      // Test du template d'email
      const emailTemplate = WelcomeMessageService.getEmailTemplate(testData.role);
      console.log(`   Template email: ${emailTemplate}`);
      
      // Test du sujet d'email
      const emailSubject = WelcomeMessageService.getEmailSubject(testData.role, testData.schoolName, true);
      console.log(`   Sujet email: ${emailSubject}`);
      
      // Test du message SMS
      if (testData.user.phone) {
        const smsMessage = WelcomeMessageService.getSMSMessage(
          testData.role, 
          testData.user.first_name, 
          testData.schoolName, 
          'https://short.url/reset'
        );
        console.log(`   Message SMS: ${smsMessage.substring(0, 80)}...`);
      }
      
      // Test d'envoi (mode simulation - ne pas envoyer réellement)
      console.log('   📧 Simulation envoi email...');
      
      // Vous pouvez décommenter cette ligne pour tester l'envoi réel
      // const result = await WelcomeMessageService.sendWelcomeMessages(testData);
      
      const simulatedResult = {
        success: true,
        results: {
          email: { success: true },
          sms: { success: testData.user.phone ? true : false }
        },
        summary: {
          email: true,
          sms: testData.user.phone ? true : false,
          user: {
            name: `${testData.user.first_name} ${testData.user.last_name}`,
            email: testData.user.email,
            phone: testData.user.phone,
            role: testData.role
          }
        }
      };
      
      results.push({
        user: testData.user,
        role: testData.role,
        result: simulatedResult,
        success: true
      });
      
      console.log('   ✅ Test réussi');
      
    } catch (error) {
      console.error(`   ❌ Erreur lors du test:`, error.message);
      results.push({
        user: testData.user,
        role: testData.role,
        result: null,
        success: false,
        error: error.message
      });
    }
  }
  
  // Résumé des résultats
  console.log('\n📊 RÉSUMÉ DES TESTS');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ Tests réussis: ${successful}/${results.length}`);
  console.log(`❌ Tests échoués: ${failed}/${results.length}`);
  
  if (failed > 0) {
    console.log('\n❌ ÉCHECS:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`   • ${r.user.first_name} ${r.user.last_name} (${r.role}): ${r.error}`);
    });
  }
  
  console.log('\n🎯 FONCTIONNALITÉS TESTÉES:');
  console.log('   ✅ Génération d\'URLs de redirection selon le rôle');
  console.log('   ✅ Noms d\'affichage des rôles');
  console.log('   ✅ Messages de bienvenue personnalisés');
  console.log('   ✅ Templates d\'email selon le rôle');
  console.log('   ✅ Sujets d\'email personnalisés');
  console.log('   ✅ Messages SMS adaptés');
  console.log('   ✅ Gestion des utilisateurs sans téléphone');
  
  console.log('\n💡 POUR TESTER L\'ENVOI RÉEL:');
  console.log('   1. Configurez vos variables d\'environnement EMAIL_* et VONAGE_*');
  console.log('   2. Décommentez la ligne "sendWelcomeMessages" dans ce script');
  console.log('   3. Modifiez les emails de test pour utiliser de vraies adresses');
  
  console.log('\n🏁 Test terminé');
}

// Exécuter le test
if (require.main === module) {
  testWelcomeMessages()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur générale:', error);
      process.exit(1);
    });
}

module.exports = { testWelcomeMessages };
