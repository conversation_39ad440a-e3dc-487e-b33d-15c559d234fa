"use client";

import { GraduationCap, Presentation } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense} from "react";
import useAuth from "@/app/hooks/useAuth";
import JustificationComponent from "@/components/Dashboard/ReusableComponents/JustificationComponent";
import { SchoolAdminDashboardSkeleton } from "@/components/skeletons";
import { useTranslation } from "@/hooks/useTranslation";

const BASE_URL = "/school-admin";

export default function Page() {
  const { logout } = useAuth();
  const { user } = useAuth();
  const { t, tDashboard } = useTranslation();

  const navigation = {
    icon: GraduationCap,
    baseHref: `${BASE_URL}/justification`,
    title: tDashboard('school-admin', 'justification', 'title')
  };
  return (
    <Suspense fallback={
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <SchoolAdminDashboardSkeleton />
      </SchoolLayout>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        {/* You might want to pass schoolId from somewhere here */}
        {user && <JustificationComponent user={user} />}
      </SchoolLayout>
    </Suspense>
  );
}
