/**
 * Service de gestion des erreurs pour l'authentification et autres opérations
 * Classifie les erreurs et fournit des messages utilisateur appropriés
 */

export interface ErrorDetails {
  type: 'credentials' | 'server' | 'network' | 'validation' | 'unknown';
  title: string;
  message: string;
  actionMessage?: string;
  canRetry: boolean;
  statusCode?: number;
}

export interface LoginErrorResponse {
  response?: {
    status: number;
    data?: {
      message?: string;
      error?: string;
    };
  };
  message?: string;
  code?: string;
}

/**
 * Service principal de gestion des erreurs
 */
export class ErrorHandlingService {
  
  /**
   * Analyse et classifie une erreur de connexion
   */
  static classifyLoginError(error: any): ErrorDetails {
    // Erreur réseau (pas de réponse du serveur)
    if (!error.response && (error.code === 'NETWORK_ERROR' || error.message?.includes('fetch'))) {
      return {
        type: 'network',
        title: 'Problème de connexion',
        message: 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.',
        actionMessage: 'Réessayez dans quelques instants ou contactez le support si le problème persiste.',
        canRetry: true
      };
    }

    // Erreur avec réponse du serveur
    if (error.response) {
      const status = error.response.status;
      const serverMessage = error.response.data?.message || error.response.data?.error;

      switch (status) {
        case 400:
          return {
            type: 'validation',
            title: 'Données invalides',
            message: 'Les informations saisies ne sont pas valides.',
            actionMessage: 'Vérifiez vos informations et réessayez.',
            canRetry: true,
            statusCode: status
          };

        case 401:
          // Erreur d'authentification Firebase ou mot de passe incorrect
          if (serverMessage?.includes('auth/wrong-password') || 
              serverMessage?.includes('auth/invalid-credential') ||
              serverMessage?.includes('Invalid password') ||
              serverMessage?.includes('wrong-password')) {
            return {
              type: 'credentials',
              title: 'Identifiants incorrects',
              message: 'L\'email ou le mot de passe que vous avez saisi ne correspond pas à nos enregistrements.',
              actionMessage: 'Vérifiez vos identifiants et réessayez, ou utilisez "Mot de passe oublié".',
              canRetry: true,
              statusCode: status
            };
          }
          
          return {
            type: 'credentials',
            title: 'Accès non autorisé',
            message: 'Vos identifiants sont incorrects.',
            actionMessage: 'Vérifiez votre email et mot de passe, puis réessayez.',
            canRetry: true,
            statusCode: status
          };

        case 403:
          return {
            type: 'credentials',
            title: 'Accès refusé',
            message: 'Votre compte n\'a pas les permissions nécessaires ou est suspendu.',
            actionMessage: 'Contactez l\'administrateur pour plus d\'informations.',
            canRetry: false,
            statusCode: status
          };

        case 404:
          // Utilisateur non trouvé
          if (serverMessage?.includes('User not found') || serverMessage?.includes('not found')) {
            return {
              type: 'credentials',
              title: 'Compte introuvable',
              message: 'Aucun compte n\'est associé à cette adresse email.',
              actionMessage: 'Vérifiez votre email ou créez un nouveau compte.',
              canRetry: true,
              statusCode: status
            };
          }
          
          return {
            type: 'server',
            title: 'Service non disponible',
            message: 'Le service de connexion est temporairement indisponible.',
            actionMessage: 'Réessayez dans quelques minutes ou contactez le support.',
            canRetry: true,
            statusCode: status
          };

        case 429:
          return {
            type: 'validation',
            title: 'Trop de tentatives',
            message: 'Trop de tentatives de connexion. Votre compte est temporairement bloqué.',
            actionMessage: 'Attendez quelques minutes avant de réessayer.',
            canRetry: true,
            statusCode: status
          };

        case 500:
        case 502:
        case 503:
        case 504:
          return {
            type: 'server',
            title: 'Erreur du serveur',
            message: 'Une erreur technique est survenue sur nos serveurs.',
            actionMessage: 'Réessayez dans quelques minutes ou contactez le support si le problème persiste.',
            canRetry: true,
            statusCode: status
          };

        default:
          return {
            type: 'unknown',
            title: 'Erreur inattendue',
            message: serverMessage || 'Une erreur inattendue s\'est produite.',
            actionMessage: 'Réessayez ou contactez le support si le problème persiste.',
            canRetry: true,
            statusCode: status
          };
      }
    }

    // Erreurs Firebase spécifiques
    if (error.code) {
      switch (error.code) {
        case 'auth/user-not-found':
          return {
            type: 'credentials',
            title: 'Compte introuvable',
            message: 'Aucun compte n\'est associé à cette adresse email.',
            actionMessage: 'Vérifiez votre email ou créez un nouveau compte.',
            canRetry: true
          };

        case 'auth/wrong-password':
        case 'auth/invalid-credential':
          return {
            type: 'credentials',
            title: 'Mot de passe incorrect',
            message: 'Le mot de passe que vous avez saisi est incorrect.',
            actionMessage: 'Vérifiez votre mot de passe ou utilisez "Mot de passe oublié".',
            canRetry: true
          };

        case 'auth/user-disabled':
          return {
            type: 'credentials',
            title: 'Compte désactivé',
            message: 'Votre compte a été désactivé.',
            actionMessage: 'Contactez l\'administrateur pour réactiver votre compte.',
            canRetry: false
          };

        case 'auth/too-many-requests':
          return {
            type: 'validation',
            title: 'Trop de tentatives',
            message: 'Trop de tentatives de connexion échouées.',
            actionMessage: 'Attendez quelques minutes avant de réessayer.',
            canRetry: true
          };

        case 'auth/network-request-failed':
          return {
            type: 'network',
            title: 'Problème de réseau',
            message: 'Impossible de se connecter. Vérifiez votre connexion internet.',
            actionMessage: 'Vérifiez votre connexion et réessayez.',
            canRetry: true
          };

        default:
          return {
            type: 'unknown',
            title: 'Erreur d\'authentification',
            message: error.message || 'Une erreur d\'authentification s\'est produite.',
            actionMessage: 'Réessayez ou contactez le support.',
            canRetry: true
          };
      }
    }

    // Erreur générique
    return {
      type: 'unknown',
      title: 'Erreur de connexion',
      message: error.message || 'Une erreur inattendue s\'est produite lors de la connexion.',
      actionMessage: 'Réessayez ou contactez le support si le problème persiste.',
      canRetry: true
    };
  }

  /**
   * Formate un message d'erreur pour l'affichage utilisateur
   */
  static formatErrorMessage(errorDetails: ErrorDetails): string {
    let message = errorDetails.message;
    
    if (errorDetails.actionMessage) {
      message += ` ${errorDetails.actionMessage}`;
    }
    
    return message;
  }

  /**
   * Détermine si l'utilisateur peut réessayer l'opération
   */
  static canRetry(errorDetails: ErrorDetails): boolean {
    return errorDetails.canRetry;
  }

  /**
   * Obtient la couleur appropriée pour le type d'erreur
   */
  static getErrorColor(errorDetails: ErrorDetails): string {
    switch (errorDetails.type) {
      case 'credentials':
        return 'text-orange-600';
      case 'server':
        return 'text-red-600';
      case 'network':
        return 'text-blue-600';
      case 'validation':
        return 'text-yellow-600';
      default:
        return 'text-red-600';
    }
  }

  /**
   * Obtient l'icône appropriée pour le type d'erreur
   */
  static getErrorIcon(errorDetails: ErrorDetails): string {
    switch (errorDetails.type) {
      case 'credentials':
        return '🔐';
      case 'server':
        return '⚠️';
      case 'network':
        return '🌐';
      case 'validation':
        return '⚡';
      default:
        return '❌';
    }
  }
}

/**
 * Hook pour utiliser le service de gestion d'erreurs
 */
export const useErrorHandling = () => {
  const classifyError = (error: any): ErrorDetails => {
    return ErrorHandlingService.classifyLoginError(error);
  };

  const formatMessage = (errorDetails: ErrorDetails): string => {
    return ErrorHandlingService.formatErrorMessage(errorDetails);
  };

  return {
    classifyError,
    formatMessage,
    canRetry: ErrorHandlingService.canRetry,
    getErrorColor: ErrorHandlingService.getErrorColor,
    getErrorIcon: ErrorHandlingService.getErrorIcon
  };
};
