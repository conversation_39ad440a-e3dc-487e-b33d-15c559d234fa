'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X } from 'lucide-react';
import { useChatbotContext } from '@/hooks/useChatbotContext';
import { chatbotService, ChatbotResponse } from '@/services/ChatbotService';
import ChatHeader from './ChatHeader';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';
import ThinkingIndicator from './ThinkingIndicator';
import ActionButtons from './ActionButtons';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  actions?: any[];
  suggestions?: string[];
}

export default function ChatbotWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isThinking, setIsThinking] = useState(false);
  const [mode, setMode] = useState<'technical' | 'user'>('user');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const context = useChatbotContext();

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Défilement lors de l'ajout de messages ou changement d'état thinking
  useEffect(() => {
    scrollToBottom();
  }, [messages, isThinking]);

  // Défilement lors de l'ouverture du chatbot
  useEffect(() => {
    if (isOpen) {
      // Petit délai pour laisser l'animation d'ouverture se terminer
      const timer = setTimeout(scrollToBottom, 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Welcome message initialization
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage = getWelcomeMessage(context.dashboard.type, context.user.role);
      setMessages([{
        id: 'welcome',
        content: welcomeMessage.content,
        role: 'assistant',
        timestamp: new Date(),
        suggestions: welcomeMessage.suggestions
      }]);
    }
  }, [isOpen, context.dashboard.type, context.user.role]);

  const getWelcomeMessage = (dashboardType: string, role: string) => {
    const messages: Record<string, { content: string; suggestions: string[] }> = {
      'super-admin': {
        content: `Hello! I'm your Scholarify assistant for the Super Admin dashboard. I can help you with global school management, user administration, subscriptions, and much more.`,
        suggestions: chatbotService.getDashboardSuggestions('super-admin')
      },
      'school-admin': {
        content: `Hello! I'm your assistant for school administration. I can help you with classes, students, teachers, and reports for your school.`,
        suggestions: chatbotService.getDashboardSuggestions('school-admin')
      },
      'teacher': {
        content: `Hello! I'm your teaching assistant. I can help you with grades, attendance, lesson planning, and parent communication.`,
        suggestions: chatbotService.getDashboardSuggestions('teacher')
      },
      'counselor': {
        content: `Hello! I'm your counseling assistant. I can help you with student enrollment, record management, and guidance services.`,
        suggestions: chatbotService.getDashboardSuggestions('counselor')
      },
      'parent': {
        content: `Hello! I'm your parent assistant. I can help you track your child's progress, communicate with teachers, and manage school-related activities.`,
        suggestions: chatbotService.getDashboardSuggestions('parent')
      }
    };

    return messages[dashboardType] || messages['super-admin'];
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setIsThinking(true);

    try {
      const response: ChatbotResponse = await chatbotService.sendMessage(content, context);

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.response,
        role: 'assistant',
        timestamp: new Date(),
        actions: response.actions,
        suggestions: response.suggestions
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Sorry, I encountered a technical issue. Please try again in a moment.',
        role: 'assistant',
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsThinking(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const handleActionClick = (action: any) => {
    // Handle different action types
    switch (action.type) {
      case 'navigate':
        if (typeof window !== 'undefined') {
          window.location.href = action.target;
        }
        break;
      case 'open_modal':
        // Trigger modal opening logic
        console.log('Opening modal:', action.target);
        // You can emit events or call specific functions here
        break;
      case 'execute_function':
        // Execute specific function
        console.log('Executing function:', action.target);
        break;
      default:
        console.log('Unknown action type:', action.type);
    }
  };

  return (
    <div className="fixed bottom-0 right-0 z-50 md:bottom-4 md:right-4">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="bg-white dark:bg-gray-800 shadow-xl flex flex-col
              w-full h-[100vh] md:w-96 md:h-[600px] md:rounded-lg md:mb-4
              fixed bottom-0 right-0 md:relative"
          >
            <ChatHeader
              mode={mode}
              onModeChange={setMode}
              onClose={() => setIsOpen(false)}
              dashboardType={context.dashboard.type}
              userName={context.user.name}
            />

            <div className="flex-1 overflow-y-auto p-4 space-y-4
              scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600
              scrollbar-track-transparent hover:scrollbar-thumb-gray-400
              dark:hover:scrollbar-thumb-gray-500"
            >
              {messages.map((message) => (
                <div key={message.id}>
                  <ChatMessage message={message} />
                  {message.actions && (
                    <ActionButtons
                      actions={message.actions}
                      onActionClick={handleActionClick}
                    />
                  )}
                  {message.suggestions && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {message.suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion)}
                          className="text-xs bg-teal-100 dark:bg-teal-900 text-teal-700 dark:text-teal-300 px-2 py-1 rounded-full hover:bg-teal-200 dark:hover:bg-teal-800 transition-colors"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              {isThinking && <ThinkingIndicator />}
              <div ref={messagesEndRef} />
            </div>

            <ChatInput onSend={handleSendMessage} disabled={isThinking} />
          </motion.div>
        )}
      </AnimatePresence>

      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`${
          isOpen
            ? 'fixed bottom-4 right-4 md:relative md:bottom-0 md:right-0'
            : 'relative'
        } bg-teal hover:bg-tealdarker text-white p-4 rounded-full shadow-lg transition-transform hover:scale-105`}
      >
        {isOpen ? (
          <X className="w-6 h-6" />
        ) : (
          <div className="relative">
            <MessageCircle className="w-6 h-6" />
            {/* Dashboard indicator */}
            <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full px-1.5 py-0.5 font-bold">
              {context.dashboard.type.split('-')[0].toUpperCase()}
            </div>
          </div>
        )}
      </button>
    </div>
  );
} 