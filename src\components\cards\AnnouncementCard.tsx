"use client";

import React from "react";
import { Calendar, Eye, Edit, Trash2, Alert<PERSON>riangle, Info, Zap, Users } from "lucide-react";
import { motion } from "framer-motion";
import { AnnouncementSchema } from "@/app/services/AnnouncementServices";
import { useTranslation } from "@/hooks/useTranslation";

interface AnnouncementCardProps {
  announcement: AnnouncementSchema;
  onView: (announcement: AnnouncementSchema) => void;
  onEdit?: (announcement: AnnouncementSchema) => void;
  onDelete?: (announcement: AnnouncementSchema) => void;
  showActions?: {
    view?: boolean;
    edit?: boolean;
    delete?: boolean;
  };
}

export default function AnnouncementCard({
  announcement,
  onView,
  onEdit,
  onDelete,
  showActions = { view: true, edit: true, delete: true }
}: AnnouncementCardProps) {
  const { t, tDashboard } = useTranslation();
  
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Zap className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <Info className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
      case 'high':
        return 'border-l-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low':
        return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
      default:
        return 'border-l-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const truncateContent = (content: string, maxLength: number = 120) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const getTargetAudienceIcon = (audience: string) => {
    return <Users className="h-3 w-3" />;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`border-l-4 rounded-r-lg p-4 transition-all duration-200 hover:shadow-md ${getPriorityColor(announcement.priority)}`}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2 flex-1">
          {getPriorityIcon(announcement.priority)}
          <h3 className="font-semibold text-foreground text-sm line-clamp-1 flex-1">
            {announcement.title}
          </h3>
        </div>
        <div className="flex items-center gap-1 ml-2">
          {showActions.view && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => onView(announcement)}
              className="p-1.5 text-foreground/60 hover:text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded transition-colors"
              title="View"
            >
              <Eye className="h-4 w-4" />
            </motion.button>
          )}
          {showActions.edit && onEdit && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => onEdit(announcement)}
              className="p-1.5 text-foreground/60 hover:text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30 rounded transition-colors"
              title="Edit"
            >
              <Edit className="h-4 w-4" />
            </motion.button>
          )}
          {showActions.delete && onDelete && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => onDelete(announcement)}
              className="p-1.5 text-foreground/60 hover:text-red-600 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-colors"
              title="Delete"
            >
              <Trash2 className="h-4 w-4" />
            </motion.button>
          )}
        </div>
      </div>

      {/* Content */}
      <p className="text-sm text-foreground/70 mb-3 line-clamp-3">
        {truncateContent(announcement.content)}
      </p>

      {/* Footer */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className={`text-xs px-2 py-1 rounded-full font-medium ${getPriorityBadgeColor(announcement.priority)}`}>
            {tDashboard('school-admin', 'announcements', announcement.priority as 'urgent' | 'high' | 'medium' | 'low')}
          </span>
          <div className="flex items-center gap-1 text-xs text-foreground/50">
            {getTargetAudienceIcon(announcement.target_audience)}
            <span>{announcement.target_audience === 'all' ? tDashboard('school-admin', 'announcements', 'everyone') : tDashboard('school-admin', 'announcements', announcement.target_audience as 'teachers' | 'parents' | 'students')}</span>
          </div>
        </div>
        
        <div className="flex items-center gap-1 text-foreground/50">
          <Calendar className="h-3 w-3" />
          <span className="text-xs">
            {formatDate(announcement.published_at || announcement.created_at)}
          </span>
        </div>
      </div>

      {/* Publication Status */}
      {!announcement.is_published && (
        <div className="mt-2 text-xs text-orange-600 dark:text-orange-400 font-medium">
          {tDashboard('school-admin', 'announcements', 'draft')}
        </div>
      )}
    </motion.div>
  );
}
