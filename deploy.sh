#!/bin/bash

# Script de déploiement pour Scholarify Dashboard
# Usage: ./deploy.sh [production|staging]

set -e  # Arrêter le script en cas d'erreur

# Configuration
PROJECT_DIR="/path/to/your/project/dashboard"
NGINX_CONFIG="/etc/nginx/sites-available/scholarify"
NGINX_ENABLED="/etc/nginx/sites-enabled/scholarify"
PM2_APP_NAME="scholarify-dashboard"
NODE_ENV=${1:-production}

echo "🚀 Déploiement de Scholarify Dashboard en mode $NODE_ENV"

# 1. Aller dans le répertoire du projet
cd $PROJECT_DIR

# 2. Mettre à jour le code (si utilisation de Git)
echo "📥 Mise à jour du code..."
git pull origin main

# 3. Installer les dépendances
echo "📦 Installation des dépendances..."
npm ci --only=production

# 4. Build de l'application
echo "🔨 Build de l'application..."
NODE_ENV=$NODE_ENV npm run build

# 5. Copier la configuration Nginx
echo "⚙️ Configuration Nginx..."
sudo cp nginx.conf $NGINX_CONFIG

# Remplacer les placeholders dans la config Nginx
sudo sed -i "s|/path/to/your/project/dashboard|$PROJECT_DIR|g" $NGINX_CONFIG
sudo sed -i "s|your-domain.com|$(hostname -f)|g" $NGINX_CONFIG

# Activer le site
sudo ln -sf $NGINX_CONFIG $NGINX_ENABLED

# Tester la configuration Nginx
sudo nginx -t

# 6. Démarrer/Redémarrer l'application avec PM2
echo "🔄 Démarrage de l'application..."

# Vérifier si PM2 est installé
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installation de PM2..."
    sudo npm install -g pm2
fi

# Créer le fichier ecosystem.config.js si il n'existe pas
if [ ! -f ecosystem.config.js ]; then
    echo "📝 Création du fichier ecosystem.config.js..."
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '$PM2_APP_NAME',
    script: 'npm',
    args: 'start',
    cwd: '$PROJECT_DIR',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_staging: {
      NODE_ENV: 'staging',
      PORT: 3001
    },
    log_file: '/var/log/pm2/$PM2_APP_NAME.log',
    out_file: '/var/log/pm2/$PM2_APP_NAME-out.log',
    error_file: '/var/log/pm2/$PM2_APP_NAME-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF
fi

# Démarrer ou redémarrer l'application
if pm2 list | grep -q $PM2_APP_NAME; then
    echo "🔄 Redémarrage de l'application..."
    pm2 restart $PM2_APP_NAME --env $NODE_ENV
else
    echo "▶️ Démarrage de l'application..."
    pm2 start ecosystem.config.js --env $NODE_ENV
fi

# Sauvegarder la configuration PM2
pm2 save

# 7. Redémarrer Nginx
echo "🔄 Redémarrage de Nginx..."
sudo systemctl reload nginx

# 8. Vérifications finales
echo "✅ Vérifications finales..."

# Vérifier que l'application répond
sleep 5
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Application accessible sur le port 3000"
else
    echo "❌ Erreur: L'application ne répond pas sur le port 3000"
    pm2 logs $PM2_APP_NAME --lines 20
    exit 1
fi

# Vérifier Nginx
if sudo nginx -t > /dev/null 2>&1; then
    echo "✅ Configuration Nginx valide"
else
    echo "❌ Erreur: Configuration Nginx invalide"
    exit 1
fi

echo "🎉 Déploiement terminé avec succès!"
echo "📊 Status de l'application:"
pm2 status $PM2_APP_NAME

echo "📝 Logs disponibles:"
echo "  - Application: pm2 logs $PM2_APP_NAME"
echo "  - Nginx Access: tail -f /var/log/nginx/scholarify_access.log"
echo "  - Nginx Error: tail -f /var/log/nginx/scholarify_error.log"
