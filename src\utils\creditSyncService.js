const School = require('../models/School');
const SchoolSubscription = require('../models/SchoolSubscription');
const CreditPurchase = require('../models/CreditPurchase');

/**
 * Service pour synchroniser les crédits entre les différents modèles
 */
class CreditSyncService {
  
  /**
   * Synchroniser les crédits d'une école après un paiement réussi
   * @param {string} schoolId - ID de l'école
   * @param {string} purchaseId - ID de l'achat de crédit (optionnel)
   */
  static async syncSchoolCredits(schoolId, purchaseId = null) {
    try {
      console.log(`🔄 Synchronisation des crédits pour l'école: ${schoolId}`);

      // 1. Calculer le total des crédits achetés avec succès
      const completedPurchases = await CreditPurchase.find({
        school_id: schoolId,
        payment_status: 'completed'
      });

      const totalCreditsPurchased = completedPurchases.reduce((sum, purchase) => {
        return sum + purchase.credits_purchased;
      }, 0);

      const totalAmountPaid = completedPurchases.reduce((sum, purchase) => {
        return sum + purchase.total_amount;
      }, 0);

      console.log(`💰 Total crédits achetés: ${totalCreditsPurchased}`);
      console.log(`💵 Montant total payé: ${totalAmountPaid} FCFA`);

      // 2. Mettre à jour l'école
      const school = await School.findById(schoolId);
      if (school) {
        school.credit = totalCreditsPurchased;
        await school.save();
        console.log(`🏫 École mise à jour - Crédits: ${school.credit}`);
      }

      // 3. Mettre à jour ou créer la souscription
      let subscription = await SchoolSubscription.findOne({ school_id: schoolId });
      
      if (!subscription) {
        // Créer une nouvelle souscription
        subscription = new SchoolSubscription({
          school_id: schoolId,
          plan_type: 'basic',
          status: 'active',
          credits_purchased: totalCreditsPurchased,
          credits_used: 0,
          credits_balance: totalCreditsPurchased,
          subscription_start: new Date(),
          features: ['student_management', 'class_management', 'attendance_tracking', 'grade_management', 'timetable_management']
        });
        console.log(`📝 Nouvelle souscription créée`);
      } else {
        // Mettre à jour la souscription existante
        subscription.credits_purchased = totalCreditsPurchased;
        subscription.credits_balance = totalCreditsPurchased - subscription.credits_used;
        
        // Activer la souscription si elle était expirée et qu'il y a des crédits
        if (subscription.status === 'expired' && totalCreditsPurchased > 0) {
          subscription.status = 'active';
        }
        console.log(`📝 Souscription mise à jour - Balance: ${subscription.credits_balance}`);
      }

      // Ajouter l'achat à l'historique si spécifié
      if (purchaseId && !subscription.credit_purchases.includes(purchaseId)) {
        subscription.credit_purchases.push(purchaseId);
      }

      subscription.last_credit_purchase = new Date();
      await subscription.save();

      console.log(`✅ Synchronisation terminée pour l'école: ${schoolId}`);

      return {
        success: true,
        schoolCredits: totalCreditsPurchased,
        subscriptionBalance: subscription.credits_balance,
        totalPurchases: completedPurchases.length
      };

    } catch (error) {
      console.error(`❌ Erreur lors de la synchronisation des crédits:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Déduire des crédits lors de l'utilisation
   * @param {string} schoolId - ID de l'école
   * @param {number} creditsToDeduct - Nombre de crédits à déduire
   * @param {string} reason - Raison de la déduction
   * @param {string} userId - ID de l'utilisateur qui effectue l'action
   */
  static async deductCredits(schoolId, creditsToDeduct, reason, userId) {
    try {
      console.log(`🔻 Déduction de ${creditsToDeduct} crédits pour l'école: ${schoolId}`);

      // 1. Vérifier la souscription
      const subscription = await SchoolSubscription.findOne({ school_id: schoolId });
      if (!subscription) {
        throw new Error('Souscription non trouvée');
      }

      // 2. Vérifier la disponibilité des crédits
      if (subscription.credits_balance < creditsToDeduct) {
        throw new Error(`Crédits insuffisants. Disponible: ${subscription.credits_balance}, Requis: ${creditsToDeduct}`);
      }

      // 3. Déduire les crédits
      const balanceBefore = subscription.credits_balance;
      subscription.credits_used += creditsToDeduct;
      subscription.credits_balance -= creditsToDeduct;
      subscription.last_credit_usage = new Date();

      await subscription.save();

      // 4. Mettre à jour l'école
      const school = await School.findById(schoolId);
      if (school) {
        school.credit = subscription.credits_balance;
        await school.save();
      }

      console.log(`✅ Déduction terminée - Balance: ${subscription.credits_balance}`);

      return {
        success: true,
        balanceBefore,
        balanceAfter: subscription.credits_balance,
        creditsDeducted: creditsToDeduct
      };

    } catch (error) {
      console.error(`❌ Erreur lors de la déduction des crédits:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Obtenir le statut des crédits d'une école
   * @param {string} schoolId - ID de l'école
   */
  static async getCreditStatus(schoolId) {
    try {
      const school = await School.findById(schoolId);
      const subscription = await SchoolSubscription.findOne({ school_id: schoolId });
      const completedPurchases = await CreditPurchase.find({
        school_id: schoolId,
        payment_status: 'completed'
      });

      return {
        schoolCredits: school?.credit || 0,
        subscriptionBalance: subscription?.credits_balance || 0,
        totalPurchased: subscription?.credits_purchased || 0,
        totalUsed: subscription?.credits_used || 0,
        totalPurchases: completedPurchases.length,
        lastPurchase: subscription?.last_credit_purchase,
        lastUsage: subscription?.last_credit_usage,
        status: subscription?.status || 'inactive'
      };
    } catch (error) {
      console.error(`❌ Erreur lors de la récupération du statut:`, error);
      return null;
    }
  }
}

module.exports = CreditSyncService;
