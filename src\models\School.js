const mongoose = require('mongoose');

// Define the schema for the School model
const schoolSchema = new mongoose.Schema({
  school_id: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,  // Ensures that the name field is required
  },
  credit: {
    type: Number,
    default: 0,  // Default value for credit
  },
  email: {
    type: String,
  },
  description: {
    type: String,
  },
  address: {
    type: String,
    required: false,
  },
  website: {
    type: String,
    required: false,
  },
  phone_number: {
    type: String,
    required: false,
  },
  established_year: {
    type: Date,
  },
  principal_name: {
    type: String,
  },
  logo: {
    type: String,
  }
}, {
  timestamps: true  // Automatically adds createdAt and updatedAt fields
});

// Hook post-save pour créer automatiquement une souscription
schoolSchema.post('save', async function(doc) {
  // Vérifier si c'est une nouvelle école (pas une mise à jour)
  if (this.isNew) {
    try {
      const SchoolSubscription = require('./SchoolSubscription');

      // Vérifier si une souscription existe déjà
      const existingSubscription = await SchoolSubscription.findOne({ school_id: doc._id });

      if (!existingSubscription) {
        // Créer une souscription par défaut avec 50 crédits
        const subscription = await SchoolSubscription.createDefaultSubscription(doc._id);
        console.log(`✅ Souscription automatique créée pour l'école: ${doc.name} (ID: ${doc._id})`);
        console.log(`   Plan: ${subscription.plan_type}, Crédits: ${subscription.credits_balance}`);
      }
    } catch (error) {
      console.error(`❌ Erreur lors de la création automatique de souscription pour l'école ${doc.name}:`, error);
    }
  }
});

// Use the model if it's already defined, or create a new one
const School = mongoose.models.School || mongoose.model('School', schoolSchema);

module.exports = School;
