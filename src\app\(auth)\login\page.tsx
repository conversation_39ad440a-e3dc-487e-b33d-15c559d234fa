"use client";
import { FormEvent, useEffect, useState } from "react";
import AppLogo from "@/components/AppLogo";
import Input from "@/components/input";
import FormHeading from "@/components/FormHeading";
import { Loader2, ArrowLeft, Home } from "lucide-react";
import NotificationCard from "@/components/NotificationCard";
import '@/styles/formStyle.css';
import useAuth from "@/app/hooks/useAuth";
import { redirect, useRouter } from "next/navigation";
import CircularLoader from "@/components/widgets/CircularLoader";
import { getCurrentUser } from "@/app/services/UserServices";
import { UserSchema } from "@/app/models/UserModel";

export default function Login() {
    const [isLoading, setIsLoading] = useState(false);
    const [isForgotPasswordLoading, setIsForgotPasswordLoading] = useState(false);
    const [user, setUser] = useState<UserSchema | null>(null);
    const { login, isAuthenticated, loading, redirectAfterLogin } = useAuth();
    const router = useRouter();
    // State pour les champs de formulaire
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [rememberMe, setRememberMe] = useState(false);
    const [hasRedirected, setHasRedirected] = useState(false); // Nouveau state pour éviter double redirection

    const [errorMessage, setErrorMessage] = useState('');
    const [hasError, setHasError] = useState(false);

    // Fonction pour obtenir la redirection par défaut selon le rôle
    const getDefaultRedirectByRole = async () => {
        const user = await getCurrentUser();
        if (!user) return '/login';

        console.log("User role for redirect:", user.role);

        switch (user.role) {
            case 'super':
                return '/super-admin/dashboard';
            case 'admin':
                return '/school-admin/dashboard';
            case 'teacher':
                return '/teacher-dashboard';
            case 'parent':
                return '/parent-dashboard';
            case 'dean_of_studies':
                return '/school-admin/dashboard';
            case 'bursar':
                return '/school-admin/dashboard';
            case 'school_admin':
                return '/school-admin/dashboard';
            default:
                console.warn("Unknown role:", user.role, "redirecting to generic dashboard");
                return '/'; // Fallback générique
        }
    };

    useEffect(() => {
        // SEULEMENT rediriger si l'utilisateur est déjà authentifié ET qu'on n'a pas encore redirigé
        if (!loading && isAuthenticated && !hasRedirected) {
            const handleRedirect = async () => {
                try {
                    setHasRedirected(true); // Marquer comme redirigé pour éviter les doublons

                    // Utiliser redirectAfterLogin si disponible, sinon redirection par rôle
                    const redirectTo = redirectAfterLogin || await getDefaultRedirectByRole();
                    console.log("Auto-redirecting to:", redirectTo);
                    router.push(redirectTo);
                } catch (error) {
                    console.error("Error during auto-redirect:", error);
                    // Fallback en cas d'erreur
                    router.push('/dashboard');
                }
            };

            handleRedirect();
        }
    }, [isAuthenticated, loading, redirectAfterLogin, router, hasRedirected]);

    if (loading) {
        return (
            <div className="flex justify-center items-center h-screen w-full absolute top-0 left-0 z-50">
                <CircularLoader size={40} color="teal-500" />
            </div>
        );
    }
    if (isAuthenticated) {
        if (user && !hasRedirected) {
            setHasRedirected(true);
            if (user?.role === 'super') {
                router.push('/super-admin/dashboard');
            } else if (user?.role === 'admin') {
                router.push('/school-admin/dashboard');
            } else if (user?.role === 'teacher') {
                router.push('/teacher-dashboard');
            } else if (user?.role === "dean_of_studies") {
                router.push('/school-admin/dashboard');
            } else if (user?.role === "bursar") {
                router.push('/school-admin/dashboard');
            } else if (user?.role === "school_admin") {
                router.push('/school-admin/dashboard');
            } else {
                router.push('/');
            }
        } else {

        }
    }

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        try {
            await login(email, password, rememberMe);
            setErrorMessage('');
            setHasError(false);
            // setEmail('');
            // setPassword('');
            // setRememberMe(false);
            // Get role from your global state or from login() result ideally
            const user = await getCurrentUser();
            if (!user) return;

            const roleRedirectMap: Record<string, string> = {
                super: '/super-admin/dashboard',
                admin: '/school-admin/dashboard',
                teacher: '/teacher-dashboard',
                dean_of_studies: '/school-admin/dashboard',
                bursar: '/school-admin/dashboard',
                school_admin: '/school-admin/dashboard',
            };

            const redirectPath = roleRedirectMap[user.role] || '/';
            router.push(redirectPath);
        } catch (error) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "The email or password you entered doesn't match our records. Please double-check and try again.";

            setErrorMessage(errorMessage);
            setHasError(true);
        } finally {
            setIsLoading(false);
            // setPassword('');
        }
    };

    // Fonction pour gérer le clic sur le lien "Forgot Password?"
    const handleForgotPasswordClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
        e.preventDefault();
        setIsForgotPasswordLoading(true);

        // Rediriger vers la page de récupération de mot de passe après un court délai
        setTimeout(() => {
            router.push('/forgot-password');
        }, 800); // Délai de 800ms pour montrer l'animation
    };

    return <>
        <div className="flex bg-white dark:bg-gray-900 dark:text-white  rounded-lg shadow-lg h-screen px-4">
            {/* Back to Home Button */}
            <button
                onClick={() => router.push('/')}
                className="fixed top-4 left-4 z-50 flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
                <ArrowLeft className="w-4 h-4" />
                <span className="text-sm font-medium">Back to Home</span>
            </button>

            {/* Section de l'image latérale */}
            <div className="asideLogo w-[50%] h-screen py-2">
                <div className="asideImage w-full h-full">
                    <img src="/assets/images/asideImage2.png" className="h-full w-full rounded-[25px]" alt="Aside Image" />
                </div>
            </div>
            <div className="asideForm  bg-white dark:bg-gray-900 flex flex-col justify-evenly items-center m-auto  ">
                <div className=" flex flex-col justify-evenly items-center m-auto w-full max-w-[500px]  dark:text-white py-6">
                    <AppLogo logoSrc="/assets/logo.png" logoAlt="Logo" />
                    <div className="w-full mx-auto p-6" >
                        <FormHeading
                            title="Nice to see you!"
                            subtitle="Sign in to access your Dashboard"
                            formIcon={
                                <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="1.25" y="1.25" width="47.5" height="47.5" rx="13.75" stroke="#17B890" strokeWidth="2.5" />
                                    <path d="M27.167 17.832H30.5003C30.9424 17.832 31.3663 18.0076 31.6788 18.3202C31.9914 18.6327 32.167 19.0567 32.167 19.4987V31.1654C32.167 31.6074 31.9914 32.0313 31.6788 32.3439C31.3663 32.6564 30.9424 32.832 30.5003 32.832H27.167" stroke="#17B890" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M23 29.4974L27.1667 25.3307L23 21.1641" stroke="#17B890" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M27.167 25.332H17.167" stroke="#17B890" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                            }
                        />
                        <NotificationCard
                            title="Error"
                            icon={
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10 1.25C5.16751 1.25 1.25 5.16751 1.25 10C1.25 14.8325 5.16751 18.75 10 18.75C14.8325 18.75 18.75 14.8325 18.75 10C18.75 5.16751 14.8325 1.25 10 1.25ZM10 17.5C5.85786 17.5 2.5 14.1421 2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10C17.5 14.1421 14.1421 17.5 10 17.5ZM10 5.625C9.65482 5.625 9.375 5.90482 9.375 6.25V10.625C9.375 10.9702 9.65482 11.25 10 11.25C10.3452 11.25 10.625 10.9702 10.625 10.625V6.25C10.625 5.90482 10.3452 5.625 10 5.625ZM10 12.5C9.65482 12.5 9.375 12.7798 9.375 13.125V13.75C9.375 14.0952 9.65482 14.375 10 14.375C10.3452 14.375 10.625 14.0952 10.625 13.75V13.125C10.625 12.7798 10.3452 12.5 10 12.5Z" fill="#EF4444" />
                                </svg>
                            }
                            message={errorMessage}
                            type="error"
                            isVisible={hasError}
                            onClose={() => {
                                setErrorMessage('');
                                setHasError(false);
                            }}

                        />
                        <form onSubmit={handleSubmit} className="flex flex-col">
                            <div className="mb-4">
                                <Input
                                    label="Email"
                                    type="email"
                                    id="email"
                                    prefixIcon={
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M17.5 3.4375H2.5C2.25136 3.4375 2.0129 3.53627 1.83709 3.71209C1.66127 3.8879 1.5625 4.12636 1.5625 4.375V15C1.5625 15.4144 1.72712 15.8118 2.02015 16.1049C2.31317 16.3979 2.7106 16.5625 3.125 16.5625H16.875C17.2894 16.5625 17.6868 16.3979 17.9799 16.1049C18.2729 15.8118 18.4375 15.4144 18.4375 15V4.375C18.4375 4.12636 18.3387 3.8879 18.1629 3.71209C17.9871 3.53627 17.7486 3.4375 17.5 3.4375ZM15.0898 5.3125L10 9.97813L4.91016 5.3125H15.0898ZM3.4375 14.6875V6.50625L9.36641 11.9414C9.53932 12.1 9.7654 12.1879 10 12.1879C10.2346 12.1879 10.4607 12.1 10.6336 11.9414L16.5625 6.50625V14.6875H3.4375Z" fill="#575D5E" />
                                        </svg>
                                    }
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    className="w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>

                            <div className="mb-4">
                                <Input
                                    label="Password"
                                    type="password"
                                    id="password"
                                    prefixIcon={
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M15.625 8.75H15V6.25C15 3.48858 12.7614 1.25 10 1.25C7.23858 1.25 5 3.48858 5 6.25V8.75H4.375C3.68464 8.75 3.125 9.30964 3.125 10V16.25C3.125 16.9404 3.68464 17.5 4.375 17.5H15.625C16.3154 17.5 16.875 16.9404 16.875 16.25V10C16.875 9.30964 16.3154 8.75 15.625 8.75ZM6.25 6.25C6.25 4.17893 7.92893 2.5 10 2.5C12.0711 2.5 13.75 4.17893 13.75 6.25V8.75H6.25V6.25ZM15.625 16.25H4.375V10H15.625V16.25ZM10 11.875C9.65482 11.875 9.375 12.1548 9.375 12.5V14.375C9.375 14.7202 9.65482 15 10 15C10.3452 15 10.625 14.7202 10.625 14.375V12.5C10.625 12.1548 10.3452 11.875 10 11.875Z" fill="#575D5E" />
                                        </svg>
                                    }
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    className="w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>

                            <div className="flex justify-between items-center mb-6">
                                <label className="flex items-center">
                                    <input
                                        type="checkbox"
                                        checked={rememberMe}
                                        onChange={(e) => setRememberMe(e.target.checked)}
                                        className="mr-2"
                                    />
                                    <span className="text-sm text-gray-800 dark:text-gray-200">Remember for 30 days</span>
                                </label>
                                <a
                                    href="/forgot-password"
                                    onClick={handleForgotPasswordClick}
                                    className="text-sm text-blue-600 hover:underline flex items-center"
                                >
                                    {isForgotPasswordLoading ? (
                                        <>
                                            <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                            Redirecting...
                                        </>
                                    ) : (
                                        "Forgot Password?"
                                    )}
                                </a>
                            </div>

                            <button
                                type="submit"
                                className="w-full flex justify-center gap-2 items-center bg-[#17B890] text-white py-2 rounded-full hover:bg-[#17b890c4] transition duration-200"
                            >
                                {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                                Sign In
                                <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M4.66699 10H16.3337M16.3337 10L11.3337 5M16.3337 10L11.3337 15" stroke="white" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>

                            </button>
                        </form>
                    </div>
                    <div>
                        <ul className="flex justify-center items-center gap-5 py-4 text-gray-800 dark:text-gray-200 text-sm">
                            <li className="hover:text-[#17B890]"><a href="#">Licence</a></li>
                            <li className="hover:text-[#17B890]"><a href="#">Terms of Use</a></li>
                            <li className="hover:text-[#17B890]"><a href="#">Blog</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </>
}
