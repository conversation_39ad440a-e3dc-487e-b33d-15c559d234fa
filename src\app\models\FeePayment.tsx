// Related basic interfaces for populated fields
export interface StudentBasic {
  _id: string;
  first_name: string;
  last_name: string;
  student_id: string;
}

export interface ClassLevelBasic {
  _id: string;
  name: string;
}

export interface FeeBasic {
  _id: string;
  fee_type: string;
  amount: number;
}

export interface SchoolResourceBasic {
  _id: string;
  name: string;
  price: number;
}

// Installment sub-schema
export interface InstallmentSchema {
  _id:string;
  amount: number;
  dueDate: string; // ISO 8601 format
  paid: boolean;
  paidAt?: string;
  transactionRef?: string;
}

// Installment creation schema (without _id as it's generated by backend)
export interface InstallmentCreateSchema {
  amount: number;
  dueDate: string; // ISO 8601 format
  paid: boolean;
  paidAt?: string;
  transactionRef?: string;
}

// Main FeePayment schema from backend
export interface FeePaymentSchema extends Record<string, unknown> {
  _id: string;
  student_id: string | StudentBasic;
  school_id: string;
  class_level: string | ClassLevelBasic;
  academic_year: string;
  receipt_number: string;
  selectedFees: (string | FeeBasic)[];
  selectedResources: (string | SchoolResourceBasic)[];
  paymentMode: "full" | "installment";
  totalAmount: number;
  scholarshipPercentage?: number;
  installments?: InstallmentSchema[];
  status: "pending" | "partially_paid" | "paid" | "cancelled";
  createdAt?: string;
  updatedAt?: string;
}

// Payload for creating a new fee payment
export interface FeePaymentCreateSchema extends Record<string, unknown> {
  student_id: string;
  school_id: string;
  class_level: string;
  academic_year: string;
  selectedFees: string[];
  selectedResources: string[];
  paymentMode: "full" | "installment";
  totalAmount: number;
  scholarshipPercentage?: number;
  installments?: InstallmentCreateSchema[];
}

// Payload for updating an existing fee payment
export interface FeePaymentUpdateSchema extends Partial<FeePaymentCreateSchema> {
  _id: string;
}
