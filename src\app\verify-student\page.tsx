'use client';

import { useSearchParams } from 'next/navigation';
import { <PERSON><PERSON>heck, AlertCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { verifyStudentId } from '../services/StudentServices'; // update path
import { StudentSchema } from '../models/StudentModel';

export default function VerifyStudentStatic() {
  const searchParams = useSearchParams();
  // Using a default ID for demonstration, consider removing in production if always expecting a real ID
  const id = searchParams.get('id') || "6844b50058e70f9151569b06";

  const [student, setStudent] = useState<StudentSchema | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError('No student ID provided in the URL.');
      return;
    }

    setLoading(true);
    verifyStudentId(id)
      .then((data) => {
        setStudent(data);
        setError(null);
      })
      .catch(() => {
        setError('Failed to verify student ID. Please check the ID and try again.'); // More user-friendly error message
      })
      .finally(() => {
        setLoading(false);
      });
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background p-6"> {/* Use bg-background */}
        <div className="p-10 text-center text-foreground"> {/* Use text-foreground */}
          <p>Loading verification data...</p>
        </div>
      </div>
    );
  }

  if (error || !student) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background p-6"> {/* Use bg-background */}
        <div className="p-10 text-center text-red-600"> {/* Error text remains red for clarity */}
          <AlertCircle size={32} className="mx-auto mb-2 text-red-600" /> {/* Icon remains red */}
          <h1 className="text-2xl font-bold text-foreground">Verification Failed</h1> {/* Use text-foreground */}
          <p className="text-foreground">{error || 'This QR code does not contain valid student information.'}</p> {/* Use text-foreground */}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-6"> {/* Use bg-background */}
      <ShieldCheck size={48} className="text-teal mb-4" /> {/* Use text-teal for success icon */}
      <h1 className="text-2xl font-bold text-foreground mb-2">Student ID Verified</h1> {/* Use text-foreground */}
      <p className="text-foreground mb-6"> {/* Use text-foreground */}
        This student ID card is valid and has been digitally signed.
      </p>

      <div className="bg-background shadow-md rounded-lg p-6 max-w-sm w-full text-center border border-teal-200"> {/* Use bg-background, add subtle border */}
        <h2 className="text-xl font-semibold text-foreground">{student.name}</h2> {/* Use text-foreground */}
        <p className="text-sm text-foreground mb-2"> {/* Use text-foreground */}
          Student ID: <strong>{student.student_id}</strong>
        </p>
        <p className="text-sm text-foreground italic">Verified at time of issuance</p> {/* Use text-foreground */}
      </div>
    </div>
  );
}