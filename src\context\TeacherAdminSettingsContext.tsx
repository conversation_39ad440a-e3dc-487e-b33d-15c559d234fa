"use client";

import React, {
    createContext,
    useContext,
    useState,
    useEffect,
    ReactNode,
} from "react";
// Ensure these paths match your project's alias configuration
import { getUserById, updateUser, uploadUserAvatar } from "@/app/services/UserServices";
import useAuth from "@/app/hooks/useAuth";
import { UserSchema } from "@/app/models/UserModel";
import { useTranslation } from "@/hooks/useTranslation";

// Define the shape of the profile settings form (reused from Super Admin context)
interface ProfileSettingsForm {
    name: string;
    phone: string;
    address: string;
    avatar: string;
}

// Define the type for the context value
interface TeacherAdminSettingsContextType {
    user: UserSchema | undefined;
    isLoading: boolean;
    profileForm: ProfileSettingsForm;
    setProfileForm: React.Dispatch<React.SetStateAction<ProfileSettingsForm>>;
    newAvatarFile: File | null;
    setNewAvatarFile: React.Dispatch<React.SetStateAction<File | null>>;
    avatarPreviewUrl: string | null;
    setAvatarPreviewUrl: React.Dispatch<React.SetStateAction<string | null>>;
    handleAvatarChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleProfileSubmit: () => Promise<void>;
    notificationMessage: string;
    notificationType: "success" | "error";
    isNotificationCard: boolean;
    setNotification: (message: string, type: "success" | "error") => void;
    setIsNotificationCard: React.Dispatch<React.SetStateAction<boolean>>;
    activeTab: "profile"; // Only profile tab for now, as per original code
    setActiveTab: React.Dispatch<React.SetStateAction<"profile">>;
}

// Create the context
const TeacherAdminSettingsContext = createContext<
    TeacherAdminSettingsContextType | undefined
>(undefined);

// Define the props for the TeacherAdminSettingsProvider
interface TeacherAdminSettingsProviderProps {
    children: ReactNode;
}

export const TeacherAdminSettingsProvider: React.FC<
    TeacherAdminSettingsProviderProps
> = ({ children }) => {
    const { user: authUser } = useAuth();
    const { t } = useTranslation();

    const [user, setUser] = useState<UserSchema | undefined>(undefined);
    const [isLoading, setIsLoading] = useState(true);

    const [profileForm, setProfileForm] = useState<ProfileSettingsForm>({
        name: "",
        phone: "",
        address: "",
        avatar: "",
    });
    const [newAvatarFile, setNewAvatarFile] = useState<File | null>(null);
    const [avatarPreviewUrl, setAvatarPreviewUrl] = useState<string | null>(null);

    const [activeTab, setActiveTab] = useState<"profile">("profile");

    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<
        "success" | "error"
    >("success");

    // Function to set notification messages
    const setNotification = (message: string, type: "success" | "error") => {
        setNotificationMessage(message);
        setNotificationType(type);
        setIsNotificationCard(true);
    };

    // Fetch user first
    useEffect(() => {
        const fetchUser = async () => {
            if (!authUser?.user_id) {
                setIsLoading(false);
                return;
            }

            try {
                setIsLoading(true);
                const userProfile = await getUserById(authUser.user_id);
                setUser(userProfile);
                setProfileForm({
                    name: userProfile.name || "",
                    phone: userProfile.phone || "",
                    address: userProfile.address || "",
                    avatar:
                        userProfile.avatar ||
                        "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg",
                });
                setAvatarPreviewUrl(
                    userProfile.avatar ||
                    "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg"
                );
            } catch (error) {
                console.error("Failed to fetch user:", error);
                setNotification("Failed to load user profile.", "error");
            } finally {
                setIsLoading(false);
            }
        };

        fetchUser();
    }, [authUser]);

    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            if (avatarPreviewUrl?.startsWith("blob:"))
                URL.revokeObjectURL(avatarPreviewUrl);
            setNewAvatarFile(file);
            setAvatarPreviewUrl(URL.createObjectURL(file));
        }
    };

    const handleProfileSubmit = async () => {
        if (!user?.user_id) {
            setNotification("User not loaded. Cannot update profile.", "error");
            return;
        }

        try {
            let avatarToUpdate = profileForm.avatar;
            if (newAvatarFile) {
                const uploadResult = await uploadUserAvatar(user.user_id, newAvatarFile);
                avatarToUpdate = uploadResult.avatarUrl;
                setAvatarPreviewUrl(uploadResult.avatarUrl);
                setNewAvatarFile(null); // Clear the file after upload
            }

            await updateUser(user.user_id, {
                name: profileForm.name,
                user_id: user.user_id,
                role: user.role,
                phone: profileForm.phone,
                address: profileForm.address,
                avatar: avatarToUpdate,
            });

            setUser((prevUser) => ({
                ...prevUser!,
                name: profileForm.name,
                phone: profileForm.phone,
                address: profileForm.address,
                avatar: avatarToUpdate,
            }));

            setNotification(t("messages.success.profile_updated"), "success");
        } catch (error) {
            console.error("Failed to update profile:", error);
            setNotification(t("messages.error.profile_update_failed"), "error");
        }
    };

    const contextValue: TeacherAdminSettingsContextType = {
        user,
        isLoading,
        profileForm,
        setProfileForm,
        newAvatarFile,
        setNewAvatarFile,
        avatarPreviewUrl,
        setAvatarPreviewUrl,
        handleAvatarChange,
        handleProfileSubmit,
        notificationMessage,
        notificationType,
        isNotificationCard,
        setNotification,
        setIsNotificationCard,
        activeTab,
        setActiveTab,
    };

    return (
        <TeacherAdminSettingsContext.Provider value={contextValue}>
            {children}
        </TeacherAdminSettingsContext.Provider>
    );
};

// Custom hook to use the TeacherAdminSettingsContext
export const useTeacherAdminSettings = () => {
    const context = useContext(TeacherAdminSettingsContext);
    if (context === undefined) {
        throw new Error("useTeacherAdminSettings must be used within a TeacherAdminSettingsProvider");
    }
    return context;
};
