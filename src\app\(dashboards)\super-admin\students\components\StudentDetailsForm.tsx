import React, { useState } from "react";
import AsyncSelect from "react-select/async";
import { components } from "react-select";
import CustomInput from "../../../../../components/inputs/CustomInput";
import CustomSelect from "../../../../../components/inputs/CustomSelect";
import CustomDateInput from "../../../../../components/inputs/CustomDateInput";
import CustomNationalitySelect from "../../../../../components/inputs/CustomNationalitySelect";
import { searchStudents } from "@/app/services/StudentServices";
import { useTranslation } from '@/hooks/useTranslation';

interface StudentDetailsFormProps {
  formData: {
    firstName: string;
    lastName: string;
    middleName?: string;
    dateOfBirth?: string;
    nationality?: string;
    gender?: string;
    place_of_birth?: string;
    [key: string]: any;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  schoolId: string;
}

const StudentDetailsForm: React.FC<StudentDetailsFormProps> = ({ formData, handleChange, schoolId }) => {
  const { t } = useTranslation();
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [showForm, setShowForm] = useState(false);

  // Check if there's existing form data (ignore fields with default values)
  const fieldsWithDefaults = ['nationality', 'student_country_code', 'guardian_country_code', 'emergency_contact_country_code', 'doctor_country_code'];
  const hasExistingData = Object.entries(formData)
    .filter(([key]) => !fieldsWithDefaults.includes(key)) // Ignore fields with default values
    .some(([, value]) => value && value !== "");

  const customSelectStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      borderColor: state.isFocused ? '#14b8a6' : '#d1d5db',
      boxShadow: state.isFocused ? '0 0 0 2px #14b8a6' : 'none',
      padding: '2px 8px',
      borderRadius: '0.375rem',
      minHeight: '38px',
      backgroundColor: state.isDisabled ? '#f3f4f6' : 'bg-gray-100',
      color: state.isDisabled ? '#9ca3af' : 'inherit',
    }),
    input: (provided: any) => ({
      ...provided,
      margin: 0,
      padding: 0,
      color: 'inherit',
      fontSize: '0.875rem',
    }),
    placeholder: (provided: any) => ({
      ...provided,
      color: '#4b5563',
    }),
    singleValue: (provided: any) => ({
      ...provided,
      color: 'inherit',
    }),
    dropdownIndicator: (provided: any, state: any) => ({
      ...provided,
      color: state.isFocused ? '#14b8a6' : '#9ca3af',
      padding: 4,
    }),
    clearIndicator: (provided: any) => ({
      ...provided,
      padding: 4,
    }),
    menu: (provided: any) => ({
      ...provided,
      borderRadius: '0.375rem',
      boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isFocused ? '#14b8a6' : 'bg-background',
      color: state.isFocused ? 'white' : 'black',
      padding: '8px 12px',
      cursor: 'pointer',
    }),
  };

  const resetFormFields = () => {
    const fields = ["firstName", "lastName", "middleName", "dateOfBirth", "nationality", "gender", "place_of_birth"];
    fields.forEach(field =>
      handleChange({ target: { name: field, value: "" } } as React.ChangeEvent<HTMLInputElement>)
    );
  };

  const loadOptions = async (inputValue: string) => {
    if (!inputValue) return [];
    try {
      const students = await searchStudents(schoolId, { name: inputValue });
      return students.map((student: any) => ({
        label: `${student.first_name} ${student.last_name} | ${t('registration.fields.dob')}: ${student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : t('common.not_available')} | ID: ${student.student_id || t('common.not_available')}`,
        value: student._id,
        data: {
          firstName: student.first_name,
          lastName: student.last_name,
          middleName: student.middle_name,
          dateOfBirth: student.date_of_birth,
          nationality: student.nationality,
          gender: student.gender,
          place_of_birth: student.place_of_birth,
        },
      }));
    } catch (error) {
      console.error(error);
      return [];
    }
  };

  const handleStudentSelect = (option: any) => {
    setSelectedStudent(option);
    setShowForm(true);

    if (option?.data) {
      const s = option.data;
      handleChange({ target: { name: "firstName", value: s.firstName || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "lastName", value: s.lastName || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "middleName", value: s.middleName || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "dateOfBirth", value: s.dateOfBirth ? s.dateOfBirth.substring(0, 10) : "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "nationality", value: s.nationality || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "gender", value: s.gender || "" } } as React.ChangeEvent<HTMLInputElement>);
      handleChange({ target: { name: "place_of_birth", value: s.place_of_birth || "" } } as React.ChangeEvent<HTMLInputElement>);
    } else {
      const fields = ["firstName", "lastName", "middleName", "dateOfBirth", "nationality", "gender", "place_of_birth"];
      fields.forEach(field =>
        handleChange({ target: { name: field, value: "" } } as React.ChangeEvent<HTMLInputElement>)
      );
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    if (selectedStudent) setSelectedStudent(null);
    handleChange(e);
  };

  return (
    <>
      {!showForm && (
        <div className="col-span-2 mb-4">
          {/* {hasExistingData && (
            <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                You have existing form data. You can search for a student to replace it, or continue with manual entry.
              </p>
              <button
                type="button"
                onClick={() => setShowForm(true)}
                className="mt-2 text-sm text-blue-600 hover:underline font-medium"
              >
                Continue with existing data
              </button>
            </div>
          )} */}

          <label className="block text-sm font-medium mb-1">
            {t('registration.fields.search_student')}
          </label>
          <AsyncSelect
            cacheOptions
            loadOptions={loadOptions}
            onChange={handleStudentSelect}
            placeholder={t('registration.fields.search_placeholder')}
            value={selectedStudent}
            isClearable
            styles={customSelectStyles}
            components={{
              Input: (props) => (
                <components.Input {...props} autoComplete="off" aria-autocomplete="none" />
              ),
            }}
          />
          <button
            type="button"
            onClick={() => setShowForm(true)}
            className="mt-2 text-sm text-teal hover:underline"
          >
            {t('registration.actions.add_new_student')}
          </button>
        </div>
      )}

      {(showForm || selectedStudent) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="col-span-2 flex justify-end mb-2">
            {(selectedStudent || Object.values(formData).some((val) => val && val !== "")) && (
              <button
                type="button"
                onClick={() => {
                  setSelectedStudent(null);
                  resetFormFields();
                  setShowForm(false);
                }}
                className="text-xs text-red-500 underline"
              >
                {t('registration.actions.clear_selected_student')}
              </button>
            )}

          </div>

          <CustomInput
            label={t('registration.fields.first_name')}
            id="firstName"
            name="firstName"
            value={formData.firstName}
            onChange={handleInputChange}
            required
          />

          <CustomInput
            label={t('registration.fields.last_name')}
            id="lastName"
            name="lastName"
            value={formData.lastName}
            onChange={handleInputChange}
            required
          />

          <CustomInput
            label={t('registration.fields.middle_name')}
            id="middleName"
            placeholder={t('common.optional')}
            name="middleName"
            value={formData.middleName || ""}
            onChange={handleInputChange}
          />

          <CustomDateInput
            label={t('registration.fields.date_of_birth')}
            id="dateOfBirth"
            name="dateOfBirth"
            value={formData.dateOfBirth || ""}
            onChange={handleInputChange}
            required
          />

          <CustomNationalitySelect
            label={t('registration.fields.nationality')}
            id="nationality"
            name="nationality"
            value={formData.nationality || ""}
            onChange={handleInputChange}
            required
          />

          <CustomSelect
            label={t('common.gender')}
            id="gender"
            name="gender"
            value={formData.gender || ""}
            onChange={handleInputChange}
            required
            options={[
              { label: t('registration.fields.male'), value: "Male" },
              { label: t('registration.fields.female'), value: "Female" },
            ]}
            placeholder={t('registration.fields.select_gender')}
          />

          <CustomInput
            label={t('registration.fields.place_of_birth')}
            id="place_of_birth"
            name="place_of_birth"
            value={formData.place_of_birth || ""}
            onChange={handleInputChange}
            required
          />
        </div>
      )}
    </>
  );
};

export default StudentDetailsForm;
