"use client";

import { FileCheck2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import React, { Suspense } from "react";
import useAuth from "@/app/hooks/useAuth";
import StudentAttendance from "@/components/attendance/StudentAttendance";
import { useSearchParams } from "next/navigation";
import { useTranslation } from "@/hooks/useTranslation";

const BASE_URL = "/school-admin";

export default function Page() {
  const { logout } = useAuth();
  const { user } = useAuth();
  const { t, tDashboard } = useTranslation();
  const searchParams = useSearchParams();

  const navigation = {
    icon: FileCheck2,
    baseHref: `${BASE_URL}/attendance`,
    title: tDashboard('school-admin', 'attendance', 'title'),
  };
  const schoolId = user?.school_ids?.[0] ?? null;
  const classIdRaw = searchParams.get("classId");
  const subjectId = searchParams.get("subjectId");

  

  return (
    <Suspense>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        {user && (
          <StudentAttendance
            schoolId={schoolId as string}
            classIdRaw={classIdRaw as string}
            subjectId={subjectId as string}
          />
        )}
      </SchoolLayout>
    </Suspense>
  );
}
