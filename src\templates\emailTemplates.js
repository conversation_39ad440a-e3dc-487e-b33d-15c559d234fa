// Email templates for staff management

const getEmailTemplate = (type, data) => {
  const baseStyle = `
    <style>
      body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
      .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
      .header { background: linear-gradient(135deg, #17B890 0%, #15A085 100%); padding: 40px 30px; text-align: center; }
      .logo { color: #ffffff; font-size: 28px; font-weight: bold; margin-bottom: 10px; }
      .header-subtitle { color: #ffffff; font-size: 16px; opacity: 0.9; }
      .content { padding: 40px 30px; }
      .welcome-text { font-size: 24px; color: #2c3e50; margin-bottom: 20px; font-weight: 600; }
      .description { font-size: 16px; color: #5a6c7d; line-height: 1.6; margin-bottom: 30px; }
      .button { display: inline-block; background: linear-gradient(135deg, #17B890 0%, #15A085 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 20px 0; }
      .button:hover { background: linear-gradient(135deg, #15A085 0%, #138A75 100%); }
      .info-box { background-color: #f8f9fa; border-left: 4px solid #17B890; padding: 20px; margin: 20px 0; border-radius: 4px; }
      .credentials { background-color: #e8f5e5; padding: 20px; border-radius: 8px; margin: 20px 0; }
      .credential-item { margin: 10px 0; }
      .credential-label { font-weight: 600; color: #2c3e50; }
      .credential-value { font-family: 'Courier New', monospace; background-color: #ffffff; padding: 8px 12px; border-radius: 4px; border: 1px solid #ddd; display: inline-block; margin-left: 10px; }
      .footer { background-color: #2c3e50; color: #ffffff; padding: 30px; text-align: center; }
      .footer-text { font-size: 14px; opacity: 0.8; margin-bottom: 10px; }
      .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 4px; margin: 20px 0; }
      .expiry { color: #e74c3c; font-weight: 600; }
    </style>
  `;

  const templates = {
    // Template for new staff welcome email
    staffWelcome: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Scholarify</title>
        ${baseStyle}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">📚 Scholarify</div>
            <div class="header-subtitle">School Management System</div>
          </div>
          
          <div class="content">
            <div class="welcome-text">Welcome to the team, ${data.firstName}! 👋</div>
            
            <div class="description">
              You have been added as a <strong>${data.roleDisplay}</strong> to <strong>${data.schoolName}</strong> 
              in our school management system. We're excited to have you on board!
            </div>

            <div class="info-box">
              <strong>🔐 Account Setup Required</strong><br>
              To get started, you'll need to set up your password by clicking the button below. 
              This link is secure and will expire in 24 hours for your protection.
            </div>

            <div style="text-align: center;">
              <a href="${data.resetUrl}" class="button">Set Up My Password 🚀</a>
            </div>

            <div class="credentials">
              <div class="credential-item">
                <span class="credential-label">📧 Email:</span>
                <span class="credential-value">${data.email}</span>
              </div>
              <div class="credential-item">
                <span class="credential-label">👤 Role:</span>
                <span class="credential-value">${data.roleDisplay}</span>
              </div>
              <div class="credential-item">
                <span class="credential-label">🏫 School:</span>
                <span class="credential-value">${data.schoolName}</span>
              </div>
              ${data.accessCode ? `
              <div class="credential-item">
                <span class="credential-label">🔑 Access Code:</span>
                <span class="credential-value">${data.accessCode}</span>
              </div>
              ` : ''}
            </div>

            ${data.role === 'teacher' ? `
            <div class="warning">
              <strong>📝 Note for Teachers:</strong> As a teacher, you can work with multiple schools. 
              Use your access code when selecting schools in your dashboard.
            </div>
            ` : ''}

            <div class="description">
              <strong>What's next?</strong><br>
              1. Click the "Set Up My Password" button above<br>
              2. Create a secure password<br>
              3. ${data.role === 'teacher' ? 'Access your teacher dashboard and select your school' : 'Start using your school admin dashboard'}<br>
              4. Explore your permissions and start managing your responsibilities
            </div>

            <div class="warning">
              <span class="expiry">⏰ Important:</span> This setup link will expire in 24 hours. 
              If you don't set up your password within this time, please contact your school administrator.
            </div>
          </div>

          <div class="footer">
            <div class="footer-text">
              If you have any questions or need assistance, please contact your school administrator.
            </div>
            <div class="footer-text">
              Best regards,<br>The Scholarify Team
            </div>
          </div>
        </div>
      </body>
      </html>
    `,

    // Template for password reset email
    passwordReset: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset - Scholarify</title>
        ${baseStyle}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">📚 Scholarify</div>
            <div class="header-subtitle">Password Reset Request</div>
          </div>
          
          <div class="content">
            <div class="welcome-text">Password Reset Request 🔐</div>
            
            <div class="description">
              Hello <strong>${data.firstName}</strong>,<br><br>
              We received a request to reset your password for your Scholarify account. 
              If you made this request, click the button below to reset your password.
            </div>

            <div style="text-align: center;">
              <a href="${data.resetUrl}" class="button">Reset My Password 🔄</a>
            </div>

            <div class="info-box">
              <strong>🛡️ Security Information</strong><br>
              • This link will expire in 24 hours<br>
              • You can only use this link once<br>
              • If you didn't request this reset, you can safely ignore this email
            </div>

            <div class="warning">
              <span class="expiry">⏰ Link expires:</span> ${data.expiryTime}
            </div>

            <div class="description">
              If the button doesn't work, you can copy and paste this link into your browser:<br>
              <code style="background-color: #f8f9fa; padding: 8px; border-radius: 4px; word-break: break-all;">
                ${data.resetUrl}
              </code>
            </div>
          </div>

          <div class="footer">
            <div class="footer-text">
              If you didn't request this password reset, please contact your administrator immediately.
            </div>
            <div class="footer-text">
              Best regards,<br>The Scholarify Team
            </div>
          </div>
        </div>
      </body>
      </html>
    `,

    // Template for access code generation
    accessCodeGenerated: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Access Code - Scholarify</title>
        ${baseStyle}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">📚 Scholarify</div>
            <div class="header-subtitle">New School Access Code</div>
          </div>
          
          <div class="content">
            <div class="welcome-text">New Access Code Generated 🔑</div>
            
            <div class="description">
              Hello <strong>${data.firstName}</strong>,<br><br>
              A new access code has been generated for you to access <strong>${data.schoolName}</strong>.
            </div>

            <div class="credentials">
              <div class="credential-item">
                <span class="credential-label">🏫 School:</span>
                <span class="credential-value">${data.schoolName}</span>
              </div>
              <div class="credential-item">
                <span class="credential-label">🔑 Access Code:</span>
                <span class="credential-value">${data.accessCode}</span>
              </div>
              <div class="credential-item">
                <span class="credential-label">📅 Generated:</span>
                <span class="credential-value">${data.generatedAt}</span>
              </div>
            </div>

            <div class="info-box">
              <strong>📝 How to use your access code:</strong><br>
              1. Log in to your teacher dashboard<br>
              2. Select the school you want to access<br>
              3. Enter this access code when prompted<br>
              4. Start teaching and managing your classes!
            </div>

            <div style="text-align: center;">
              <a href="${data.dashboardUrl}" class="button">Go to Teacher Dashboard 🎓</a>
            </div>
          </div>

          <div class="footer">
            <div class="footer-text">
              Keep your access code secure and don't share it with others.
            </div>
            <div class="footer-text">
              Best regards,<br>The Scholarify Team
            </div>
          </div>
        </div>
      </body>
      </html>
    `,

    // Template pour Super Administrateur
    superAdminWelcome: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bienvenue Super Administrateur - Scholarify</title>
        ${baseStyle}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🚀 Scholarify</div>
            <div class="header-subtitle">Plateforme de Gestion Scolaire</div>
          </div>

          <div class="content">
            <div class="welcome-text">Bienvenue Super Administrateur, ${data.firstName}! 👑</div>

            <div class="description">
              Vous avez été ajouté comme <strong>Super Administrateur</strong> sur Scholarify.
              Vous avez accès à toutes les fonctionnalités de la plateforme et pouvez gérer toutes les écoles.
            </div>

            <div class="info-box">
              <strong>🔐 Configuration de votre compte</strong><br>
              Pour commencer, configurez votre mot de passe en cliquant sur le bouton ci-dessous.
              Ce lien est sécurisé et expirera dans 24 heures.
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${data.resetUrl}" class="button">Configurer mon mot de passe 🔑</a>
            </div>

            <div style="text-align: center; margin: 20px 0;">
              <a href="${data.dashboardUrl}" class="button" style="background: #10B981;">Accéder au Dashboard Super Admin 🎛️</a>
            </div>

            <div class="info-box">
              <strong>🎯 Vos privilèges Super Admin :</strong><br>
              • Gestion de toutes les écoles<br>
              • Configuration système globale<br>
              • Supervision des administrateurs<br>
              • Accès aux rapports globaux<br>
              • Gestion des souscriptions
            </div>
          </div>

          <div class="footer">
            <div class="footer-text">
              En tant que Super Administrateur, vous avez la responsabilité de maintenir la sécurité et l'intégrité de la plateforme.
            </div>
            <div class="footer-text">
              Cordialement,<br>L'équipe Scholarify
            </div>
          </div>
        </div>
      </body>
      </html>
    `,

    // Template pour Administrateur d'École
    schoolAdminWelcome: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bienvenue Administrateur - Scholarify</title>
        ${baseStyle}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🏫 Scholarify</div>
            <div class="header-subtitle">Système de Gestion Scolaire</div>
          </div>

          <div class="content">
            <div class="welcome-text">Bienvenue ${data.firstName}! 🎉</div>

            <div class="description">
              Vous avez été ajouté comme <strong>${data.roleDisplay}</strong> à <strong>${data.schoolName}</strong>.
              Gérez efficacement votre établissement avec tous les outils Scholarify.
            </div>

            <div class="info-box">
              <strong>🔐 Configuration de votre compte</strong><br>
              Pour commencer, configurez votre mot de passe en cliquant sur le bouton ci-dessous.
              Ce lien est sécurisé et expirera dans 24 heures.
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${data.resetUrl}" class="button">Configurer mon mot de passe 🔑</a>
            </div>

            <div style="text-align: center; margin: 20px 0;">
              <a href="${data.dashboardUrl}" class="button" style="background: #3B82F6;">Accéder au Dashboard École 🏫</a>
            </div>

            <div class="info-box">
              <strong>🎯 Vos responsabilités :</strong><br>
              • Gestion du personnel enseignant<br>
              • Supervision des élèves et classes<br>
              • Suivi des performances académiques<br>
              • Gestion des finances de l'école<br>
              • Communication avec les parents
            </div>
          </div>

          <div class="footer">
            <div class="footer-text">
              Nous sommes ravis de vous accompagner dans la gestion de ${data.schoolName}.
            </div>
            <div class="footer-text">
              Cordialement,<br>L'équipe Scholarify
            </div>
          </div>
        </div>
      </body>
      </html>
    `,

    // Template pour Enseignant
    teacherWelcome: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bienvenue Enseignant - Scholarify</title>
        ${baseStyle}
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">📚 Scholarify</div>
            <div class="header-subtitle">Plateforme Éducative</div>
          </div>

          <div class="content">
            <div class="welcome-text">Bienvenue dans l'équipe, ${data.firstName}! 👨‍🏫</div>

            <div class="description">
              Vous avez rejoint l'équipe enseignante de <strong>${data.schoolName}</strong>.
              Enseignez, suivez vos élèves et gérez vos classes avec Scholarify.
            </div>

            ${data.accessCode ? `
            <div class="info-box" style="background: #FEF3C7; border-left: 4px solid #F59E0B;">
              <strong>🔑 Votre code d'accès : ${data.accessCode}</strong><br>
              Utilisez ce code pour accéder à votre école depuis le dashboard enseignant.
            </div>
            ` : ''}

            <div class="info-box">
              <strong>🔐 Configuration de votre compte</strong><br>
              Pour commencer, configurez votre mot de passe en cliquant sur le bouton ci-dessous.
              Ce lien est sécurisé et expirera dans 24 heures.
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${data.resetUrl}" class="button">Configurer mon mot de passe 🔑</a>
            </div>

            <div style="text-align: center; margin: 20px 0;">
              <a href="${data.dashboardUrl}" class="button" style="background: #8B5CF6;">Accéder au Dashboard Enseignant 🎓</a>
            </div>

            <div class="info-box">
              <strong>🎯 Vos outils d'enseignement :</strong><br>
              • Gestion de vos classes et élèves<br>
              • Saisie des notes et évaluations<br>
              • Suivi des présences<br>
              • Communication avec les parents<br>
              • Planification des cours
            </div>
          </div>

          <div class="footer">
            <div class="footer-text">
              Nous sommes fiers de vous accueillir dans notre communauté éducative.
            </div>
            <div class="footer-text">
              Cordialement,<br>L'équipe Scholarify
            </div>
          </div>
        </div>
      </body>
      </html>
    `
  };

  return templates[type] || '';
};

module.exports = { getEmailTemplate };
