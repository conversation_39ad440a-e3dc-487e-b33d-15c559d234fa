"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, AlertTriangle, Wifi, Server, Key, RefreshCw } from 'lucide-react';
import { ErrorDetails } from '@/app/services/ErrorHandlingService';

interface LoginErrorCardProps {
  errorDetails: ErrorDetails | null;
  isVisible: boolean;
  onClose: () => void;
  onRetry?: () => void;
}

export default function LoginErrorCard({
  errorDetails,
  isVisible,
  onClose,
  onRetry
}: LoginErrorCardProps) {
  if (!errorDetails || !isVisible) return null;

  const getIcon = () => {
    switch (errorDetails.type) {
      case 'credentials':
        return <Key className="h-5 w-5" />;
      case 'network':
        return <Wifi className="h-5 w-5" />;
      case 'server':
        return <Server className="h-5 w-5" />;
      default:
        return <AlertTriangle className="h-5 w-5" />;
    }
  };

  const getColorClasses = () => {
    switch (errorDetails.type) {
      case 'credentials':
        return {
          border: 'border-l-orange-500',
          bg: 'bg-orange-50 dark:bg-orange-900/20',
          text: 'text-orange-800 dark:text-orange-200',
          icon: 'text-orange-600 dark:text-orange-400',
          button: 'text-orange-600 hover:text-orange-800 dark:text-orange-400 dark:hover:text-orange-200'
        };
      case 'network':
        return {
          border: 'border-l-blue-500',
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          text: 'text-blue-800 dark:text-blue-200',
          icon: 'text-blue-600 dark:text-blue-400',
          button: 'text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200'
        };
      case 'server':
        return {
          border: 'border-l-red-500',
          bg: 'bg-red-50 dark:bg-red-900/20',
          text: 'text-red-800 dark:text-red-200',
          icon: 'text-red-600 dark:text-red-400',
          button: 'text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200'
        };
      case 'validation':
        return {
          border: 'border-l-yellow-500',
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          text: 'text-yellow-800 dark:text-yellow-200',
          icon: 'text-yellow-600 dark:text-yellow-400',
          button: 'text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-200'
        };
      default:
        return {
          border: 'border-l-gray-500',
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          text: 'text-gray-800 dark:text-gray-200',
          icon: 'text-gray-600 dark:text-gray-400',
          button: 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200'
        };
    }
  };

  const colors = getColorClasses();

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -10, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className={`mb-4 p-4 rounded-lg border-l-4 ${colors.border} ${colors.bg} shadow-sm`}
        >
          <div className="flex items-start space-x-3">
            {/* Icône */}
            <div className={`flex-shrink-0 ${colors.icon} mt-0.5`}>
              {getIcon()}
            </div>

            {/* Contenu */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className={`text-sm font-semibold ${colors.text} mb-1`}>
                    {errorDetails.title}
                  </h3>
                  <p className={`text-sm ${colors.text} leading-relaxed`}>
                    {errorDetails.message}
                  </p>
                  
                  {errorDetails.actionMessage && (
                    <p className={`text-xs ${colors.text} mt-2 opacity-90`}>
                      💡 {errorDetails.actionMessage}
                    </p>
                  )}

                  {/* Informations de debug en mode développement */}
                  {process.env.NODE_ENV === 'development' && errorDetails.statusCode && (
                    <p className={`text-xs ${colors.text} mt-1 opacity-70 font-mono`}>
                      Code: {errorDetails.statusCode} | Type: {errorDetails.type}
                    </p>
                  )}
                </div>

                {/* Bouton fermer */}
                <button
                  onClick={onClose}
                  className={`flex-shrink-0 ml-2 ${colors.button} hover:bg-black/5 dark:hover:bg-white/5 rounded-full p-1 transition-colors`}
                  aria-label="Fermer"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>

              {/* Bouton de retry si applicable */}
              {errorDetails.canRetry && onRetry && (
                <div className="mt-3 flex items-center space-x-2">
                  <button
                    onClick={onRetry}
                    className={`inline-flex items-center space-x-1 text-xs font-medium ${colors.button} hover:bg-black/5 dark:hover:bg-white/5 px-2 py-1 rounded transition-colors`}
                  >
                    <RefreshCw className="h-3 w-3" />
                    <span>Réessayer</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

/**
 * Composant simplifié pour les erreurs de connexion rapides
 */
export function QuickLoginError({
  message,
  type = 'credentials',
  isVisible,
  onClose
}: {
  message: string;
  type?: 'credentials' | 'server' | 'network';
  isVisible: boolean;
  onClose: () => void;
}) {
  const errorDetails: ErrorDetails = {
    type,
    title: type === 'credentials' ? 'Identifiants incorrects' : 
           type === 'server' ? 'Erreur serveur' : 'Problème de connexion',
    message,
    canRetry: true
  };

  return (
    <LoginErrorCard
      errorDetails={errorDetails}
      isVisible={isVisible}
      onClose={onClose}
    />
  );
}

/**
 * Hook pour gérer l'état des erreurs de connexion
 */
export function useLoginError() {
  const [errorDetails, setErrorDetails] = React.useState<ErrorDetails | null>(null);
  const [isVisible, setIsVisible] = React.useState(false);

  const showError = (details: ErrorDetails) => {
    setErrorDetails(details);
    setIsVisible(true);
  };

  const hideError = () => {
    setIsVisible(false);
    // Délai pour permettre l'animation de sortie
    setTimeout(() => setErrorDetails(null), 200);
  };

  const clearError = () => {
    setErrorDetails(null);
    setIsVisible(false);
  };

  return {
    errorDetails,
    isVisible,
    showError,
    hideError,
    clearError
  };
}
