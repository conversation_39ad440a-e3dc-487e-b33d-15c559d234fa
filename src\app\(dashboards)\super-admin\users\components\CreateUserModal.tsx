"use client";

import React, { useEffect, useState } from "react";
import { X, ChevronDown } from "lucide-react";
import { UserSchema, UserCreateSchema } from "@/app/models/UserModel";
import CustomInput from "@/components/inputs/CustomInput";
import CustomPhoneInput from "@/components/inputs/CustomPhoneInput";
import { SchoolSchema } from "@/app/models/SchoolModel";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";
import ActionButton from "@/components/ActionButton";
import BackButton from "@/components/BackButton";
import { useTranslation } from "@/hooks/useTranslation";

interface CreateUserModalProps {
  onClose: () => void;
  onSave: (userData: UserCreateSchema) => void;
  roles: string[];
  schools: SchoolSchema[];  // Updated to handle school_id and name
  initialData?: UserCreateSchema;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
}

const CreateUserModal: React.FC<CreateUserModalProps> = ({
  onClose,
  onSave,
  roles,
  schools,
  initialData,
  submitStatus,
  isSubmitting,
}) => {
  const { t, tDashboard } = useTranslation();
  const [formData, setFormData] = useState<UserCreateSchema>({
    name: "",
    email: "",
    role: "admin",
    password: "",
    phone: "",
    address: "",
    school_ids: [],
  });

  const [countryCode, setCountryCode] = useState("+237");
  const [searchTerm, setSearchTerm] = useState("");
  const [showSchoolDropdown, setShowSchoolDropdown] = useState(false);

  useEffect(() => {
    if (initialData) {
      const rawPhone = initialData.phone?.replace(/\s|-/g, "") || "";
      const code = rawPhone.match(/^\+\d{1,3}/)?.[0] || "+237";
      const number = rawPhone.replace(code, "");

      // console.log("Initial Data:", initialData); 
      // console.log("Parsed countryCode:", code); 
      // console.log("Parsed number:", number); 

      setFormData({
        name: initialData.name || "",
        email: initialData.email || "",
        password: initialData.password || "", // Optional: leave empty if you don't want to show it during edit
        role: initialData.role,
        phone: number,
        address: initialData.address || "",
        school_ids: initialData.school_ids || [],
      });

      setCountryCode(code);
    }
  }, [initialData]);


  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      ...formData,
      phone: `${countryCode}${formData.phone}`,
      name: formData.name,
      email: formData.email,
      role: formData.role,
      password: formData.password,
      address: formData.address,
      school_ids: formData.school_ids || [],  // Ensure school_ids is set
    });
  };

  const filteredSchools = (schools || [])
    .filter((school) =>
      school.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .slice(0, 2);


  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md mx-4 sm:mx-6 md:mx-0 p-6 relative">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-foreground">{tDashboard('super-admin', 'users', 'add_user')}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>
        {submitStatus ? (
          <SubmissionFeedback status={submitStatus}
            message={
              submitStatus === "success"
                ? t('messages.success.saved')
                : t('messages.error.generic')
            } />
        ) : (
          <form onSubmit={handleSubmit}>
            {/* Full Name */}
            <CustomInput
              label={t('common.name')}
              id="fullName"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />

            <CustomInput
              label={t('common.email')}
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
            />

            <CustomInput
              label={t('forms.placeholders.password')}
              id="password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              required
            />

            {/* Phone */}
            <CustomPhoneInput
              label={t('common.phone')}
              id="phone"
              name="phone"
              value={formData.phone || ""}
              onChange={handleChange}
              countryCode={countryCode}
              onCountryCodeChange={(e) => setCountryCode(e.target.value)}
              required countryCodeName={""} />

            {/* Address */}
            <CustomInput
              label={t('common.address')}
              id="address"
              name="address"
              value={formData.address}
              onChange={handleChange}
              required
            />

            {/* School and Role (Flex Row) */}
            <div className="mb-4 flex flex-wrap gap-4">
              {/* Role Dropdown */}
              <div className="flex-1">
                <label className="block text-sm mb-1">{t('forms.placeholders.role')}</label>
                <select
                  name="role"
                  value={formData.role}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-teal "
                  required
                >
                  <option value="">{t('forms.placeholders.select_role')}</option>
                  {roles.map((role) => (
                    <option key={role} value={role}>
                      {role}
                    </option>
                  ))}
                </select>
              </div>

              {/* School (Searchable Checkboxes Dropdown) */}
              <div className="relative flex-1">
                <label className="block text-sm mb-1">{t('navigation.schools')}</label>
                <div
                  onClick={() => setShowSchoolDropdown((prev) => !prev)}
                  className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 bg-white dark:text-white cursor-pointer flex items-center justify-between"
                >
                  <span>
                    {formData.school_ids && formData.school_ids.length > 0
                      ? schools
                        .filter((school) => formData.school_ids.includes(school._id))
                        .map((school) => school.name)
                        .join(", ")
                      : "Select schools"}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </div>

                {showSchoolDropdown && (
                  <div className="absolute z-10 bg-white dark:bg-gray-700 w-full mt-1 rounded-md border max-h-48 overflow-y-auto p-2 shadow-lg">
                    <input
                      type="text"
                      placeholder="Search school..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full mb-2 px-2 py-1 border rounded-md text-sm dark:bg-gray-600"
                    />

                    {filteredSchools.length > 0 ? (
                      filteredSchools.map((school) => (
                        <label
                          key={school._id}
                          className="flex items-center gap-2 px-2 py-1 text-sm"
                        >
                          <input
                            type="checkbox"
                            checked={formData.school_ids.includes(school._id)}
                            onChange={(e) => {
                              const isChecked = e.target.checked;
                              setFormData((prev) => ({
                                ...prev,
                                school_ids: isChecked
                                  ? [...prev.school_ids, school._id]
                                  : prev.school_ids.filter((id) => id !== school._id),
                              }));
                            }}
                          />
                          {school.name}
                        </label>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">No schools found</p>
                    )}

                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-2 mt-6">
              <ActionButton
                action="cancel"
                onClick={onClose}
                disabled={isSubmitting}
              />
              <ActionButton
                action="save"
                type="submit"
                isLoading={isSubmitting}
                disabled={isSubmitting}
              />
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default CreateUserModal;
