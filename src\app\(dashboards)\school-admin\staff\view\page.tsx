"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ArrowLeft, User, Mail, Phone, Calendar, Shield, Key, BookOpen } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { StaffSchema, getStaffById } from "@/app/services/StaffServices";
import NotificationCard from "@/components/NotificationCard";
import { motion } from "framer-motion";

const BASE_URL = "/school-admin";

const navigation = {
  icon: User,
  baseHref: `${BASE_URL}/staff/view`,
  title: "Staff Details"
};

export default function ViewStaffPage() {
  const { logout } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const staffId = searchParams.get("id");

  const [staff, setStaff] = useState<StaffSchema | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStaff = async () => {
      if (!staffId) {
        setError("No staff ID provided");
        setLoading(false);
        return;
      }

      try {
        const staffData = await getStaffById(staffId);
        setStaff(staffData);
      } catch (err) {
        console.error("Error fetching staff:", err);
        setError("Failed to load staff details");
      } finally {
        setLoading(false);
      }
    };

    fetchStaff();
  }, [staffId]);

  const getRoleBadge = (role: string) => {
    const colors = {
      school_admin: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      teacher: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      bursar: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      dean_of_studies: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      custom: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"
    };
    return colors[role as keyof typeof colors] || colors.custom;
  };

  const formatRoleName = (role: string) => {
    const roleNames = {
      school_admin: "School Admin",
      teacher: "Teacher",
      bursar: "Bursar",
      dean_of_studies: "Dean of Studies",
      custom: "Custom"
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["admin", "school_admin"]}>
        <SchoolLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={() => logout()}
        >
          <div className="flex justify-center items-center h-64">
            <CircularLoader size={32} color="teal" />
          </div>
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  if (error || !staff) {
    return (
      <ProtectedRoute allowedRoles={["admin", "school_admin"]}>
        <SchoolLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={() => logout()}
        >
          <div className="space-y-6">
            <NotificationCard
              message={error || "Staff member not found"}
              type="error"
              onClose={() => router.push(`${BASE_URL}/staff`)}
              isVisible={true}
            />
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => router.push(`${BASE_URL}/staff`)}
              className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
            >
              <ArrowLeft size={16} />
              <span>Back to Staff</span>
            </motion.button>
          </div>
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => router.push(`${BASE_URL}/staff`)}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-foreground rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <ArrowLeft size={16} />
              <span>Back to Staff</span>
            </motion.button>

            <div className="flex items-center space-x-2">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                staff.is_staff_active 
                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                  : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
              }`}>
                {staff.is_staff_active ? "Active" : "Inactive"}
              </span>
            </div>
          </div>

          {/* Staff Profile */}
          <div className="bg-widget rounded-lg border border-stroke p-6 space-y-6">
            {/* Basic Info */}
            <div className="flex items-start space-x-6">
              <div className="w-20 h-20 bg-teal rounded-full flex items-center justify-center text-white text-2xl font-bold">
                {staff.first_name?.[0]}{staff.last_name?.[0]}
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-2xl font-bold text-foreground">
                    {staff.first_name} {staff.last_name}
                  </h1>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleBadge(staff.role)}`}>
                    {formatRoleName(staff.role)}
                  </span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-foreground/70">
                    <Mail className="h-4 w-4" />
                    <span>{staff.email}</span>
                  </div>
                  
                  {staff.phone && (
                    <div className="flex items-center space-x-2 text-foreground/70">
                      <Phone className="h-4 w-4" />
                      <span>{staff.phone}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2 text-foreground/70">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {formatDate(staff.createdAt)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Staff ID */}
            {staff.staff_id && (
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-foreground/60" />
                  <span className="text-sm font-medium text-foreground/60">Staff ID:</span>
                  <span className="text-sm font-mono text-foreground">{staff.staff_id}</span>
                </div>
              </div>
            )}
          </div>

          {/* Access Codes (for teachers) */}
          {staff.role === 'teacher' && staff.access_codes && staff.access_codes.length > 0 && (
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Key className="h-5 w-5 text-foreground" />
                <h2 className="text-lg font-semibold text-foreground">School Access Codes</h2>
              </div>
              
              <div className="space-y-3">
                {staff.access_codes.map((accessCode, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <p className="font-medium text-foreground">Access Code</p>
                      <p className="text-sm text-foreground/60">
                        Granted on {formatDate(accessCode.granted_at)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-mono text-lg font-bold text-teal">{accessCode.access_code}</p>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        accessCode.is_active 
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                      }`}>
                        {accessCode.is_active ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Permissions */}
          {staff.permissions && (
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Shield className="h-5 w-5 text-foreground" />
                <h2 className="text-lg font-semibold text-foreground">Permissions</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(staff.permissions.permissions).map(([module, permissions]) => (
                  <div key={module} className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <h3 className="font-medium text-foreground mb-2 capitalize">
                      {module.replace('_', ' ')}
                    </h3>
                    <div className="space-y-1">
                      {Object.entries(permissions as Record<string, boolean>).map(([permission, hasPermission]) => (
                        <div key={permission} className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${
                            hasPermission ? 'bg-green-500' : 'bg-gray-300'
                          }`} />
                          <span className={`text-xs ${
                            hasPermission ? 'text-foreground' : 'text-foreground/50'
                          }`}>
                            {permission.replace(/_/g, ' ')}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Class Assignments (for teachers) */}
          {staff.permissions?.assigned_classes && staff.permissions.assigned_classes.length > 0 && (
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="h-5 w-5 text-foreground" />
                <h2 className="text-lg font-semibold text-foreground">Class Assignments</h2>
              </div>
              
              <div className="space-y-3">
                {staff.permissions.assigned_classes.map((assignment, index) => (
                  <div key={index} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-foreground">Class ID: {assignment.class_id}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <div>
                            <p className="text-sm text-foreground/60">Subjects:</p>
                            <p className="text-sm text-foreground">{assignment.subjects.join(', ') || 'None'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-foreground/60">Periods:</p>
                            <p className="text-sm text-foreground">{assignment.periods.join(', ') || 'None'}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}


        </div>
      </SchoolLayout>
  );
}
