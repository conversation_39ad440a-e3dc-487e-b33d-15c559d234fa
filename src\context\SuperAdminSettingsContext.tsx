"use client";

import React, {
    createContext,
    useContext,
    useState,
    useEffect,
    ReactNode,
} from "react";
// Ensure these paths match your project's alias configuration
import { UserSchema } from "@/app/models/UserModel";
import { SettingsSchema } from "@/app/models/Settings";
import {
    getUserById,
    updateUser,
    uploadUserAvatar,
} from "@/app/services/UserServices";
import {
    getSettings,
    updateCreditSettings,
    updateGeneralSettings,
} from "@/app/services/Settings";
import useAuth from "@/app/hooks/useAuth";
import { useTranslation } from "@/hooks/useTranslation";

// Define the shape of the general settings form
interface GeneralSettingsForm {
    platform_name: string;
    support_email: string;
    default_language: string;
    maintenance_mode: boolean;
    maintenance_message?: string;
}

// Define the shape of the credit settings form
interface CreditSettingsForm {
    resell_price_per_credit: number;
    buy_price_per_credit: number;
}

// Define the shape of the profile settings form
interface ProfileSettingsForm {
    name: string;
    phone: string;
    address: string;
    avatar: string;
}

// Define the type for the context value
interface SettingsContextType {
    user: UserSchema | undefined;
    settings: SettingsSchema | undefined;
    isLoading: boolean;
    profileForm: ProfileSettingsForm;
    setProfileForm: React.Dispatch<React.SetStateAction<ProfileSettingsForm>>;
    newAvatarFile: File | null;
    setNewAvatarFile: React.Dispatch<React.SetStateAction<File | null>>;
    avatarPreviewUrl: string | null;
    setAvatarPreviewUrl: React.Dispatch<React.SetStateAction<string | null>>;
    creditForm: CreditSettingsForm;
    setCreditForm: React.Dispatch<React.SetStateAction<CreditSettingsForm>>;
    generalForm: GeneralSettingsForm;
    setGeneralForm: React.Dispatch<React.SetStateAction<GeneralSettingsForm>>;
    handleAvatarChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleProfileSubmit: () => Promise<void>;
    handleCreditSubmit: () => Promise<void>;
    handleGeneralSubmit: () => Promise<void>;
    notificationMessage: string;
    notificationType: "success" | "error";
    isNotificationCard: boolean;
    setNotification: (message: string, type: "success" | "error") => void;
    setIsNotificationCard: React.Dispatch<React.SetStateAction<boolean>>;
}

// Create the context
const SettingsContext = createContext<SettingsContextType | undefined>(
    undefined
);

// Define the props for the SettingsProvider
interface SettingsProviderProps {
    children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({
    children,
}) => {
    const { t, tDashboard } = useTranslation();
    const { user: authUser } = useAuth();

    const [user, setUser] = useState<UserSchema | undefined>(undefined);
    const [settings, setSettings] = useState<SettingsSchema | undefined>(
        undefined
    );
    const [isLoading, setIsLoading] = useState(true);

    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<
        "success" | "error"
    >("success");

    const [profileForm, setProfileForm] = useState<ProfileSettingsForm>({
        name: "",
        phone: "",
        address: "",
        avatar: "",
    });

    const [newAvatarFile, setNewAvatarFile] = useState<File | null>(null);
    const [avatarPreviewUrl, setAvatarPreviewUrl] = useState<string | null>(
        null
    );

    const [creditForm, setCreditForm] = useState<CreditSettingsForm>({
        resell_price_per_credit: 0,
        buy_price_per_credit: 0,
    });

    const [generalForm, setGeneralForm] = useState<GeneralSettingsForm>({
        platform_name: "",
        support_email: "",
        default_language: "en",
        maintenance_mode: false,
        maintenance_message: "", // Initialize as empty string
    });

    // Function to set notification messages
    const setNotification = (message: string, type: "success" | "error") => {
        setNotificationMessage(message);
        setNotificationType(type);
        setIsNotificationCard(true);
    };

    // Effect to fetch initial data
    useEffect(() => {
        const fetchData = async () => {
            try {
                if (!authUser || !authUser._id) {
                    // Handle case where user is not authenticated, perhaps redirect or show error
                    console.warn("User not authenticated, cannot fetch settings.");
                    setIsLoading(false);
                    return;
                }

                // Fetch user data
                const fetchedUser = await getUserById(authUser.user_id as string);
                setUser(fetchedUser);
                setProfileForm({
                    name: fetchedUser.name || "",
                    phone: fetchedUser.phone || "",
                    address: fetchedUser.address || "",
                    avatar:
                        fetchedUser.avatar ||
                        "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg",
                });
                setAvatarPreviewUrl(
                    fetchedUser.avatar ||
                    "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg"
                );

                // Fetch settings data
                const fetchedSettings = await getSettings();
                setSettings(fetchedSettings);
                setCreditForm({
                    resell_price_per_credit:
                        fetchedSettings.credit?.resell_price_per_credit || 0,
                    buy_price_per_credit:
                        fetchedSettings.credit?.buy_price_per_credit || 0,
                });
                setGeneralForm({
                    platform_name: fetchedSettings.general?.platform_name || "",
                    support_email: fetchedSettings.general?.support_email || "",
                    default_language:
                        fetchedSettings.general?.default_language || "en",
                    maintenance_mode:
                        fetchedSettings.general?.maintenance_mode || false,
                    maintenance_message:
                        fetchedSettings.general?.maintenance_message || "",
                });

                setIsLoading(false);
            } catch (error) {
                console.error("Error fetching data:", error);
                setNotification("Failed to load settings", "error");
                setIsLoading(false); // Ensure loading state is reset even on error
            }
        };

        fetchData();
    }, [authUser]); // Re-run when authUser changes

    // Handler for avatar file input change
    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            if (avatarPreviewUrl?.startsWith("blob:")) {
                URL.revokeObjectURL(avatarPreviewUrl); // Clean up previous blob URL
            }
            setNewAvatarFile(file);
            setAvatarPreviewUrl(URL.createObjectURL(file));
        }
    };

    // Handler for profile form submission
    const handleProfileSubmit = async () => {
        try {
            if (!user || !user.user_id) {
                throw new Error("User not loaded or user_id is missing");
            }

            let avatarToUpdate = profileForm.avatar;
            if (newAvatarFile) {
                const uploadResult = await uploadUserAvatar(
                    user.user_id,
                    newAvatarFile
                );
                avatarToUpdate = uploadResult.avatarUrl;
                setAvatarPreviewUrl(uploadResult.avatarUrl); // Update preview with new URL
                setNewAvatarFile(null); // Clear the file after upload
            }

            // Update user in the backend
            await updateUser(user.user_id, {
                name: profileForm.name,
                user_id: user.user_id,
                role: user.role, // Ensure role is preserved
                phone: profileForm.phone,
                address: profileForm.address,
                avatar: avatarToUpdate,
            });

            // Update local user state
            setUser((prevUser) => ({
                ...prevUser!,
                name: profileForm.name,
                phone: profileForm.phone,
                address: profileForm.address,
                avatar: avatarToUpdate,
            }));

            setNotification(t("messages.success.profile_updated"), "success");
        } catch (error) {
            console.error("Error updating profile:", error);
            setNotification(t("messages.error.profile_update_failed"), "error");
        }
    };

    // Handler for credit settings form submission
    const handleCreditSubmit = async () => {
        try {
            if (!settings || !settings._id) {
                throw new Error("Settings not loaded");
            }

            const updatedCredit = {
                resell_price_per_credit: creditForm.resell_price_per_credit,
                buy_price_per_credit: creditForm.buy_price_per_credit,
            };

            await updateCreditSettings(updatedCredit);

            // Update local settings state
            setSettings((prevSettings) => ({
                ...prevSettings!,
                credit: {
                    ...prevSettings?.credit,
                    ...updatedCredit,
                },
            }));

            setNotification(
                tDashboard("super-admin", "settings", "credit_updated"),
                "success"
            );
        } catch (error) {
            console.error("Error updating credit settings:", error);
            setNotification(
                tDashboard("super-admin", "settings", "credit_update_failed"),
                "error"
            );
        }
    };

    // Handler for general settings form submission
    const handleGeneralSubmit = async () => {
        try {
            if (!settings || !settings._id) {
                throw new Error("Settings not loaded");
            }

            const updatedGeneral = {
                platform_name: generalForm.platform_name,
                support_email: generalForm.support_email,
                default_language: generalForm.default_language,
                maintenance_mode: generalForm.maintenance_mode,
                maintenance_message: generalForm.maintenance_mode
                    ? generalForm.maintenance_message
                    : undefined,
            };

            await updateGeneralSettings(updatedGeneral);

            // Update local settings state
            setSettings((prevSettings) => ({
                ...prevSettings!,
                general: {
                    ...prevSettings?.general,
                    ...updatedGeneral,
                },
            }));

            setNotification(
                tDashboard("super-admin", "settings", "general_updated"),
                "success"
            );
        } catch (error) {
            console.error("Error updating general settings:", error);
            setNotification(
                tDashboard("super-admin", "settings", "general_update_failed"),
                "error"
            );
        }
    };

    // Value provided by the context
    const contextValue: SettingsContextType = {
        user,
        settings,
        isLoading,
        profileForm,
        setProfileForm,
        newAvatarFile,
        setNewAvatarFile,
        avatarPreviewUrl,
        setAvatarPreviewUrl,
        creditForm,
        setCreditForm,
        generalForm,
        setGeneralForm,
        handleAvatarChange,
        handleProfileSubmit,
        handleCreditSubmit,
        handleGeneralSubmit,
        notificationMessage,
        notificationType,
        isNotificationCard,
        setNotification,
        setIsNotificationCard,
    };

    return (
        <SettingsContext.Provider value={contextValue}>
            {children}
        </SettingsContext.Provider>
    );
};

// Custom hook to use the SettingsContext
export const useSettings = () => {
    const context = useContext(SettingsContext);
    if (context === undefined) {
        throw new Error("useSettings must be used within a SettingsProvider");
    }
    return context;
};