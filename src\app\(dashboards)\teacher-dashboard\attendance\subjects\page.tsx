"use client";

import React, { Suspense, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>O<PERSON>, FileCheck2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { SubjectGradesSkeleton } from "@/components/skeletons";
import { getClassById } from "@/app/services/ClassServices";
import { getSubjectsByClassId, getSubjectsBySchoolId } from "@/app/services/SubjectServices";
import { getGradeStats } from "@/app/services/GradeServices";
import { getTeacherAssignmentsByClass } from "@/app/services/TeacherAssignmentServices";
import SubjectGradeCard from "@/components/grades/SubjectGradeCard";
import { useToast, ToastContainer } from "@/components/ui/Toast";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import Link from "next/link";
import SubjectAttendanceCard from "@/components/attendance/SubjectAttendanceCard";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
interface SelectedSchool {
    school_id: string;
    school_name: string;
    access_granted_at: string;
}


interface SubjectWithStats {
    _id: string;
    name: string;
    teacherName: string;
    classAverage: number;
    gradeCount: number;
}

export default function ClassSubjectsPage() {

    const { logout, user } = useAuth();
    const { toasts, removeToast, showSuccess, showError } = useToast();
    const searchParams = useSearchParams();
    const classId = searchParams.get("classId");
    const [error, setError] = useState<string | null>(null);
    const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
    const router = useRouter();

    useEffect(() => {
        // Check if user is a teacher
        if (user && user.role !== "teacher") {
            router.push("/dashboard");
            return;
        }

        // Get selected school from localStorage
        const storedSchool = localStorage.getItem("teacher_selected_school");
        if (storedSchool) {
            try {
                const school = JSON.parse(storedSchool);
                setSelectedSchool(school);
            } catch (error) {
                console.error("Error parsing stored school:", error);
                router.push("/teacher-dashboard");
            }
        } else {
            router.push("/teacher-dashboard");
        }
    }, [user, router]);
    // State management
    const [classData, setClassData] = useState<any>(null);
    const [subjects, setSubjects] = useState<SubjectWithStats[]>([]);
    const [loading, setLoading] = useState(true);

    const schoolId: any = user?.school_ids?.[0] || user?.school_id;

    const navigation = {
        icon: FileCheck2,
        baseHref: `/teacher-dashboard/attendance/class/subjects`,
        title: classData ? `Manage Attendance - ${classData.name}` : "Manage Loading..."
    };

    // Fetch class and subjects data
    useEffect(() => {
        const fetchData = async () => {
            if (!schoolId || !classId) {
                return;
            }

            try {
                setLoading(true);

                // Fetch class data
                const classResponse = await getClassById(classId);
                setClassData(classResponse);

                // Fetch subjects for the school
                const subjectsResponse = await getSubjectsByClassId(classResponse._id);

                // Fetch teacher assignments for this class
                const assignmentsResponse = await getTeacherAssignmentsByClass(schoolId, classResponse._id);

                // Process subjects with teacher and grade statistics
                const subjectsWithStats = await Promise.all(
                    subjectsResponse
                        .filter((subject: any) => {
                            const assignment = assignmentsResponse.find(
                                (assign: any) =>
                                    assign.subject_id === subject._id &&
                                    user && assign.teacher_id === user._id // Only subjects assigned to this teacher
                            );
                            return !!assignment;
                        })
                        .map(async (subject: any) => {
                            try {
                                const assignment = assignmentsResponse.find(
                                    (assign: any) => assign.subject_id === subject._id && user && assign.teacher_id === user._id
                                );

                                const teacherName = assignment?.primary_teacher || "Non assigné";

                                const statsResponse = await getGradeStats(schoolId, {
                                    class_id: classResponse._id,
                                    subject_id: subject._id,
                                });

                                return {
                                    _id: subject._id,
                                    name: subject.name,
                                    teacherName,
                                    classAverage: statsResponse.stats.averageScore || 0,
                                    gradeCount: statsResponse.stats.totalGrades || 0,
                                };
                            } catch (error) {
                                console.error(`Error fetching stats for subject ${subject.name}:`, error);
                                const assignment = assignmentsResponse.find(
                                    (assign: any) => assign.subject_id === subject._id && user && assign.teacher_id === user._id
                                );
                                const teacherName = assignment?.primary_teacher || "Non assigné";

                                return {
                                    _id: subject._id,
                                    name: subject.name,
                                    teacherName,
                                    classAverage: 0,
                                    gradeCount: 0,
                                };
                            }
                        })
                );

                setSubjects(subjectsWithStats);
            } catch (error) {
                console.error("Error fetching data:", error);
                //showError("Error", "Failed to load class data");
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [schoolId, classId]);

    const handleSubjectClick = (subjectId: string) => {
        router.push(`/teacher-dashboard/attendance/subjects/student-list?classId=${classId}__${classData._id}&subjectId=${subjectId}`);
    };

    const handleBackClick = () => {
        router.back();
    };

    const handleSchoolChange = () => {
        localStorage.removeItem("teacher_selected_school");
        router.push("/teacher-dashboard");
    };

    if (loading) {
        return (
            <TeacherLayout
                navigation={navigation}
                selectedSchool={selectedSchool ? {
                    _id: selectedSchool.school_id,
                    name: selectedSchool.school_name
                } : null}
                onSchoolChange={handleSchoolChange}
                onLogout={logout}
            >
                <SubjectGradesSkeleton />
            </TeacherLayout>
        );
    }

    return (
        <TeacherLayout
            navigation={navigation}
            selectedSchool={selectedSchool ? {
                _id: selectedSchool.school_id,
                name: selectedSchool.school_name
            } : null}
            onSchoolChange={handleSchoolChange}
            onLogout={logout}
        >
            <div className="space-y-6">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                    <Link
                        href="/school-admin/grades"
                        className="hover:text-teal transition-colors"
                    >
                        Attendance
                    </Link>
                    <span>/</span>
                    <span className="text-foreground font-medium">
                        {classData?.name}
                    </span>
                </div>

                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={handleBackClick}
                            className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                        >
                            <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                        </motion.button>
                        <div>
                            <h1 className="text-2xl font-bold text-foreground">
                                Manage Attendance - {classData?.name}
                            </h1>
                            <p className="text-foreground/60">
                                Select a subject to manage attendance.
                            </p>
                        </div>
                    </div>
                </div>

                {/* Subjects Grid */}
                {subjects.length === 0 ? (
                    <div className="text-center py-12">
                        <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-foreground mb-2">
                            No subjects available
                        </h3>
                        <p className="text-gray-500 dark:text-gray-400">
                            Subjects will appear here once they are created and assigned to teachers
                        </p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {subjects.map((subject) => (
                            <SubjectAttendanceCard
                                key={subject._id}
                                subjectId={subject._id}
                                subjectName={subject.name}
                                teacherName={subject.teacherName}
                                onClick={() => handleSubjectClick(subject._id)}
                            />
                        ))}
                    </div>
                )}
            </div>

            {/* Toast Container */}
            <ToastContainer toasts={toasts} onClose={removeToast} />
        </TeacherLayout>
    );
}


