const mongoose = require('mongoose');

const SchoolSubscriptionSchema = new mongoose.Schema({
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true,
    unique: true // Une seule souscription par école
  },
  
  // Type de plan souscrit
  plan_type: {
    type: String,
    enum: ['basic', 'standard', 'custom'],
    required: true,
    default: 'basic'
  },
  
  // Statut de la souscription
  status: {
    type: String,
    enum: ['active', 'expired', 'suspended', 'trial'],
    required: true,
    default: 'trial'
  },
  
  // Gestion des crédits
  credits_balance: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  
  credits_purchased: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  
  credits_used: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  
  // Dates de souscription
  subscription_start: {
    type: Date,
    default: Date.now
  },
  
  subscription_end: {
    type: Date,
    required: false // Null pour les souscriptions sans limite de temps
  },
  
  // Limite mensuelle pour le chatbot (plan standard)
  monthly_chatbot_limit: {
    type: Number,
    default: 0 // 0 = pas de limite ou pas d'accès
  },
  
  chatbot_usage_current_month: {
    type: Number,
    default: 0
  },
  
  chatbot_usage_reset_date: {
    type: Date,
    default: Date.now
  },
  
  // Fonctionnalités activées
  features: [{
    type: String,
    enum: [
      'student_management',
      'class_management', 
      'attendance_tracking',
      'grade_management',
      'timetable_management',
      'chatbot_access',
      'advanced_reports',
      'priority_support',
      'custom_features'
    ]
  }],
  
  // Historique des achats de crédits
  credit_purchases: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CreditPurchase'
  }],
  
  // Paramètres de notification
  low_credit_threshold: {
    type: Number,
    default: 10 // Alerte quand il reste moins de 10 crédits
  },
  
  notifications_enabled: {
    type: Boolean,
    default: true
  },
  
  // Métadonnées
  last_credit_purchase: {
    type: Date,
    required: false
  },
  
  last_credit_usage: {
    type: Date,
    required: false
  },
  
  // Notes administratives
  admin_notes: {
    type: String,
    maxlength: 1000
  }
}, {
  timestamps: true
});

// Index pour optimiser les requêtes
SchoolSubscriptionSchema.index({ school_id: 1 });
SchoolSubscriptionSchema.index({ status: 1 });
SchoolSubscriptionSchema.index({ plan_type: 1 });
SchoolSubscriptionSchema.index({ credits_balance: 1 });

// Méthodes d'instance
SchoolSubscriptionSchema.methods.hasCredits = function(amount = 1) {
  return this.credits_balance >= amount;
};

SchoolSubscriptionSchema.methods.deductCredits = function(amount, usage_type, reference_id, used_by) {
  if (!this.hasCredits(amount)) {
    throw new Error('Insufficient credits');
  }
  
  this.credits_balance -= amount;
  this.credits_used += amount;
  this.last_credit_usage = new Date();
  
  return this.save();
};

SchoolSubscriptionSchema.methods.addCredits = function(amount) {
  this.credits_balance += amount;
  this.credits_purchased += amount;
  this.last_credit_purchase = new Date();
  
  return this.save();
};

SchoolSubscriptionSchema.methods.canUseChatbot = function() {
  return this.features.includes('chatbot_access') && 
         (this.monthly_chatbot_limit === 0 || this.chatbot_usage_current_month < this.monthly_chatbot_limit);
};

SchoolSubscriptionSchema.methods.resetMonthlyChatbotUsage = function() {
  const now = new Date();
  const resetDate = new Date(this.chatbot_usage_reset_date);
  
  // Reset si on est dans un nouveau mois
  if (now.getMonth() !== resetDate.getMonth() || now.getFullYear() !== resetDate.getFullYear()) {
    this.chatbot_usage_current_month = 0;
    this.chatbot_usage_reset_date = now;
    return this.save();
  }
  
  return Promise.resolve(this);
};

// Méthodes statiques
SchoolSubscriptionSchema.statics.getBySchoolId = function(school_id) {
  return this.findOne({ school_id }).populate('credit_purchases');
};

SchoolSubscriptionSchema.statics.createDefaultSubscription = function(school_id) {
  return this.create({
    school_id,
    plan_type: 'basic',
    status: 'trial',
    credits_balance: 50, // 50 crédits gratuits pour l'ouverture de compte
    credits_purchased: 50, // Marquer comme crédits achetés (gratuits)
    features: ['student_management', 'class_management', 'attendance_tracking', 'grade_management', 'timetable_management']
  });
};

// Middleware pre-save
SchoolSubscriptionSchema.pre('save', function(next) {
  // Vérifier la cohérence des crédits
  if (this.credits_used > this.credits_purchased) {
    return next(new Error('Credits used cannot exceed credits purchased'));
  }
  
  // Calculer le solde correct
  this.credits_balance = this.credits_purchased - this.credits_used;
  
  // Définir les fonctionnalités selon le plan
  if (this.isModified('plan_type')) {
    switch (this.plan_type) {
      case 'basic':
        this.features = ['student_management', 'class_management', 'attendance_tracking', 'grade_management', 'timetable_management'];
        break;
      case 'standard':
        this.features = ['student_management', 'class_management', 'attendance_tracking', 'grade_management', 'timetable_management', 'chatbot_access', 'advanced_reports'];
        break;
      case 'custom':
        this.features = ['student_management', 'class_management', 'attendance_tracking', 'grade_management', 'timetable_management', 'chatbot_access', 'advanced_reports', 'priority_support', 'custom_features'];
        break;
    }
  }
  
  next();
});

const SchoolSubscription = mongoose.models.SchoolSubscription || mongoose.model('SchoolSubscription', SchoolSubscriptionSchema);

module.exports = SchoolSubscription;
