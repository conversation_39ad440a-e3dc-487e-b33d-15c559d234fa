"use client";

import React from "react";
import { useTranslation } from '@/hooks/useTranslation';
import { FeeSchema } from "@/app/models/FeesModel";
import { SchoolResourceSchema } from "@/app/models/SchoolResources";

interface RegistrationSummaryProps {
    formData: any;
    feeList: FeeSchema[];
    resourceList: SchoolResourceSchema[];
}

const RegistrationSummary: React.FC<RegistrationSummaryProps> = ({
    formData,
    feeList,
    resourceList,
}) => {
    const { t } = useTranslation();
    const selectedFeesDetails = feeList.filter((fee) =>
        formData.selectedFees.includes(fee._id)
    );
    const selectedResourceDetails = resourceList.filter((res) =>
        formData.selectedResources.includes(res._id)
    );

    const totalAmount =
        selectedFeesDetails.reduce((sum, f) => sum + f.amount, 0) +
        selectedResourceDetails.reduce((sum, r) => sum + r.price, 0);

    const scholarshipPercentage = Number(formData.scholarshipPercentage) || 0;
    const applyScholarship = formData.applyScholarship;

    const scholarshipDiscount = applyScholarship
        ? (scholarshipPercentage / 100) * totalAmount
        : 0;

    const finalAmount = totalAmount - scholarshipDiscount;

    return (
        <div className="space-y-6 text-sm text-gray-800 dark:text-gray-200">
            <h3 className="text-lg font-semibold">{t('registration.summary.review_title')}</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 className="font-medium text-lg text-teal">{t('registration.summary.student_info')}</h4>
                    <p><strong>{t('common.name')}:</strong> {formData.firstName} {formData.middleName} {formData.lastName}</p>
                    <p><strong>{t('registration.fields.dob')}:</strong> {formData.dateOfBirth}</p>
                    <p><strong>{t('common.gender')}:</strong> {formData.gender}</p>
                    <p><strong>{t('registration.fields.nationality')}:</strong> {formData.nationality}</p>
                    <p><strong>{t('registration.fields.place_of_birth')}:</strong> {formData.place_of_birth}</p>
                    <p><strong>{t('common.address')}:</strong> {formData.address}</p>
                </div>

                <div>
                    <h4 className="font-medium text-lg text-teal">{t('registration.summary.guardian_info')}</h4>
                    <p><strong>{t('common.name')}:</strong> {formData.guardian_name}</p>
                    <p><strong>{t('registration.fields.relationship')}:</strong> {formData.guardian_relationship}</p>
                    <p><strong>{t('common.phone')}:</strong> {formData.guardian_phone}</p>
                    <p><strong>{t('common.email')}:</strong> {formData.guardian_email}</p>
                    <p><strong>{t('registration.fields.occupation')}:</strong> {formData.guardian_occupation}</p>
                </div>

                <div>
                    <h4 className="font-medium text-lg text-teal">{t('registration.summary.emergency_contact')}</h4>
                    <p><strong>{t('common.name')}:</strong> {formData.emergency_contact_name}</p>
                    <p><strong>{t('common.phone')}:</strong> {formData.emergency_contact_phone}</p>
                    <p><strong>{t('registration.fields.relationship')}:</strong> {formData.emergency_contact_relationship}</p>
                </div>

                <div>
                    <h4 className="font-medium text-lg text-teal">{t('registration.summary.medical_info')}</h4>
                    <p><strong>{t('registration.fields.health_condition')}:</strong> {formData.health_condtion || t('registration.summary.none_reported')}</p>
                    <p><strong>{t('registration.fields.doctor_name')}:</strong> {formData.doctors_name}</p>
                    <p><strong>{t('registration.fields.doctor_phone')}:</strong> {formData.doctors_phone}</p>
                </div>
            </div>

            <div className="mt-4">
                <h4 className="font-medium text-lg text-teal">{t('registration.summary.fees_resources')}</h4>
                <ul className="list-disc ml-5">
                    {selectedFeesDetails.map((fee) => (
                        <li key={fee._id}>{fee.fee_type}: {fee.amount.toLocaleString()} XAF</li>
                    ))}
                    {selectedResourceDetails.map((res) => (
                        <li key={res._id}>{res.name}: {res.price.toLocaleString()} XAF</li>
                    ))}
                </ul>

                <p className="mt-2">
                    <strong>{t('registration.fees.total')}:</strong> {totalAmount.toLocaleString()} XAF
                </p>

                {applyScholarship && (
                    <>
                        <p>
                            <strong>{t('registration.fees.scholarship_applied')} ({scholarshipPercentage}%):</strong> -{scholarshipDiscount.toLocaleString()} XAF
                        </p>
                        <p className="font-semibold">
                            {t('registration.summary.total_payable')}: {finalAmount.toLocaleString()} XAF
                        </p>
                    </>

                )}



                <p className="mt-2">
                    {t('registration.summary.payment_mode')}: <strong>{formData.paymentMode === 'full' ? t('registration.fees.full_payment') : t('registration.fees.pay_installments')}</strong>
                </p>

                {formData.paymentMode === "installment" && (
                    <div className="mt-2">
                        <p><strong>{t('registration.fees.installments')}:</strong> {formData.installments}</p>
                        <p><strong>{t('registration.summary.payment_dates')}:</strong> {formData.installmentDates.join(', ')}</p>
                        <p className="mt-1 font-medium">{t('registration.summary.installment_breakdown')}:</p>
                        <ul className="list-disc ml-6">
                            {Array.from({ length: formData.installments }, (_, idx) => (
                                <li key={idx}>
                                    {t('registration.fees.installment')} {idx + 1}: {(finalAmount / formData.installments).toLocaleString()} XAF
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>

            <div className="mt-4">
                <h4 className="font-medium">{t('registration.summary.consent')}</h4>
                <p>
                    {t('registration.summary.guardian_agreement')}:{" "}
                    <span className={`font-semibold ${formData.guardian_agreed_to_terms ? "text-green-600" : "text-red-600"}`}>
                        {formData.guardian_agreed_to_terms ? t('registration.summary.agreed') : t('registration.summary.not_agreed')}
                    </span>
                </p>
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {t('registration.summary.final_warning')}
            </p>
        </div>
    );
};

export default RegistrationSummary;
