'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import TranslatedNotification from './TranslatedNotification';
import { NotificationItem } from '@/hooks/useNotifications';

interface NotificationContainerProps {
  notifications: NotificationItem[];
  onRemove: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

const NotificationContainer: React.FC<NotificationContainerProps> = ({
  notifications,
  onRemove,
  position = 'top-right'
}) => {
  const getPositionClasses = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      default:
        return 'top-4 right-4';
    }
  };

  const getAnimationDirection = () => {
    switch (position) {
      case 'top-right':
      case 'bottom-right':
        return { x: 300, opacity: 0 };
      case 'top-left':
      case 'bottom-left':
        return { x: -300, opacity: 0 };
      case 'top-center':
        return { y: -100, opacity: 0 };
      case 'bottom-center':
        return { y: 100, opacity: 0 };
      default:
        return { x: 300, opacity: 0 };
    }
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className={`fixed z-50 ${getPositionClasses()}`}>
      <div className="flex flex-col space-y-2 max-w-sm w-full">
        <AnimatePresence>
          {notifications.map((notification) => (
            <motion.div
              key={notification.id}
              initial={getAnimationDirection()}
              animate={{ x: 0, y: 0, opacity: 1 }}
              exit={getAnimationDirection()}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30
              }}
              layout
            >
              <TranslatedNotification
                type={notification.type}
                messageKey={notification.messageKey}
                params={notification.params}
                onClose={() => onRemove(notification.id)}
                autoClose={notification.autoClose}
                duration={notification.duration}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default NotificationContainer;
