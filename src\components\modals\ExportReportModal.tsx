"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Calendar,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { 
  ExportOptions, 
  exportGlobalReport, 
  exportSchoolReport,
  validateExportOptions,
  getDefaultPeriodOptions,
  getFormatOptions,
  estimateReportSize
} from '@/app/services/ReportServices';
import { useTranslation } from '@/hooks/useTranslation';

interface ExportReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'global' | 'school';
  schoolId?: string;
  schoolName?: string;
}

const ExportReportModal: React.FC<ExportReportModalProps> = ({
  isOpen,
  onClose,
  type,
  schoolId,
  schoolName
}) => {
  const { t } = useTranslation();
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    period: 'month'
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [useCustomDates, setUseCustomDates] = useState(false);

  const periodOptions = getDefaultPeriodOptions();
  const formatOptions = getFormatOptions();

  const handleExport = async () => {
    try {
      setIsExporting(true);
      setExportStatus('idle');
      setErrorMessage('');

      // Valider les options
      const validationErrors = validateExportOptions(exportOptions);
      if (validationErrors.length > 0) {
        setErrorMessage(validationErrors.join(' '));
        setExportStatus('error');
        return;
      }

      // Exporter selon le type
      if (type === 'global') {
        await exportGlobalReport(exportOptions);
      } else if (type === 'school' && schoolId) {
        await exportSchoolReport(schoolId, exportOptions);
      } else {
        throw new Error(t('reports.export_modal.missing_school_id'));
      }

      setExportStatus('success');
      
      // Fermer la modal après un délai
      setTimeout(() => {
        onClose();
        setExportStatus('idle');
      }, 2000);

    } catch (error: any) {
      console.error('Export error:', error);
      setErrorMessage(error.message || t('reports.export_modal.error'));
      setExportStatus('error');
    } finally {
      setIsExporting(false);
    }
  };

  const handleOptionChange = (key: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const getModalTitle = () => {
    if (type === 'global') {
      return t('reports.export_modal.global_title');
    } else {
      return t('reports.export_modal.school_title', { schoolName: schoolName || t('reports.export_modal.school') });
    }
  };

  const getEstimatedSize = () => {
    return estimateReportSize(type, exportOptions.format);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {getModalTitle()}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Format Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                {t('reports.export_modal.format')}
              </label>
              <div className="grid grid-cols-2 gap-3">
                {formatOptions.map((format) => (
                  <button
                    key={format.value}
                    onClick={() => handleOptionChange('format', format.value)}
                    className={`p-4 border-2 rounded-lg transition-all ${
                      exportOptions.format === format.value
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    }`}
                  >
                    <div className="flex items-center justify-center mb-2">
                      {format.value === 'pdf' ? (
                        <FileText className="h-8 w-8 text-red-500" />
                      ) : (
                        <FileSpreadsheet className="h-8 w-8 text-green-500" />
                      )}
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {format.label}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {format.description}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Period Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                {t('reports.export_modal.period')}
              </label>
              <select
                value={exportOptions.period}
                onChange={(e) => handleOptionChange('period', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              >
                {periodOptions.map((period) => (
                  <option key={period.value} value={period.value}>
                    {period.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Custom Date Range */}
            <div>
              <label className="flex items-center space-x-2 mb-3">
                <input
                  type="checkbox"
                  checked={useCustomDates}
                  onChange={(e) => setUseCustomDates(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t('reports.export_modal.custom_dates')}
                </span>
              </label>
              
              {useCustomDates && (
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                      {t('reports.export_modal.start_date')}
                    </label>
                    <input
                      type="date"
                      value={exportOptions.start_date || ''}
                      onChange={(e) => handleOptionChange('start_date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                      {t('reports.export_modal.end_date')}
                    </label>
                    <input
                      type="date"
                      value={exportOptions.end_date || ''}
                      onChange={(e) => handleOptionChange('end_date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* File Size Estimate */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <Calendar className="h-4 w-4" />
                <span>{t('reports.export_modal.estimated_size')}: {getEstimatedSize()}</span>
              </div>
            </div>

            {/* Status Messages */}
            {exportStatus === 'success' && (
              <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                <CheckCircle className="h-5 w-5" />
                <span className="text-sm">{t('reports.export_modal.success_message')}</span>
              </div>
            )}

            {exportStatus === 'error' && (
              <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
                <AlertCircle className="h-5 w-5" />
                <span className="text-sm">{errorMessage}</span>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              disabled={isExporting}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
            >
              {t('common.cancel')}
            </button>
            <button
              onClick={handleExport}
              disabled={isExporting}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span>{isExporting ? t('reports.export_modal.exporting') : t('reports.export')}</span>
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ExportReportModal;
