"use client";

import * as React from "react";

interface SwitchProps extends React.InputHTMLAttributes<HTMLInputElement> {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  id?: string;
}

const Switch: React.FC<SwitchProps> = ({ checked, onCheckedChange, id, ...props }) => {
  return (
    <label
      htmlFor={id}
      className="relative inline-flex items-center cursor-pointer"
    >
      <input
        id={id}
        type="checkbox"
        checked={checked}
        onChange={(e) => onCheckedChange(e.target.checked)}
        className="sr-only"
        {...props}
      />
      <div
        className={`w-11 h-6 rounded-full transition-colors duration-300
          ${checked ? "bg-teal" : "bg-gray-300 dark:bg-gray-600"}
        `}
      >
        <div
          className={`w-5 h-5 rounded-full shadow transform transition-transform duration-300
            ${checked ? "translate-x-5" : "translate-x-1"}
            ${checked ? "bg-white" : "bg-white dark:bg-gray-200"}
        `}
        />
      </div>
    </label>
  );
};

export { Switch };
