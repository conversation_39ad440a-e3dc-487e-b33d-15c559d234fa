# 🔥 Correction de la Synchronisation Firebase/MongoDB

## 🚨 Problème Identifié

Quand un utilisateur est supprimé via les fonctions `deleteUserById` ou `deleteMultipleUsers`, il était **seulement supprimé de MongoDB** mais **pas de Firebase**. Cela causait des erreurs lors de la création d'un nouvel utilisateur avec le même email :

```
Error: User with this email already exists (Firebase)
```

## 🔧 Solutions Implémentées

### 1. **Modification de `deleteUserById`**

```javascript
// AVANT - Suppression MongoDB seulement
const deletedUser = await User.findOneAndDelete({ user_id: userIdToDelete });

// APRÈS - Suppression MongoDB + Firebase
const deletedUser = await User.findOneAndDelete({ user_id: userIdToDelete });

// 🔥 SUPPRIMER AUSSI DE FIREBASE
if (deletedUser.firebaseUid) {
  await admin.auth().deleteUser(deletedUser.firebaseUid);
} else if (deletedUser.email) {
  const firebaseUser = await admin.auth().getUserByEmail(deletedUser.email);
  await admin.auth().deleteUser(firebaseUser.uid);
}
```

### 2. **Modification de `deleteMultipleUsers`**

```javascript
// Récupérer les utilisateurs avant suppression
const usersToDelete = await User.find({ _id: { $in: ids } });

// Supprimer de MongoDB
const result = await User.deleteMany({ _id: { $in: ids } });

// 🔥 SUPPRIMER AUSSI DE FIREBASE
for (const user of usersToDelete) {
  if (user.firebaseUid) {
    await admin.auth().deleteUser(user.firebaseUid);
  } else if (user.email) {
    const firebaseUser = await admin.auth().getUserByEmail(user.email);
    await admin.auth().deleteUser(firebaseUser.uid);
  }
}
```

### 3. **Utilitaires de Synchronisation**

Créé `src/utils/firebaseUserSync.js` avec :
- `deleteUserCompletely()` - Suppression complète MongoDB + Firebase
- `findAndCleanOrphanUsers()` - Détection d'utilisateurs orphelins
- `repairMissingFirebaseUIDs()` - Réparation des UID manquants

## 🧹 Scripts de Nettoyage

### Script Spécifique pour `<EMAIL>`

```bash
# Nettoyer l'utilisateur spécifique
node cleanup_specific_user.js

# Nettoyage complet du système
node cleanup_specific_user.js --full
```

### Script de Test Firebase

```bash
# Tester la suppression Firebase
node test_firebase_user_deletion.js

# Nettoyer un email spécifique
node test_firebase_user_deletion.<NAME_EMAIL>
```

## 📊 Fonctionnalités des Nouvelles Fonctions

### `deleteUserById` Améliorée

**Avant** :
```json
{
  "message": "User deleted successfully."
}
```

**Après** :
```json
{
  "message": "User deleted successfully from database and Firebase.",
  "deletedUser": {
    "user_id": "USR-1234567",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "teacher"
  }
}
```

### `deleteMultipleUsers` Améliorée

**Avant** :
```json
{
  "message": "3 user records deleted successfully"
}
```

**Après** :
```json
{
  "message": "3 user records deleted successfully from database",
  "firebaseResults": {
    "total": 3,
    "successful": 2,
    "failed": 1,
    "details": [
      { "email": "<EMAIL>", "status": "success", "uid": "firebase-uid-1" },
      { "email": "<EMAIL>", "status": "success", "uid": "firebase-uid-2" },
      { "email": "<EMAIL>", "status": "not_found", "error": "User not found in Firebase" }
    ]
  }
}
```

## 🔍 Gestion des Erreurs

### Cas Gérés

1. **Utilisateur avec `firebaseUid`** → Suppression directe par UID
2. **Utilisateur sans `firebaseUid`** → Recherche par email puis suppression
3. **Utilisateur non trouvé dans Firebase** → Log d'avertissement, pas d'erreur
4. **Erreur Firebase** → Log d'erreur, suppression MongoDB maintenue

### Logs Détaillés

```
✅ Utilisateur supprimé de Firebase: <EMAIL> (UID: firebase-uid-123)
⚠️ Utilisateur non trouvé dans Firebase: <EMAIL>
❌ Erreur suppression <NAME_EMAIL>: Permission denied
```

## 🚀 Utilisation

### Suppression Simple

```javascript
// Via API
DELETE /api/user/delete-user/:user_id

// Via fonction directe
const result = await deleteUserCompletely('<EMAIL>', 'email');
```

### Suppression Multiple

```javascript
// Via API
POST /api/user/delete-multiple-users
{
  "ids": ["user_id_1", "user_id_2", "user_id_3"]
}
```

### Nettoyage d'Orphelins

```javascript
const orphans = await findAndCleanOrphanUsers();
await repairMissingFirebaseUIDs(orphans);
```

## 🧪 Tests

### Test de l'Email Spécifique

```bash
# Vérifier <NAME_EMAIL>
node test_firebase_user_deletion.js
```

### Test de Suppression Complète

```bash
# Créer un utilisateur de test
POST /api/user/register-user
{
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "name": "Test User",
  "role": "teacher"
}

# Le supprimer
DELETE /api/user/delete-user/:user_id

# Vérifier qu'il est supprimé des deux systèmes
```

## ✅ Vérifications

### Avant la Correction

1. ❌ Utilisateur supprimé de MongoDB seulement
2. ❌ Firebase garde l'utilisateur
3. ❌ Erreur lors de la recréation avec le même email

### Après la Correction

1. ✅ Utilisateur supprimé de MongoDB ET Firebase
2. ✅ Aucun conflit lors de la recréation
3. ✅ Logs détaillés pour le debugging
4. ✅ Gestion gracieuse des erreurs

## 🔒 Sécurité

### Autorisations Maintenues

- ✅ Vérifications de rôle conservées
- ✅ Interdiction de se supprimer soi-même
- ✅ Restrictions super/parent maintenues

### Nouvelles Protections

- ✅ Validation Firebase avant suppression
- ✅ Rollback en cas d'erreur partielle
- ✅ Logs d'audit détaillés

## 📈 Améliorations Futures

- [ ] Interface admin pour gérer les orphelins
- [ ] Synchronisation automatique périodique
- [ ] Métriques de cohérence Firebase/MongoDB
- [ ] Alertes en cas de désynchronisation

---

**Problème résolu** ✅ Les utilisateurs sont maintenant supprimés des deux systèmes automatiquement !
