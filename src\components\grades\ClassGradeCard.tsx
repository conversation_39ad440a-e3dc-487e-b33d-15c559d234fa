"use client";

import React from "react";
import { motion } from "framer-motion";
import { Users, ArrowRight } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";

interface ClassGradeCardProps {
  classId: string;
  className: string;
  classCode: string;
  studentCount: number;
  lastTerm: string;
  onClick: () => void;
}

export default function ClassGradeCard({
  classId,
  className,
  classCode,
  studentCount,
  lastTerm,
  onClick
}: ClassGradeCardProps) {
  const { t, tDashboard } = useTranslation();
  
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className="bg-widget border border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:shadow-lg transition-all duration-200"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-teal/10 dark:bg-teal/20 rounded-lg flex items-center justify-center">
            <span className="text-teal font-semibold text-lg">
              {className.charAt(0)}
            </span>
          </div>
          <div>
            <h3 className="font-semibold text-foreground text-lg">
              {className}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {classCode}
            </p>
          </div>
        </div>
        <ArrowRight className="w-5 h-5 text-gray-400" />
      </div>

      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
          <Users className="w-4 h-4" />
          <span>{t("dashboard.school-admin.pages.terms.last_term")}: {lastTerm}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {t("dashboard.school-admin.pages.dashboard.total_students")}
          </span>
          <span className="text-3xl font-bold text-teal">
            {studentCount}
          </span>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button className="text-teal hover:text-teal-dark text-sm font-medium flex items-center gap-1">
          {t("dashboard.school-admin.pages.grades.manage_grades")}
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </motion.div>
  );
}
