"use client";

import { DollarSign } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import { createSuccessNotification, createErrorNotification, NotificationState } from "@/app/types/notification";
import { FeeSchema } from "@/app/models/FeesModel";
import { getFees, getFeesBySchoolId, createFee, updateFee, deleteFee, deleteMultipleFees } from "@/app/services/FeesServices";
import FeeTypeModal from "@/components/modals/FeeTypeModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import FeeTypeSkeleton from "@/components/skeletons/FeeTypeSkeleton";
import { verifyPassword } from "@/app/services/UserServices";
import { useTranslation } from "@/hooks/useTranslation";

const BASE_URL = "/school-admin";

// Navigation will be defined inside the component to access translation

function FeesContent() {
  const { t, tDashboard } = useTranslation();
  const [fees, setFees] = useState<FeeSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedFees, setSelectedFees] = useState<FeeSchema[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [feeToEdit, setFeeToEdit] = useState<FeeSchema | null>(null);
  const [feeToDelete, setFeeToDelete] = useState<FeeSchema | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(null);
  const [clearSelection, setClearSelection] = useState(false);
  const { user } = useAuth();
  
  // Columns for the table
  const columns = [
    {
      header: tDashboard('school-admin', 'fees', 'fee_type'),
      accessor: (row: FeeSchema) => (
        <span className="font-medium">{row.fee_type}</span>
      )
    },
    {
      header: tDashboard('school-admin', 'fees', 'amount'),
      accessor: (row: FeeSchema) => (
        <span className="font-mono text-green-600 dark:text-green-400">
          {row.amount.toFixed(2)}
        </span>
      )
    },
  ];

  // Actions for the table
  const actions = [
    {
      label: t('common.edit'),
      onClick: (fee: FeeSchema) => {
        handleEditFee(fee);
      },
    },
    {
      label: t('common.delete'),
      onClick: (fee: FeeSchema) => {
        handleDeleteFee(fee);
      },
    },
  ];

  // Load fees on page load
  useEffect(() => {
    fetchFees();
  }, []);

  const fetchFees = async () => {
    try {
      setLoadingData(true);
      if (user && user.school_ids && user.school_ids.length > 0) {
        const schoolId = user.school_ids[0];
        const feesData = await getFeesBySchoolId(schoolId);
        setFees(feesData);
      } else {
        const feesData = await getFees();
        setFees(feesData);
      }
    } catch (error) {
      console.error("Error fetching fees:", error);
      setSubmitStatus(createErrorNotification(tDashboard('school-admin', 'fees', 'failed_to_fetch')));
    } finally {
      setLoadingData(false);
    }
  };

  // Handle creating new fee
  const handleCreateFee = () => {
    setFeeToEdit(null);
    setIsModalOpen(true);
  };

  // Handle editing fee
  const handleEditFee = (fee: FeeSchema) => {
    setFeeToEdit(fee);
    setIsModalOpen(true);
  };

  // Handle deleting single fee
  const handleDeleteFee = (fee: FeeSchema) => {
    setFeeToDelete(fee);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  // Handle deleting multiple fees
  const handleDeleteMultiple = (selectedIds: string[]) => {
    setDeleteType("multiple");
    setFeeToDelete(null);
    setIsDeleteModalOpen(true);
  };

  // Handle selection change
  const handleSelectionChange = (selected: FeeSchema[]) => {
    setSelectedFees(selected);
  };

  // Handle save (create or update)
  const handleSave = async (feeData: any) => {
    setIsSubmitting(true);
    try {
      if (feeToEdit) {
        // Update existing fee
        await updateFee(feeToEdit._id, feeData);
        setSubmitStatus(createSuccessNotification(tDashboard('school-admin', 'fees', 'fee_updated_successfully')));
      } else {
        // Create new fee
        await createFee(feeData);
        setSubmitStatus(createSuccessNotification(tDashboard('school-admin', 'fees', 'fee_created_successfully')));
      }

      setIsModalOpen(false);
      setFeeToEdit(null);
      await fetchFees(); // Refresh the list
    } catch (error) {
      console.error("Error saving fee:", error);
      setSubmitStatus(createErrorNotification(tDashboard('school-admin', 'fees', 'failed_to_save')));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation with password
  const handleDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    try {
      // Verify password first
      if (!user?.email) {
        throw new Error(tDashboard('school-admin', 'fees', 'user_email_not_found'));
      }

      await verifyPassword(user.email, password);

      if (deleteType === "single" && feeToDelete) {
        // Delete single fee
        await deleteFee(feeToDelete._id);
        setSubmitStatus(createSuccessNotification(tDashboard('school-admin', 'fees', 'fee_deleted_successfully')));
      } else if (deleteType === "multiple" && selectedFees.length > 0) {
        // Delete multiple fees
        const ids = selectedFees.map(fee => fee._id);
        await deleteMultipleFees(ids);
        setSubmitStatus(createSuccessNotification(t('dashboard.school-admin.fees.fees_deleted_successfully', { count: selectedFees.length })));
        setSelectedFees([]);
        setClearSelection(true);
      }

      setIsDeleteModalOpen(false);
      setFeeToDelete(null);
      await fetchFees(); // Refresh the list
    } catch (error) {
      console.error("Error deleting fee type(s):", error);
      if (error instanceof Error && error.message.includes("password")) {
        setSubmitStatus(createErrorNotification(tDashboard('school-admin', 'fees', 'invalid_password')));
      } else {
        setSubmitStatus(createErrorNotification(tDashboard('school-admin', 'fees', 'failed_to_delete')));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loadingData) {
    return <FeeTypeSkeleton />;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4 text-text">{tDashboard('school-admin', 'fees', 'fee_types_management')}</h1>

      {submitStatus && (
        <div className="mb-4">
          <NotificationCard
            type={submitStatus.type}
            title={submitStatus.title}
            message={submitStatus.message}
            onClose={() => setSubmitStatus(null)}
            isVisible={true}
          />
        </div>
      )}

      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: 'spring', stiffness: 300 }}
        onClick={handleCreateFee}
        className="mb-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
      >
        {tDashboard('school-admin', 'fees', 'add_new_fee_type')}
      </motion.button>

      <DataTableFix<FeeSchema>
        data={fees}
        columns={columns}
        actions={actions}
        defaultItemsPerPage={10}
        onSelectionChange={handleSelectionChange}
        handleDeleteMultiple={handleDeleteMultiple}
        clearSelection={clearSelection}
        onSelectionCleared={() => setClearSelection(false)}
      />

      {/* Fee Type Modal */}
      <FeeTypeModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setFeeToEdit(null);
        }}
        onSave={handleSave}
        fee={feeToEdit}
        isSubmitting={isSubmitting}
      />

      {/* Delete Confirmation Modal with Password */}
      <PasswordConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setFeeToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        title={
          deleteType === "single"
            ? tDashboard('school-admin', 'fees', 'delete_fee_type')
            : tDashboard('school-admin', 'fees', 'delete_selected_fee_types')
        }
        message={
          deleteType === "single"
            ? t('dashboard.school-admin.fees.delete_confirmation_single')
            : t('dashboard.school-admin.fees.delete_confirmation_multiple', { count: selectedFees.length })
        }
        itemName={
          deleteType === "single" && feeToDelete
            ? feeToDelete.fee_type
            : undefined
        }
        itemCount={deleteType === "multiple" ? selectedFees.length : undefined}
        type={deleteType}
      />
    </div>
  );
}

export default function Page() {
  const { logout } = useAuth();
  const { tDashboard } = useTranslation();

  const navigation = {
    icon: DollarSign,
    baseHref: `${BASE_URL}/fees`,
    title: tDashboard('school-admin', 'fees', 'title')
  };

  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <FeesContent />
      </SchoolLayout>
    </Suspense>
  );
}
