import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { SettingsSchema } from "../models/Settings";

// Get the current settings (there should only be one settings document)
export async function getSettings(): Promise<SettingsSchema> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/settings/settings`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch settings");
    }

    const settings = await response.json();
    return settings as SettingsSchema;
  } catch (error) {
    console.error("Error fetching settings:", error);
    throw new Error("Failed to fetch settings");
  }
}

// Update general settings
export async function updateGeneralSettings(data: Partial<SettingsSchema["general"]>) {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/settings/general`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to update general settings");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating general settings:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update general settings");
  }
}

export async function updateCreditSettings(
  data: Partial<SettingsSchema["credit"]>
) {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/settings/credit`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to update credit settings");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating credit settings:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update credit settings");
  }
}

