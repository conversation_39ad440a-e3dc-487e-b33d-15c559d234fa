"use client";

import React from "react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { Check } from "lucide-react";

interface CreditSettingsProps {
  creditForm: {
    credit_limit: number;
    credit_usage_alert_threshold: number;
  };
  setCreditForm: React.Dispatch<React.SetStateAction<{
    credit_limit: number;
    credit_usage_alert_threshold: number;
  }>>;
  handleCreditSubmit: () => Promise<void>;
}

const CreditSettings: React.FC<CreditSettingsProps> = ({
  creditForm,
  setCreditForm,
  handleCreditSubmit,
}) => {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-foreground mb-4">Credit Settings</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-foreground mb-1">Credit Limit</label>
          <Input
            type="number"
            value={creditForm.credit_limit}
            min={0}
            onChange={(e: { target: { value: any; }; }) =>
              setCreditForm({ ...creditForm, credit_limit: Number(e.target.value) })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder="Credit Limit"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-1">Alert Threshold (%)</label>
          <Input
            type="number"
            value={creditForm.credit_usage_alert_threshold}
            min={0}
            max={100}
            onChange={(e: { target: { value: any; }; }) =>
              setCreditForm({ ...creditForm, credit_usage_alert_threshold: Number(e.target.value) })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder="Alert Threshold"
          />
        </div>
      </div>

      <Button
        onClick={handleCreditSubmit}
        className="w-full py-3 bg-teal hover:opacity-90 text-white font-semibold rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <Check className="h-5 w-5" />
        <span>Save Credit Settings</span>
      </Button>
    </div>
  );
};

export default CreditSettings;
