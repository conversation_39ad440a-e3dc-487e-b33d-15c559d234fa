# 📧 Système de Messages de Bienvenue avec Redirection par Rôle

## 📋 Vue d'ensemble

Ce système envoie automatiquement des **messages de bienvenue personnalisés par email et SMS** lors de la création d'utilisateurs staff, avec des **boutons de redirection vers le dashboard approprié selon le rôle**.

## 🎯 Fonctionnalités

### ✅ **Messages Personnalisés par Rôle**
- **Super Admin** → Accès à toutes les fonctionnalités
- **Admin d'École** → Gestion de l'établissement  
- **Enseignant** → Outils pédagogiques + code d'accès
- **Directeur des Études** → Supervision académique
- **Économe** → Gestion financière

### ✅ **Redirection Automatique**
- **Super Admin** → `/super-admin/dashboard`
- **Admin/School Admin** → `/school-admin/dashboard`
- **Enseignant** → `/teacher-dashboard`
- **Autres rôles** → Dashboard approprié

### ✅ **Multi-canal**
- **Email** avec template HTML responsive
- **SMS** avec lien de configuration (optionnel)
- **Fallback** vers l'ancien système en cas d'échec

## 🏗️ Architecture

### 📁 Fichiers Créés/Modifiés

```
src/
├── services/
│   └── WelcomeMessageService.js     # Service principal
├── templates/
│   └── emailTemplates.js           # Templates ajoutés
├── controllers/
│   └── staffController.js          # Intégration
└── test_welcome_messages.js        # Tests
```

## 🔧 Service WelcomeMessageService

### Méthodes Principales

```javascript
// Envoi complet (email + SMS)
WelcomeMessageService.sendWelcomeMessages(userData)

// Email uniquement
WelcomeMessageService.sendWelcomeEmail(userData)

// SMS uniquement  
WelcomeMessageService.sendWelcomeSMS(userData)

// Utilitaires
WelcomeMessageService.getDashboardUrl(role)
WelcomeMessageService.getRoleDisplayName(role)
WelcomeMessageService.getWelcomeMessage(role, firstName, schoolName)
```

### Structure des Données

```javascript
const userData = {
  user: {
    _id: ObjectId,
    first_name: String,
    last_name: String, 
    email: String,
    phone: String (optionnel)
  },
  role: String,           // 'super', 'admin', 'teacher', etc.
  schoolName: String,
  resetToken: String,     // Token pour configuration mot de passe
  accessCode: String,     // Code d'accès (enseignants uniquement)
  isNewUser: Boolean
};
```

## 📧 Templates d'Email

### Templates Disponibles
- `superAdminWelcome` - Super Administrateur
- `schoolAdminWelcome` - Administrateurs d'école
- `teacherWelcome` - Enseignants (avec code d'accès)
- `staffWelcome` - Template générique (fallback)

### Caractéristiques
- **Design responsive** avec CSS intégré
- **Boutons d'action** vers dashboard et configuration
- **Informations contextuelles** selon le rôle
- **Branding Scholarify** cohérent

## 📱 Messages SMS

### Format Optimisé
- **160 caractères maximum**
- **Lien de configuration** inclus
- **Message personnalisé** selon le rôle
- **Nom de l'expéditeur** : "Scholarify"

### Exemples
```
Super Admin:
"Bienvenue Jean! Vous êtes Super Admin sur Scholarify. Configurez votre mot de passe: https://..."

Admin d'École:
"Bienvenue Marie! Vous êtes Administrateur d'École à École Les Palmiers. Configurez votre mot de passe: https://..."

Enseignant:
"Bienvenue Pierre! Votre compte Enseignant à Collège Saint-Joseph est créé. Configurez votre mot de passe: https://..."
```

## 🔄 Intégrations

### 1. Dans registerUser (userController)

```javascript
// Après création de l'utilisateur
await user.save();

// Générer token de réinitialisation
const resetToken = crypto.randomBytes(32).toString('hex');
user.password_reset_token = resetToken;
user.password_reset_expires = new Date(Date.now() + 24 * 60 * 60 * 1000);
await user.save();

// Obtenir nom de l'école
let schoolName = 'Scholarify';
if (school_ids && school_ids.length > 0) {
  const school = await School.findById(school_ids[0]);
  schoolName = school ? school.name : 'Votre École';
}

// Envoyer messages de bienvenue
const welcomeResult = await WelcomeMessageService.sendWelcomeMessages({
  user: user,
  role: role,
  schoolName: schoolName,
  resetToken: resetToken,
  accessCode: null,
  isNewUser: true
});
```

### 2. Dans createStaff (staffController) - Supprimé

L'intégration dans `createStaff` a été supprimée car elle avait déjà sa propre logique d'envoi d'emails.

## 🧪 Tests

### Scripts de Test Disponibles

#### 1. Test du Service (Simulation)
```bash
node test_welcome_messages.js
```
Teste les fonctionnalités du service sans envoi réel.

#### 2. Test de registerUser (Intégration)
```bash
node test_register_user_welcome.js
```
Teste l'enregistrement complet avec messages de bienvenue.

### Fonctionnalités Testées
- ✅ URLs de redirection par rôle
- ✅ Noms d'affichage des rôles
- ✅ Messages personnalisés
- ✅ Templates d'email
- ✅ Sujets d'email
- ✅ Messages SMS
- ✅ Gestion sans téléphone
- ✅ Enregistrement utilisateur complet
- ✅ Génération tokens de réinitialisation
- ✅ Association avec écoles

## ⚙️ Configuration

### Variables d'Environnement Requises
```env
# Email (obligatoire)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# SMS (optionnel)
VONAGE_API_KEY=your-vonage-key
VONAGE_API_SECRET=your-vonage-secret

# Frontend
FRONTEND_URL=https://your-domain.com

# Support
SUPPORT_EMAIL=<EMAIL>
```

## 🚀 Utilisation

### Enregistrement d'Utilisateur
Le système s'active automatiquement lors de :
```javascript
POST /api/user/register-user
```

### Données Requises
```javascript
{
  "name": "Jean Dupont",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "role": "admin", // super, admin, teacher, parent
  "phone": "+237123456789", // optionnel
  "address": "123 Rue Example",
  "school_ids": ["school_id_here"] // optionnel selon le rôle
}
```

### Résultat
1. **Utilisateur créé** dans la base de données et Firebase
2. **Token de réinitialisation** généré (24h)
3. **Email de bienvenue** envoyé avec bouton de redirection selon le rôle
4. **SMS de bienvenue** envoyé (si téléphone disponible)
5. **Logs détaillés** pour le debugging
6. **Réponse enrichie** avec statut des messages

## 🔍 Monitoring

### Logs Générés
```
✅ Email de bienvenue envoyé à <EMAIL> (Super Administrateur)
✅ SMS de bienvenue envoyé à +237123456789 (Super Administrateur)
📊 Résultats envoi <NAME_EMAIL>:
   Email: ✅
   SMS: ✅
```

### Métriques Suivies
- Taux de succès email par rôle
- Taux de succès SMS par rôle  
- Temps de traitement
- Erreurs et fallbacks

## 🛠️ Maintenance

### Ajout d'un Nouveau Rôle
1. Ajouter dans `getDashboardUrl()`
2. Ajouter dans `getRoleDisplayName()`
3. Ajouter dans `getWelcomeMessage()`
4. Créer template email si nécessaire
5. Tester avec le script de test

### Modification des Templates
- Templates dans `src/templates/emailTemplates.js`
- CSS intégré pour compatibilité email
- Variables disponibles via `${data.variableName}`

## 🔒 Sécurité

### Bonnes Pratiques
- **Tokens de réinitialisation** expiration 24h
- **Validation des emails** avant envoi
- **Limitation SMS** 160 caractères
- **Logs sécurisés** (pas de mots de passe)
- **Fallback robuste** en cas d'échec

## 📈 Évolutions Futures

### Améliorations Possibles
- [ ] Templates SMS HTML (RCS)
- [ ] Raccourcissement d'URLs automatique
- [ ] Personnalisation par école
- [ ] Statistiques d'ouverture email
- [ ] Notifications push mobile
- [ ] Templates multilingues

---

**Développé pour Scholarify** - Système de gestion scolaire moderne
