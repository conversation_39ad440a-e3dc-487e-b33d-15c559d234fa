"use client";

import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { ArrowLeft, Presentation } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

import { createClass, deleteClass, getClasses, deleteMultipleClasses, deleteAllClasses } from "@/app/services/ClassServices";
import { getSchools } from "@/app/services/SchoolServices";
import { createClassLevel, deleteClassLevel, getClassLevels } from "@/app/services/ClassLevels";
import BulkDeleteModal from '@/components/modals/BulkDeleteModal';
import ClassLevelsTableWithBulkActions from '../components/ClassLevelsTableWithBulkActions';

import { ClassCreateSchema, ClassSchema } from "@/app/models/ClassModel";
import { SchoolSchema } from "@/app/models/SchoolModel";
import DataTableFix from "@/components/utils/TableFix";
import { ClassLevelCreateSchema, ClassLevelSchema } from "@/app/models/ClassLevel";
import CreateLevelModal from "../components/CreateLevelModal";
import useAuth from "@/app/hooks/useAuth";
import NotificationCard from "@/components/NotificationCard";
import DeleteClassLevelModal from "../components/DeleteLevelModal";
import { verifyPassword } from "@/app/services/UserServices";
import CreateClassModal from "../components/CreateClassModal";
import DeleteClassModal from "../components/DeleteClassModal";
import { motion } from "framer-motion";
import type { NotificationType } from "@/components/NotificationCard";
import ActionButton from "@/components/ActionButton";
import { useTranslation } from '@/hooks/useTranslation';


const BASE_URL = "/super-admin";

function ManageClassesPage(): JSX.Element {
  const { t, tDashboard } = useTranslation();
  const searchParams = useSearchParams();
  const schoolId = searchParams.get("id");
  const [selectedClasses, setSelectedClasses] = useState<ClassSchema[]>([]);
  const [selectedLevels, setSelectedLevels] = useState<ClassLevelSchema[]>([]);
  const [classes, setClasses] = useState<ClassSchema[]>([]);
  const [school, setSchool] = useState<SchoolSchema | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingClasses, setLoadingClasses] = useState(false);
  const [loadingLevels, setLoadingLevels] = useState(false);
  const [classLevel, setClassLevel] = useState<ClassLevelSchema[]>([]);
  const [classToDelete, setClassToDelete] = useState<ClassSchema | null>(null);
  const [LevelToDelete, setLevelToDelete] = useState<ClassLevelSchema | null>(null);
  const [loadingData, setLoadingData] = useState(false);
  const [isLevelModalOpen, setIsLevelModalOpen] = useState(false);
  const [isClassModalOpen, setIsClassModalOpen] = useState(false);
  const [selectedClassLevelId, setSelectedClassLevelId] = useState("all");
  const [isNotificationCard, setIsNotificationCard] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState<NotificationType>("success");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<ClassLevelSchema | null>(null);
  const [editingClass, setEditingClass] = useState<ClassSchema | undefined>(undefined);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
  const { user } = useAuth();
  const router = useRouter();



  // Bulk delete modal states for classes
  const [isBulkDeleteClassModalOpen, setIsBulkDeleteClassModalOpen] = useState(false);
  const [bulkDeleteClassType, setBulkDeleteClassType] = useState<"selected" | "all">("selected");
  const [selectedClassIds, setSelectedClassIds] = useState<string[]>([]);

  // Bulk delete modal states for levels
  const [isBulkDeleteLevelModalOpen, setIsBulkDeleteLevelModalOpen] = useState(false);
  const [bulkDeleteLevelType, setBulkDeleteLevelType] = useState<"selected" | "all">("selected");
  const [selectedLevelIds, setSelectedLevelIds] = useState<string[]>([]);

  // Keys to force DataTable re-render and clear selection
  const [classTableKey, setClassTableKey] = useState(0);
  const [levelTableKey, setLevelTableKey] = useState(0);

  useEffect(() => {
    if (!schoolId) return;

    const fetchData = async () => {
      setLoading(true);
      setLoadingClasses(true);
      setLoadingLevels(true);
      try {
        const [allClasses, allSchools, allClassLevels] = await Promise.all([
          getClasses(),
          getSchools(),
          getClassLevels(),
        ]);

        const filteredClasses = allClasses.filter(
          (cls: ClassSchema) => cls.school_id === schoolId
        );
        const matchedSchool = allSchools.find((s: SchoolSchema) => s._id === schoolId) || null;

        const filteredClassLevels = allClassLevels.filter(
          (level: ClassLevelSchema) => level.school_id === schoolId
        );

        setClasses(filteredClasses);
        setSchool(matchedSchool);
        setClassLevel(filteredClassLevels);
      } catch (err) {
        console.error("Failed to fetch data", err);
      } finally {
        setLoading(false);
        setLoadingClasses(false);
        setLoadingLevels(false);
      }
    };

    fetchData();
  }, [schoolId]);

  const columns = [
    {
      header: tDashboard('super-admin', 'classes', 'class_level'),
      accessor: (row: ClassSchema) => {
        const level = classLevel.find(
          (lvl) => lvl._id === (typeof row.class_level === "object" ? row.class_level : row.class_level)
        );
        return level ? level.name : t('common.unknown');
      },
    },
    { header: tDashboard('super-admin', 'classes', 'class_name'), accessor: (row: ClassSchema) => row.name },
    { header: tDashboard('super-admin', 'classes', 'class_code'), accessor: (row: ClassSchema) => row.class_code },
  ];

  const classLevelColumns = [
    { header: tDashboard('super-admin', 'classes', 'class_level_name'), accessor: (row: ClassLevelSchema) => row.name },
  ];

  const filteredClasses =
    selectedClassLevelId === "all"
      ? classes
      : classes.filter((cls) => cls.class_level === selectedClassLevelId);

  const navigation = {
    icon: Presentation,
    baseHref: `${BASE_URL}/classes/manage/?id=${schoolId}`,
    title: school ? `${tDashboard('super-admin', 'classes', 'manage_classes_of')} ${school.name}` : tDashboard('super-admin', 'classes', 'manage_classes'),
  };

  const Class_actions = [
    {
      label: t('common.view'),
      onClick: (cls: ClassSchema) => {
        router.push(`${BASE_URL}/classes/manage/view?classId=${cls.class_id}&schoolId=${schoolId}`);
      },
    },
    {
      label: t('common.delete'),
      onClick: (cls: ClassSchema) => {
        setClassToDelete(cls);
      },
    },
  ];

  const Level_actions = [
    {
      label: t('common.delete'),
      onClick: (level: ClassLevelSchema) => {
        setLevelToDelete(level);
      },
    },
  ];

  const handleAddClass = () => {
    setEditingClass(undefined);
    setIsClassModalOpen(true);
    setSubmitStatus(null);
  };

  const handleSaveClass = async (classData: ClassSchema) => {
    setIsSubmitting(true);         // Start submitting
    setSubmitStatus(null);
    setLoadingData(true);
    try {
      // Build the new class object
      const newClass: ClassCreateSchema = {
        school_id: schoolId as string,
        class_level: classData.class_level,
        class_code: classData.class_code,
        name: classData.name,
      };

      // Make the API call
      const data = await createClass(newClass);

      if (data) {
        // Construct the class object from response (optional: you may already get it structured)
        const createdClass: ClassSchema = {
          _id: data._id,
          class_id: data.class_id,
          school_id: data.school_id,
          class_level: data.class_level,
          class_code: data.class_code,
          name: data.name,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
        };

        // Optional: Update class list in state if needed
        setClasses((prev) => [...prev, createdClass]);

        // Show success notification
        setSubmitStatus("success");
        setNotificationMessage(t('messages.success.class_created'));
        setIsNotificationCard(true);
        setNotificationType("success");

        // optional: close modal after delay
        setTimeout(() => {
          setIsClassModalOpen(false);
          setSubmitStatus(null); // reset
        }, 10000);
      }
    } catch (error) {
      console.error("Error creating class:", error);
      setSubmitStatus("failure");
      const errorMessage =
        error instanceof Error
          ? error.message
          : t('messages.error.class_creation_failed');
      setNotificationMessage(errorMessage);
      setIsNotificationCard(true);
      setNotificationType("error");
    } finally {
      setIsSubmitting(false);                     // ✅ end submitting
      setLoadingData(false);
    }
  };

  const handleSaveLevel = async (levelData: ClassLevelCreateSchema) => {
    setIsSubmitting(true);         // Start submitting
    setSubmitStatus(null);
    setLoadingData(true);
    try {
      const newLevel: ClassLevelCreateSchema = {
        name: levelData.name,
        school_id: schoolId || "",
      };

      const data = await createClassLevel(newLevel);
      if (data) {
        const createdLevel: ClassLevelSchema = {
          name: data.name,
          school_id: data.school_id,
          _id: data._id,
        };

        setClassLevel((prev) => [...prev, createdLevel]);
        setSubmitStatus("success");
        setNotificationMessage(t('messages.success.level_created'));
        setNotificationType("success");
        setIsNotificationCard(true);

        setTimeout(() => {
          setIsLevelModalOpen(false);
          setSubmitStatus(null); // reset
        }, 10000);
      }
    } catch (error) {
      console.error("Error creating class level:", error);
      setSubmitStatus("failure");                  // ✅ update failure
      const errorMessage =
        error instanceof Error
          ? error.message
          : t('messages.error.level_creation_failed');

      setNotificationMessage(errorMessage);
      setNotificationType("error");
      setIsNotificationCard(true);
    } finally {
      setIsSubmitting(false);                     // ✅ end submitting
      setLoadingData(false);
    }
  };

  const handleDeleteClass = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);
    setLoadingData(true);
    if (!classToDelete || !user) return;

    const passwordVerified = await verifyPassword(password, user.email);
    if (!passwordVerified) {
      setNotificationMessage(t('messages.error.invalid_password'));
      setNotificationType("error");
      setIsNotificationCard(true);

      // ✅ Fix: Reset loading/submitting states even when password fails
      setIsSubmitting(false);
      setLoadingData(false);
      setSubmitStatus("failure");
      setTimeout(() => {
        setClassToDelete(null); // ✅ Close delete modal properly
        setSubmitStatus(null);
      }, 10000);
      return;
    }

    try {
      // Call the delete class API here
      await deleteClass(classToDelete._id);
      setClasses((prev) => prev.filter((cls) => cls.class_id !== classToDelete.class_id));
      setSubmitStatus("success");
      setNotificationMessage(t('messages.success.class_deleted'));
      setNotificationType("success");
      setIsNotificationCard(true);

      setTimeout(() => {
        setClassToDelete(null); // ✅ Close delete modal properly
        setSubmitStatus(null);
      }, 10000);
    } catch (error) {
      console.error("Error Deleting Class :", error);

      setSubmitStatus("failure");

      const errorMessage =
        error instanceof Error
          ? error.message
          : t('messages.error.class_deletion_failed');

      setNotificationMessage(errorMessage);
      setNotificationType("error");
      setIsNotificationCard(true);

    } finally {
      setIsSubmitting(false);
      setLoadingData(false);
    }
  }

  const handleDeleteLevel = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);
    setLoadingData(true);
    if (!LevelToDelete || !user) return;

    const passwordVerified = await verifyPassword(password, user.email);
    if (!passwordVerified) {
      setNotificationMessage(t('messages.error.invalid_password'));
      setNotificationType("error");
      setIsNotificationCard(true);

      // ✅ Fix: Reset loading/submitting states even when password fails
      setIsSubmitting(false);
      setLoadingData(false);
      setSubmitStatus("failure");
      setTimeout(() => {
        setLevelToDelete(null); // ✅ Close delete modal properly
        setSubmitStatus(null);
      }, 10000);
      return;
    }

    try {
      await deleteClassLevel(LevelToDelete._id);
      setClassLevel((prevLevels) => prevLevels.filter((lvl) => lvl._id !== LevelToDelete._id));

      setSubmitStatus("success");
      setNotificationMessage(t('messages.success.level_deleted'));
      setNotificationType("success");
      setIsNotificationCard(true);

      setTimeout(() => {
        setLevelToDelete(null); // ✅ Close delete modal properly
        setSubmitStatus(null);
      }, 10000);
    } catch (error) {
      console.error("Error Deleting Class Level:", error);

      setSubmitStatus("failure");
      const errorMessage =
        error instanceof Error
          ? error.message
          : t('messages.error.level_deletion_failed');

      setNotificationMessage(errorMessage);
      setNotificationType("error");
      setIsNotificationCard(true);
    } finally {
      setIsSubmitting(false);
      setLoadingData(false);
    }
  };

  // Handle opening bulk delete modal for selected classes
  const handleDeleteMultipleClasses = async (selectedIds: string[]) => {
    if (selectedIds.length === 0) {
      alert(t('messages.validation.select_at_least_one'));
      return;
    }
    setSelectedClassIds(selectedIds);
    setBulkDeleteClassType("selected");
    setIsBulkDeleteClassModalOpen(true);
  };

  // Handle opening bulk delete modal for all classes
  const handleDeleteAllClasses = async () => {
    if (filteredClasses.length === 0) {
      alert(t('messages.validation.no_items_to_delete'));
      return;
    }
    setBulkDeleteClassType("all");
    setIsBulkDeleteClassModalOpen(true);
  };

  // Handle the actual bulk deletion of classes with password confirmation
  const handleBulkDeleteClassConfirm = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    // Verify password
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    if (!passwordVerified) {
      setNotificationMessage(t('messages.error.invalid_password'));
      setNotificationType("error");
      setIsNotificationCard(true);
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      if (bulkDeleteClassType === "all") {
        await deleteAllClasses();
        setNotificationMessage(t('messages.success.all_items_deleted', { type: t('navigation.classes') }));
        setClasses([]);
      } else {
        await deleteMultipleClasses(selectedClassIds);
        setNotificationMessage(t('messages.success.items_deleted', { count: selectedClassIds.length, type: t('navigation.classes') }));
        setClasses(prev => prev.filter(cls => !selectedClassIds.includes(cls._id)));
      }

      setNotificationType("success");
      setIsNotificationCard(true);
      setSubmitStatus("success");
      setSelectedClasses([]); // Clear selection
      setClassTableKey(prev => prev + 1); // Force table re-render to clear selection

      // Refresh data after successful deletion
      if (bulkDeleteClassType === "all") {
        setClasses([]);
      } else {
        setClasses(prev => prev.filter(cls => !selectedClassIds.includes(cls._id)));
      }

      // Close modal after success
      setTimeout(() => {
        setIsBulkDeleteClassModalOpen(false);
        setSubmitStatus(null);
      }, 2000);

    } catch (error) {
      console.error("Error in bulk deletion:", error);
      const errorMessage = error instanceof Error ? error.message : t('messages.error.bulk_delete_failed');
      setNotificationMessage(errorMessage);
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => console.log("Logged out")}>
      <button
        onClick={() => router.back()}
        className="flex text-foreground items-center space-x-2 px-4 py-2 rounded-full border border-gray-300 hover:bg-gray-100 transition"
      >
        <ArrowLeft className="w-5 h-5" />
        <span className="text-sm font-medium">{t('common.back')}</span>
      </button>
      <div className="md:py-6 flex flex-col md:flex-row gap-6">
        {schoolId && isClassModalOpen && (
          <CreateClassModal
            onClose={() => { setIsClassModalOpen(false), setSubmitStatus(null) }}
            onSave={handleSaveClass}
            schoolId={schoolId} // ✅ guaranteed to be string here
            classLevels={classLevel}
            initialData={editingClass}
            submitStatus={submitStatus}
            isSubmitting={isSubmitting}
          />
        )}

        {classToDelete && (
          <DeleteClassModal
            className={classToDelete.name}
            onClose={() => { setClassToDelete(null), setSubmitStatus(null) }}
            onDelete={handleDeleteClass}
            submitStatus={submitStatus}
            isSubmitting={isSubmitting}
          />
        )}
        {isLevelModalOpen && schoolId && (
          <CreateLevelModal
            onClose={() => { setIsLevelModalOpen(false), setSubmitStatus(null) }}
            onSave={handleSaveLevel}
            schoolId={schoolId}
            submitStatus={submitStatus}
            isSubmitting={isSubmitting}
          />
        )}

        {LevelToDelete && (
          <DeleteClassLevelModal
            levelName={LevelToDelete.name}
            onClose={() => { setLevelToDelete(null), setSubmitStatus(null) }}
            onDelete={handleDeleteLevel}
            submitStatus={submitStatus}
            isSubmitting={isSubmitting}
          />
        )}

        {/* Bulk Delete Modal for Classes */}
        {isBulkDeleteClassModalOpen && (
          <BulkDeleteModal
            isOpen={isBulkDeleteClassModalOpen}
            onClose={() => {
              setIsBulkDeleteClassModalOpen(false);
              setSubmitStatus(null);
            }}
            onConfirm={handleBulkDeleteClassConfirm}
            title={bulkDeleteClassType === "all" ? t('components.modals.delete_all_items', { type: t('navigation.classes') }) : t('components.modals.delete_selected_items', { type: t('navigation.classes') })}
            message={
              bulkDeleteClassType === "all"
                ? t('components.modals.delete_all_warning', { type: t('navigation.classes') })
                : t('components.modals.delete_selected_warning', { type: t('navigation.classes') })
            }
            itemCount={bulkDeleteClassType === "all" ? filteredClasses.length : selectedClassIds.length}
            itemType={t('navigation.classes')}
            isDeleteAll={bulkDeleteClassType === "all"}
            isSubmitting={isSubmitting}
            submitStatus={submitStatus}
            requirePassword={true}
          />
        )}

        {isNotificationCard && (
          <NotificationCard
            title={t('common.notification')}
            icon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
                <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
              </svg>
            }
            message={notificationMessage}
            onClose={() => setIsNotificationCard(false)}
            type={notificationType}
            isVisible={isNotificationCard}
            isFixed={true}
          />
        )}

        {/* Class Table */}

        <div className="w-full md:1/2">

          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-4">
            {/* <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: 'spring', stiffness: 300 }}
              onClick={handleAddClass}
              className="px-4 py-2 w-full bg-teal text-white rounded-md hover:bg-teal-600">
              Add New Class
            </motion.button> */}
            <ActionButton
              action="add"
              label={tDashboard('super-admin', 'classes', 'add_new_class')}
              onClick={handleAddClass}
            />
            <select
              value={selectedClassLevelId}
              onChange={(e) => setSelectedClassLevelId(e.target.value)}
              className="px-8 py-2 border rounded-md text-sm bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-teal w-fit"
            >
              <option value="all">{tDashboard('super-admin', 'classes', 'all_class_levels')}</option>
              {classLevel.map((level) => (
                <option key={level._id} value={level._id}>
                  {level.name}
                </option>
              ))}
            </select>
          </div>
          <DataTableFix
            key={classTableKey} // Force re-render to clear selection
            columns={columns}
            actions={Class_actions}
            data={filteredClasses}
            defaultItemsPerPage={5}
            loading={loadingClasses}
            onLoadingChange={setLoadingClasses}
            onSelectionChange={setSelectedClasses}
            enableBulkActions={true}
            handleDeleteMultiple={handleDeleteMultipleClasses}
            handleDeleteAll={handleDeleteAllClasses}
            idAccessor="_id"
          />
        </div>

        {/* Level Table */}
        <div className="w-full md:1/2">
          {/* <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: 'spring', stiffness: 300 }}
            onClick={() => { setIsLevelModalOpen(true), setSubmitStatus(null) }}
            className="px-4 mb-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 md:w-fit w-full"
          >
            Add New Level
          </motion.button> */}
          <div className="mb-4">
            <ActionButton
              action="add"
              label={tDashboard('super-admin', 'classes', 'add_new_level')}
              onClick={() => { setIsLevelModalOpen(true), setSubmitStatus(null) }}
            />
          </div>

          {/* ANCIEN CODE - DataTableFix sans bulk actions
          <DataTableFix
            columns={classLevelColumns}
            actions={Level_actions}
            data={classLevel}
            defaultItemsPerPage={5}
            loading={loadingLevels}
            onLoadingChange={setLoadingLevels}
            onSelectionChange={setSelectedLevels}
          />
          */}

          {/* NOUVEAU CODE - Avec fonctionnalités de multiple delete */}
          <ClassLevelsTableWithBulkActions
            classLevels={classLevel}
            setClassLevels={setClassLevel}
            columns={classLevelColumns}
            actions={Level_actions}
            loading={loadingLevels}
            onLoadingChange={setLoadingLevels}
            onSelectionChange={setSelectedLevels}
            onSuccess={(message) => {
              setNotificationMessage(message);
              setNotificationType("success");
              setIsNotificationCard(true);
            }}
            onError={(message) => {
              setNotificationMessage(message);
              setNotificationType("error");
              setIsNotificationCard(true);
            }}
          />
        </div>
      </div>
    </SuperLayout>
  );
}

export default function Page(): JSX.Element {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-screen"><CircularLoader size={32} color="teal" /></div>}>
      <ManageClassesPage />
    </Suspense>
  );
}
