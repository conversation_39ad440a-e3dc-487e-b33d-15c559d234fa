const express = require('express');
const settingsController = require('../controllers/SettingsController');
const { authenticate, authorize } = require('../middleware/middleware');

const router = express.Router();

// Get current settings (auth required)
router.get('/settings', settingsController.getSettings);

// Update general settings (admin only)
router.put('/general', authenticate, authorize('super-admin'), settingsController.updateGeneralSettings);

// Update credit settings (admin only)
router.put('/credit', authenticate, authorize('super-admin'), settingsController.updateCreditSettings);

module.exports = router;
