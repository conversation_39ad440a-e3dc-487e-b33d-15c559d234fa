"use client";

import React from "react";
import { User, CreditCard, <PERSON>lide<PERSON><PERSON><PERSON><PERSON><PERSON>, Settings } from "lucide-react";
import NotificationCard from "@/components/NotificationCard";
import ProfileSettings from "../../super-admin/settings/components/ProfileSettings"; // Reusing ProfileSettings
import GeneralSettings from "./components/GeneralSettings";
import PreferencesSettings from "./components/PrefenceSettings";
import CreditSettings from "./components/CreditSettings";
import { useTranslation } from "@/hooks/useTranslation";
import CircularLoader from "@/components/widgets/CircularLoader";

// Import the useSchoolAdminSettings hook
import { useSchoolAdminSettings } from "@/context/SchoolAdminSettingsContext"; // Adjust path as needed
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import useAuth from "@/app/hooks/useAuth";

const BASE_URL = "/school-admin";

// Main SchoolAdminSettingsPageContent component
const SchoolAdminSettingsPageContent: React.FC = () => {
    const { t, tDashboard } = useTranslation();

    // Consume the school admin settings context
    const {
        user,
        isLoading,
        profileForm,
        setProfileForm,
        avatarPreviewUrl,
        handleAvatarChange,
        handleProfileSubmit,
        generalForm,
        setGeneralForm,
        handleGeneralSubmit,
        preferencesForm,
        setPreferencesForm,
        handlePreferencesSubmit,
        creditForm,
        setCreditForm,
        handleCreditSubmit,
        notificationMessage,
        notificationType,
        isNotificationCard,
        setNotification,
        setIsNotificationCard,
        activeTab,
        setActiveTab,
    } = useSchoolAdminSettings();

    // The navigation object for SchoolLayout
    const navigation = {
        icon: Settings,
        baseHref: `${BASE_URL}/settings`,
        title: tDashboard('school-admin', 'settings', 'title'), // Assuming you have a translation for this
    };

    if (isLoading)
        return (
            <div className="flex items-center justify-center h-full min-h-[300px]">
                <CircularLoader />
            </div>
        );

    return (
        <>
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    icon={
                        notificationType === "success" ? (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path
                                    d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                                    stroke="#15803d"
                                    strokeWidth="1.5"
                                />
                                <path
                                    d="M7.75 11.9999L10.58 14.8299L16.25 9.16992"
                                    stroke="#15803d"
                                    strokeWidth="1.5"
                                />
                            </svg>
                        ) : (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="10" stroke="#dc2626" strokeWidth="2" />
                                <path
                                    d="M8 8L16 16M16 8L8 16"
                                    stroke="#dc2626"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                />
                            </svg>
                        )
                    }
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                />
            )}

            <div className="flex flex-col md:flex-row gap-8 p-6 w-full max-w-6xl mx-auto">
                <nav className="flex flex-col space-y-2 w-full md:w-1/4">
                    {[
                        { key: "profile", label: 'Profile', icon: <User className="inline mr-2" /> },
                        { key: "general", label: 'General', icon: <SlidersHorizontal className="inline mr-2" /> },
                        { key: "preferences", label: 'Preference', icon: <Settings className="inline mr-2" /> },
                        { key: "credit", label: 'Credit', icon: <CreditCard className="inline mr-2" /> },
                    ].map((tab) => (
                        <button
                            key={tab.key}
                            className={`w-full text-left px-4 py-3 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${
                                activeTab === tab.key
                                    ? "bg-teal text-white shadow-md"
                                    : "bg-background text-foreground hover:bg-background-darker"
                            }`}
                            onClick={() => setActiveTab(tab.key as any)}
                        >
                            {tab.icon} {tab.label}
                        </button>
                    ))}
                </nav>

                <section className="flex-1 w-full md:w-3/4 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
                    {activeTab === "profile" && (
                        <ProfileSettings
                            user={user}
                            profileForm={profileForm}
                            setProfileForm={setProfileForm}
                            avatarPreviewUrl={avatarPreviewUrl}
                            handleAvatarChange={handleAvatarChange}
                            handleProfileSubmit={handleProfileSubmit}
                        />
                    )}
                    {activeTab === "general" && (
                        <GeneralSettings
                            generalForm={generalForm}
                            setGeneralForm={setGeneralForm}
                            handleGeneralSubmit={handleGeneralSubmit}
                        />
                    )}
                    {activeTab === "preferences" && (
                        <PreferencesSettings
                            preferencesForm={preferencesForm}
                            setPreferencesForm={setPreferencesForm}
                            handlePreferencesSubmit={handlePreferencesSubmit}
                        />
                    )}
                    {activeTab === "credit" && (
                        <CreditSettings
                            creditForm={creditForm}
                            setCreditForm={setCreditForm}
                            handleCreditSubmit={handleCreditSubmit}
                        />
                    )}
                </section>
            </div>
        </>
    );
};

// Wrapper component to provide the SchoolAdminSettingsContext
// This will be the default export for the page.
export default function SchoolAdminSettingsPage() {
    const { logout } = useAuth();
    const { t } = useTranslation();

    const navigation = {
        icon: Settings,
        baseHref: `${BASE_URL}/settings`,
        title: t('navigation.settings'),
    };

    return (

            <SchoolLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
                <SchoolAdminSettingsPageContent />
            </SchoolLayout>
    );
}