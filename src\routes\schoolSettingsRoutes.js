const express = require('express');
const schoolSettingsController = require('../controllers/schoolSettingsController');
const { authenticate, authorize } = require('../middleware/middleware');

const router = express.Router();

// GET /school-settings/:school_id - Get settings for a school
router.get('/:school_id',
  authenticate,
  authorize(['admin','school_admin']),
  schoolSettingsController.getSchoolSettings
);

// POST /school-settings - Create or update (upsert) school settings
router.post('/',
  authenticate,
  authorize(['admin','school_admin']),
  schoolSettingsController.upsertSchoolSettings
);

// DELETE /school-settings/:school_id - Delete settings (optional)
router.delete('/:school_id',
  authenticate,
  authorize(['admin','school_admin']),
  schoolSettingsController.deleteSchoolSettings
);

module.exports = router;
