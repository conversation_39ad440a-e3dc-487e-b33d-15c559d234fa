#!/bin/bash

# Script de déploiement simple pour corriger les fichiers statiques
set -e

echo "🚀 Déploiement de la configuration Nginx simplifiée..."

# 1. Sauvegarder la configuration actuelle
sudo cp /etc/nginx/sites-available/scholarifyltd.com /etc/nginx/sites-available/scholarifyltd.com.backup.$(date +%Y%m%d_%H%M%S)

# 2. Copier la nouvelle configuration
sudo cp nginx-static.conf /etc/nginx/sites-available/scholarifyltd.com

# 3. Tester la configuration
echo "🧪 Test de la configuration Nginx..."
if sudo nginx -t; then
    echo "✅ Configuration Nginx valide"
    
    # 4. Recharger Nginx
    echo "🔄 Rechargement de Nginx..."
    sudo systemctl reload nginx
    
    echo "✅ Configuration déployée avec succès!"
    
    # 5. Vérifications
    echo "🔍 Vérifications finales..."
    
    # Vérifier que Next.js fonctionne
    if curl -f -s http://localhost:3000 > /dev/null; then
        echo "✅ Application Next.js accessible"
    else
        echo "❌ Application Next.js non accessible"
        echo "💡 Démarrez votre application avec: npm start ou pm2 start"
    fi
    
    # Tester le site HTTPS
    echo "🌐 Test du site HTTPS..."
    if curl -f -s -I https://scholarifyltd.com > /dev/null; then
        echo "✅ Site HTTPS accessible"
    else
        echo "⚠️ Site HTTPS non accessible - vérifiez les DNS et certificats"
    fi
    
else
    echo "❌ Erreur dans la configuration Nginx"
    echo "🔄 Restauration de la sauvegarde..."
    sudo cp /etc/nginx/sites-available/scholarifyltd.com.backup.* /etc/nginx/sites-available/scholarifyltd.com
    exit 1
fi

echo ""
echo "🎉 Déploiement terminé!"
echo ""
echo "📝 Points importants:"
echo "  ✅ Nginx laisse maintenant Next.js gérer TOUS les fichiers statiques"
echo "  ✅ Plus de problèmes de 404 pour les fonts et CSS"
echo "  ✅ Configuration simplifiée et plus robuste"
echo ""
echo "🔧 Commandes utiles:"
echo "  - Voir les logs: sudo tail -f /var/log/nginx/scholarify_*.log"
echo "  - Status de l'app: pm2 status (si vous utilisez PM2)"
echo "  - Redémarrer l'app: pm2 restart scholarify-dashboard"
