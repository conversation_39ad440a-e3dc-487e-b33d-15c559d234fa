import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { SchoolSettingsSchema } from "../models/SchoolSettings";

// Get school settings by school_id
export async function getSchoolSettings(school_id: string): Promise<SchoolSettingsSchema | null> {
  try {
    const token = getTokenFromCookie("idToken");
    if (!token) {
      console.warn("No authentication token found");
      return null;
    }

    const response = await fetch(`${BASE_API_URL}/school-settings/${school_id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("API Response Error:", {
        status: response.status,
        statusText: response.statusText,
        url: response.url,
      });

      if (response.status === 401) {
        console.warn("Unauthorized access - user may need to log in");
        return null;
      }

      throw new Error(`Failed to fetch school settings: ${response.status} ${response.statusText}`);
    }

    const settings = await response.json();

    // Basic check if object looks valid (can enhance validation here)
    if (!settings || typeof settings !== "object") {
      console.error("Invalid school settings data received:", settings);
      return null;
    }

    return settings as SchoolSettingsSchema;
  } catch (error) {
    console.error("Error fetching school settings:", error);

    if (error instanceof TypeError && error.message.includes("fetch")) {
      console.error("Network error - API might be unreachable");
      return null;
    }

    return null;
  }
}

// Create or update (upsert) school settings
export async function upsertSchoolSettings(settings: Partial<SchoolSettingsSchema> & { school_id: string }): Promise<SchoolSettingsSchema | null> {
  try {
    const token = getTokenFromCookie("idToken");
    if (!token) {
      console.warn("No authentication token found");
      return null;
    }

    const response = await fetch(`${BASE_API_URL}/school-settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(settings),
    });

    if (!response.ok) {
      console.error("API Response Error:", {
        status: response.status,
        statusText: response.statusText,
        url: response.url,
      });

      if (response.status === 401) {
        console.warn("Unauthorized access - user may need to log in");
        return null;
      }

      throw new Error(`Failed to upsert school settings: ${response.status} ${response.statusText}`);
    }

    const updatedSettings = await response.json();
    return updatedSettings as SchoolSettingsSchema;
  } catch (error) {
    console.error("Error upserting school settings:", error);

    if (error instanceof TypeError && error.message.includes("fetch")) {
      console.error("Network error - API might be unreachable");
      return null;
    }

    return null;
  }
}

// Delete school settings by school_id
export async function deleteSchoolSettings(school_id: string): Promise<boolean> {
  try {
    const token = getTokenFromCookie("idToken");
    if (!token) {
      console.warn("No authentication token found");
      return false;
    }

    const response = await fetch(`${BASE_API_URL}/school-settings/${school_id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("API Response Error:", {
        status: response.status,
        statusText: response.statusText,
        url: response.url,
      });

      if (response.status === 401) {
        console.warn("Unauthorized access - user may need to log in");
        return false;
      }

      throw new Error(`Failed to delete school settings: ${response.status} ${response.statusText}`);
    }

    return true;
  } catch (error) {
    console.error("Error deleting school settings:", error);

    if (error instanceof TypeError && error.message.includes("fetch")) {
      console.error("Network error - API might be unreachable");
      return false;
    }

    return false;
  }
}
