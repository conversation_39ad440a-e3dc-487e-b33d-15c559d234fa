"use client";

import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { Check, ChevronDown } from "lucide-react";

interface GeneralSettingsProps {
  generalForm: {
    language: string;
    timezone: string;
    currency: string;
  };
  setGeneralForm: React.Dispatch<React.SetStateAction<{
    language: string;
    timezone: string;
    currency: string;
  }>>;
  handleGeneralSubmit: () => Promise<void>;
}

const languages = [
  { value: "en", label: "English" },
  { value: "es", label: "Spanish" },
  { value: "fr", label: "French" },
];

const timezones = [
  { value: "WAT", label: "WAT (West Africa Time)" },
  { value: "UTC", label: "UTC" },
  { value: "EST", label: "EST (Eastern Standard Time)" },
  { value: "PST", label: "PST (Pacific Standard Time)" },
];

const currencies = [
  { value: "XAF", label: "XAF (CFA Franc BEAC)" },
  { value: "USD", label: "USD ($)" },
  { value: "EUR", label: "EUR (€)" },
  { value: "GBP", label: "GBP (£)" },
];

const GeneralSettings: React.FC<GeneralSettingsProps> = ({
  generalForm,
  setGeneralForm,
  handleGeneralSubmit,
}) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const handleDropdownToggle = (dropdownName: string) => {
    setOpenDropdown(openDropdown === dropdownName ? null : dropdownName);
  };

  const handleSelect = (name: "language" | "timezone" | "currency", value: string) => {
    setGeneralForm({ ...generalForm, [name]: value });
    setOpenDropdown(null);
  };

  useEffect(() => {
    // Optional: Close dropdowns when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-container')) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const renderDropdown = (name: "language" | "timezone" | "currency", items: { value: string; label: string; }[]) => (
    <div className="relative dropdown-container">
      <button
        type="button"
        onClick={() => handleDropdownToggle(name)}
        className="w-full text-left bg-background border border-gray-300 rounded-md py-2 px-3 flex justify-between items-center focus:ring-teal focus:border-teal"
      >
        <span>{items.find(item => item.value === generalForm[name])?.label || `Select a ${name}`}</span>
        <ChevronDown className={`h-4 w-4 transition-transform ${openDropdown === name ? 'rotate-180' : ''}`} />
      </button>
      {openDropdown === name && (
        <ul className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {items.map((item) => (
            <li
              key={item.value}
              onClick={() => handleSelect(name, item.value)}
              className="py-2 px-3 hover:bg-gray-100 cursor-pointer"
            >
              {item.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-foreground mb-4">General Settings</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-foreground mb-1">Language</label>
          {renderDropdown("language", languages)}
        </div>
        <div>
          <label className="block text-sm font-medium text-foreground mb-1">Timezone</label>
          {renderDropdown("timezone", timezones)}
        </div>
        <div>
          <label className="block text-sm font-medium text-foreground mb-1">Currency</label>
          {renderDropdown("currency", currencies)}
        </div>
      </div>

      <Button
        onClick={handleGeneralSubmit}
        className="w-full py-3 bg-teal hover:opacity-90 text-white font-semibold rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <Check className="h-5 w-5" />
        <span>Save General Settings</span>
      </Button>
    </div>
  );
};

export default GeneralSettings;