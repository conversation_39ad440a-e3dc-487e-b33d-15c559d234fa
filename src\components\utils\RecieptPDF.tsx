import {
  Page,
  Text,
  View,
  Document,
  StyleSheet,
  Font,
  Image,
} from '@react-pdf/renderer';

// Font registration
Font.register({
  family: 'Roboto',
  fonts: [
    { src: '/fonts/Roboto-Regular.ttf', fontWeight: 'normal' },
    { src: '/fonts/Roboto-Italic.ttf', fontWeight: 'normal', fontStyle: 'italic' },
    { src: '/fonts/Roboto-Bold.ttf', fontWeight: 'bold' },
  ],
});

const styles = StyleSheet.create({
  page: {
    fontFamily: 'Roboto',
    fontSize: 10,
    padding: 30,
    backgroundColor: '#FFFFFF', // Clean white background
    color: '#000000', // Black for all text
  },
  header: {
    textAlign: 'center',
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 10,
  },
  logo: {
    width: '15%',
    height: 40,
    marginBottom: 10,
    alignSelf: 'center',
  },
  receiptInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    fontSize: 10,
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontWeight: 'bold',
    fontSize: 12,
    marginBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#CCCCCC',
    paddingBottom: 4,
  },
  studentDetails: {
    fontSize: 10,
    lineHeight: 1.4,
  },
  table: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    marginTop: 8,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderBottomWidth: 1,
    borderBottomColor: '#CCCCCC',
    paddingVertical: 6,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    paddingVertical: 6,
  },
  lastRow: {
    borderBottomWidth: 0,
  },
  cellIndex: {
    width: '10%',
    paddingLeft: 10,
  },
  cellDesc: {
    width: '60%',
    paddingLeft: 12,
  },
  cellAmount: {
    width: '30%',
    textAlign: 'right',
    paddingRight: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
    fontSize: 10,
  },
  summaryTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    marginTop: 6,
    borderTopWidth: 1,
    borderTopColor: '#000000',
    fontWeight: 'bold',
    fontSize: 12,
  },
  installmentBox: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 4,
    padding: 10,
    marginTop: 10,
  },
  installmentLine: {
    fontSize: 10,
    marginBottom: 4,
  },
  paidStamp: {
    position: 'absolute',
    top: '40%',
    left: '20%',
    fontSize: 60,
    color: '#000000',
    opacity: 0.1,
    transform: 'rotate(-20deg)',
    fontWeight: 'bold',
  },
  note: {
    fontSize: 9,
    fontStyle: 'italic',
    color: '#666666',
    marginTop: 20,
    textAlign: 'center',
  },
  footer: {
    marginTop: 5,
    fontSize: 9,
    color: '#999999',
    textAlign: 'center',
  },
  poweredByContainer: {
    marginTop: 5,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  poweredByText: {
    fontSize: 9,
    color: '#999999',
    marginRight: 6,
  },
  poweredByLogo: {
    width: 60,
    height: 30,
  },
  // New styles for the payment history table
  historyTable: {
    marginTop: 8,
  },
  historyRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    paddingVertical: 4,
  },
  historyHeader: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderBottomWidth: 1,
    borderBottomColor: '#CCCCCC',
    paddingVertical: 6,
  },
  historyCell: {
    flex: 1,
    textAlign: 'center',
  },
  historyCellPaid: {
    flex: 1,
    textAlign: 'center',
    color: '#00A300', // Green for paid
    fontWeight: 'bold',
  },
  historyCellUpcoming: {
    flex: 1,
    textAlign: 'center',
    color: '#FF8C00', // Orange for upcoming
    fontWeight: 'bold',
  },
  historyCellDate: {
    flex: 2,
    textAlign: 'center',
  },
});

type Student = {
  student_id: string;
  first_name: string;
  last_name: string;
  class_level?: string;
  class_id?: string;
};

type School ={
  name: string;
  logoUrl?: string;
  email: string;
  address?: string;
  website?: string;
  phone_number?: string;
}

type PaymentItem = {
  description: string;
  amount: number;
};

type ReceiptPDFProps = {
  student: Student;
  school: School;
  paymentItems: PaymentItem[];
  receiptId: string;
  date: string | number | Date;
  taxRate?: number;
  applyScholarship?: boolean;
  scholarshipPercentage?: number;
  installments?: number;
  installmentDates?: string[];
  paidInstallmentNumber?: number;
};

const ReceiptPDF: React.FC<ReceiptPDFProps> = ({
  student,
  school,
  paymentItems,
  receiptId,
  date,
  taxRate = 0,
  applyScholarship = false,
  scholarshipPercentage = 0,
  installments,
  installmentDates,
  paidInstallmentNumber = 1,
}) => {
  const subTotal = paymentItems.reduce((sum, item) => sum + item.amount, 0);
  const taxAmount = subTotal * taxRate;
  const scholarshipDiscount = applyScholarship ? (scholarshipPercentage / 100) * subTotal : 0;
  const totalAfterScholarship = subTotal - scholarshipDiscount;
  const total = totalAfterScholarship + taxAmount;

  const isInstallment = installments && installments > 1;
  const amountPerInstallment = isInstallment ? total / installments : total;
  const paidInstallment = paidInstallmentNumber;
  const amountPaid = amountPerInstallment;
  const totalPaidSoFar = amountPerInstallment * paidInstallment;
  const remainingBalance = total - totalPaidSoFar;
  const statusStamp = isInstallment ? (paidInstallment >= installments ? 'FULLY PAID' : 'PARTIALLY PAID') : 'PAID';

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.paidStamp}>{statusStamp}</Text>
        
        {/* Logo and Header */}
        <View style={{ alignItems: 'center', marginBottom: 10 }}>
          {school.logoUrl && (
            <Image
              style={styles.logo}
              src={school.logoUrl}
            />
          )}
          <Text style={styles.header}>SCHOOL FEE RECEIPT</Text>
        </View>

        {/* Info */}
        <View style={styles.receiptInfoRow}>
          <Text>Receipt No: {receiptId}</Text>
          <Text>Initial Payment Date: {new Date(date).toLocaleDateString()}</Text>
        </View>

        {/* Student Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Student Details</Text>
          <Text style={styles.studentDetails}>
            ID: {student.student_id}
            {'\n'}
            Name: {student.first_name} {student.last_name}
            {'\n'}
            Grade/Class: {student.class_level || student.class_id}
            {'\n'}
            School: {school.name}
          </Text>
        </View>

        {/* Breakdown */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Breakdown</Text>
          <View style={styles.table}>
            <View style={styles.tableHeader}>
              <Text style={styles.cellIndex}>#</Text>
              <Text style={styles.cellDesc}>Description</Text>
              <Text style={styles.cellAmount}>Amount (XAF)</Text>
            </View>
            {paymentItems.map((item, index) => (
              <View
                key={index}
                style={[
                  styles.tableRow,
                  ...(index === paymentItems.length - 1 ? [styles.lastRow] : []),
                ]}
              >
                <Text style={styles.cellIndex}>{String(index + 1).padStart(2, '0')}</Text>
                <Text style={styles.cellDesc}>{item.description}</Text>
                <Text style={styles.cellAmount}>{(item.amount ?? 0).toFixed(2)}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Summary Section */}
        <View style={styles.section}>
          <View style={styles.summaryRow}>
            <Text>Subtotal:</Text>
            <Text>XAF {subTotal.toFixed(2)}</Text>
          </View>
          {applyScholarship && (
            <View style={styles.summaryRow}>
              <Text>Scholarship ({scholarshipPercentage}%):</Text>
              <Text>- XAF {scholarshipDiscount.toFixed(2)}</Text>
            </View>
          )}
          {taxRate > 0 && (
            <View style={styles.summaryRow}>
              <Text>Tax Rate:</Text>
              <Text>{(taxRate * 100).toFixed(0)}%</Text>
            </View>
          )}
          {taxRate > 0 && (
            <View style={styles.summaryRow}>
              <Text>Tax Amount:</Text>
              <Text>XAF {taxAmount.toFixed(2)}</Text>
            </View>
          )}
          {!isInstallment && (
            <View style={styles.summaryTotal}>
              <Text>Total Payable:</Text>
              <Text>XAF {total.toFixed(2)}</Text>
            </View>
          )}
        </View>

        {/* Installment Plan and Paid Installment Info */}
        {isInstallment && installmentDates && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Installment Payment Details</Text>
            <View style={styles.installmentBox}>
              <Text style={styles.installmentLine}>Total Payable: XAF {total.toFixed(2)}</Text>
              <Text style={styles.installmentLine}>Installments: {installments}</Text>
              <Text style={styles.installmentLine}>Amount Per Installment: XAF {amountPerInstallment.toFixed(2)}</Text>
              <Text style={styles.installmentLine}>Total Paid So Far: XAF {totalPaidSoFar.toFixed(2)}</Text>
              <Text style={styles.installmentLine}>Remaining Balance: XAF {remainingBalance.toFixed(2)}</Text>
            </View>
          </View>
        )}

        {/* New Payment History Section */}
        {isInstallment && installmentDates && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Payment History</Text>
            <View style={styles.historyTable}>
              <View style={styles.historyHeader}>
                <Text style={styles.historyCell}>#</Text>
                <Text style={styles.historyCellDate}>Due Date</Text>
                <Text style={styles.historyCell}>Status</Text>
                <Text style={styles.historyCell}>Amount</Text>
              </View>
              {installmentDates.map((installmentDate, index) => {
                const isPaid = index + 1 <= paidInstallment;
                return (
                  <View key={index} style={styles.historyRow}>
                    <Text style={styles.historyCell}>{index + 1}</Text>
                    <Text style={styles.historyCellDate}>{new Date(installmentDate).toLocaleDateString()}</Text>
                    <Text style={isPaid ? styles.historyCellPaid : styles.historyCellUpcoming}>
                      {isPaid ? 'PAID' : 'UPCOMING'}
                    </Text>
                    <Text style={styles.historyCell}>XAF {amountPerInstallment.toFixed(2)}</Text>
                  </View>
                );
              })}
            </View>
          </View>
        )}

        {/* Footer Notes */}
        <View style={styles.section}>
          <Text style={styles.note}>
            Note: Keep this receipt for future reference. Thank you for your payment!
          </Text>
          <Text style={styles.footer}>
          {school.name} | Contact: {school.email} | {school.phone_number}
        </Text>
        </View>
        <View style={styles.poweredByContainer}>
          <Text style={styles.poweredByText}>Powered by</Text>
          <Image style={styles.poweredByLogo} src="/assets/logo.png" />
        </View>
      </Page>
    </Document>
  );
};

export default ReceiptPDF;