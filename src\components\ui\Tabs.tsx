"use client";

import React, { useState } from "react";

export const Tabs = ({ children, defaultValue, className = "" }: any) => {
  const [activeTab, setActiveTab] = useState(defaultValue);

  const childrenWithProps = React.Children.map(children, (child: any) => {
    if (!React.isValidElement(child)) return child;

    return React.cloneElement(child as React.ReactElement<any>, {
      activeTab,
      setActiveTab,
    });
  });

  return <div className={className}>{childrenWithProps}</div>;
};

export const TabsList = ({ children, className = "", activeTab, setActiveTab }: any) => {
  const childrenWithProps = React.Children.map(children, (child: any) =>
    React.cloneElement(child, { activeTab, setActiveTab })
  );
  return <div className={`${className} flex gap-2 border-b`}>{childrenWithProps}</div>;
};

export const TabsTrigger = ({ value, children, activeTab, setActiveTab }: any) => {
  const isActive = activeTab === value;
  return (
    <button
      className={`py-2 px-4 rounded-t-md border-b-2 ${
        isActive ? "border-blue-500 text-blue-600" : "border-transparent text-gray-500"
      }`}
      onClick={() => setActiveTab(value)}
    >
      {children}
    </button>
  );
};

export const TabsContent = ({ value, activeTab, children }: any) => {
  if (activeTab !== value) return null;
  return <div className="mt-4">{children}</div>;
};
