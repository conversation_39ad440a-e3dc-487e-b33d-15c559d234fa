"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Check, X} from 'lucide-react';
import { SubscriptionPlanSchema } from '@/app/models/SchoolSubscriptionModel';
import { getSubscriptionPlans } from '@/app/services/SubscriptionServices';
import PricingCard from '@/components/pricing/PricingCard';
import PricingCalculator from '@/components/pricing/PricingCalculator';
import PricingFAQ from '@/components/pricing/PricingFAQ';
import PlanSelectionModal from '@/components/pricing/PlanSelectionModal';
import CircularLoader from '@/components/widgets/CircularLoader';

export default function PricingPage() {
  const [plans, setPlans] = useState<SubscriptionPlanSchema[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<string>('standard');
  const [showPlanModal, setShowPlanModal] = useState(false);
  const [selectedPlanForModal, setSelectedPlanForModal] = useState<SubscriptionPlanSchema | null>(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await getSubscriptionPlans();
      setPlans(response);
    } catch (err) {
      setError('Erreur lors du chargement des plans');
      console.error('Error fetching plans:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = (plan: SubscriptionPlanSchema) => {
    setSelectedPlanForModal(plan);
    setShowPlanModal(true);
  };

  const handleModalSuccess = () => {
    // Refresh plans or show success message
    fetchPlans();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <CircularLoader size={48} color="teal" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Erreur</h2>
          <p className="text-gray-600 dark:text-gray-400">{error}</p>
          <button
            onClick={fetchPlans}
            className="mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg transition-colors"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  // Ensure plans is always an array
  const safePlans = Array.isArray(plans) ? plans : [];

  // Debug: afficher les plans chargés
  console.log('Plans loaded:', plans);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="pt-20 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Choisissez votre plan
              <span className="text-blue-600"> Scholarify</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Des solutions flexibles pour moderniser la gestion de votre établissement scolaire. 
              Payez uniquement pour ce que vous utilisez avec notre système de crédits.
            </p>
            
            {/* Pricing Toggle */}
            <div className="flex items-center justify-center mb-12">
              <div className="bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setSelectedPlan('basic')}
                  className={`px-6 py-2 rounded-md transition-all ${
                    selectedPlan === 'basic' 
                      ? 'bg-white text-blue-600 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Mensuel
                </button>
                <button
                  onClick={() => setSelectedPlan('standard')}
                  className={`px-6 py-2 rounded-md transition-all ${
                    selectedPlan === 'standard' 
                      ? 'bg-white text-blue-600 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Annuel
                  <span className="ml-2 px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full">
                    -20%
                  </span>
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {safePlans.map((plan, index) => (
              <motion.div
                key={plan._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <PricingCard plan={plan} onPlanSelect={handlePlanSelect} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Comparison */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Comparaison détaillée des fonctionnalités
            </h2>
            <p className="text-lg text-gray-600">
              Découvrez ce qui est inclus dans chaque plan
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-4 px-6 font-semibold text-gray-900">
                    Fonctionnalités
                  </th>
                  {safePlans.map(plan => (
                    <th key={plan._id} className="text-center py-4 px-6">
                      <div className="font-semibold text-gray-900">{plan.display_name}</div>
                      <div className="text-sm text-gray-500 mt-1">
                        {(plan.price_per_credit ?? 0) > 0 ? `${plan.price_per_credit ?? 0} FCFA/crédit` : 'Sur mesure'}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {/* Render feature comparison rows */}
                {getFeatureComparison(safePlans).map((feature, index) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="py-4 px-6 font-medium text-gray-900">
                      {feature.name}
                    </td>
                    {feature.availability.map((available, planIndex) => (
                      <td key={planIndex} className="py-4 px-6 text-center">
                        {available ? (
                          <Check className="h-5 w-5 text-green-500 mx-auto" />
                        ) : (
                          <X className="h-5 w-5 text-gray-300 mx-auto" />
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* Credit Calculator */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Calculateur de crédits
            </h2>
            <p className="text-lg text-gray-600">
              Estimez vos besoins en crédits selon votre utilisation
            </p>
          </div>
          <PricingCalculator plans={safePlans} />
        </div>
      </section>

      {/* FAQ */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Questions fréquentes
            </h2>
            <p className="text-lg text-gray-600">
              Tout ce que vous devez savoir sur nos plans et tarifs
            </p>
          </div>
          <PricingFAQ />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-blue-600">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Prêt à moderniser votre école ?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Commencez avec 5 crédits gratuits et découvrez la puissance de Scholarify
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
                Commencer gratuitement
              </button>
              <button className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors">
                Demander une démo
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Plan Selection Modal */}
      {showPlanModal && selectedPlanForModal && (
        <PlanSelectionModal
          isOpen={showPlanModal}
          onClose={() => setShowPlanModal(false)}
          selectedPlan={selectedPlanForModal}
          onSuccess={handleModalSuccess}
        />
      )}
    </div>
  );
}

// Helper function to generate feature comparison data
function getFeatureComparison(plans: SubscriptionPlanSchema[]) {
  const allFeatures = [
    'Gestion des étudiants',
    'Gestion des classes',
    'Suivi des présences',
    'Gestion des notes',
    'Emplois du temps',
    'Chatbot IA',
    'Rapports avancés',
    'Support prioritaire',
    'Fonctionnalités personnalisées'
  ];

  // Fonctionnalités de base (incluses dans tous les plans)
  const basicFeatures = [
    'Gestion des étudiants',
    'Gestion des classes',
    'Suivi des présences',
    'Gestion des notes',
    'Emplois du temps'
  ];

  return allFeatures.map(featureName => ({
    name: featureName,
    availability: plans.map(plan => {
      // Vérifier si c'est une fonctionnalité de base
      const isBasicFeature = basicFeatures.includes(featureName);

      // Pour les plans Standard et Custom, inclure automatiquement les fonctionnalités de base
      if (isBasicFeature && (plan.plan_name === 'standard' || plan.plan_name === 'custom')) {
        return true;
      }

      // Vérifier dans les features et benefits du plan
      return plan.features.some(feature =>
        feature.name.toLowerCase().includes(featureName.toLowerCase()) && feature.enabled
      ) || plan.benefits.some(benefit =>
        benefit.toLowerCase().includes(featureName.toLowerCase())
      ) || (plan.plan_name === 'standard' && featureName === 'Chatbot IA') ||
         (plan.plan_name === 'standard' && featureName === 'Rapports avancés') ||
         (plan.plan_name === 'custom' && featureName === 'Chatbot IA') ||
         (plan.plan_name === 'custom' && featureName === 'Rapports avancés') ||
         (plan.plan_name === 'custom' && featureName === 'Support prioritaire') ||
         (plan.plan_name === 'custom' && featureName === 'Fonctionnalités personnalisées');
    })
  }));
}
