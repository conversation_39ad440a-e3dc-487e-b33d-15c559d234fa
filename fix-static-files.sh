#!/bin/bash

# Script pour diagnostiquer et corriger les problèmes de fichiers statiques
set -e

PROJECT_DIR="/var/www/scholarifyltd.com"
NGINX_USER="www-data"

echo "🔍 Diagnostic complet pour Scholarify Dashboard..."

# 1. Vérifier que l'application Next.js fonctionne
echo "🚀 Vérification de l'application Next.js..."

if curl -f -s http://localhost:3000 > /dev/null; then
    echo "✅ Application Next.js accessible sur localhost:3000"

    # Tester un fichier statique spécifique
    echo "🧪 Test des fichiers statiques via Next.js..."
    if curl -f -s "http://localhost:3000/_next/static/media/e4af272ccee01ff0-s.p.woff2" > /dev/null; then
        echo "✅ Fichier font accessible via Next.js"
    else
        echo "❌ Fichier font non accessible via Next.js"
        echo "💡 Vérifiez que le build Next.js est complet"
    fi
else
    echo "❌ Application Next.js non accessible sur localhost:3000"
    echo "💡 Actions à effectuer:"
    echo "   1. Aller dans le répertoire du projet: cd $PROJECT_DIR"
    echo "   2. Installer les dépendances: npm install"
    echo "   3. Builder l'application: npm run build"
    echo "   4. Démarrer l'application: npm start"
    echo ""
    echo "🔍 Vérification des processus Node.js en cours:"
    ps aux | grep node | grep -v grep || echo "Aucun processus Node.js trouvé"
    exit 1
fi

# 2. Vérifier que le répertoire du projet existe et a la bonne structure
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ Le répertoire $PROJECT_DIR n'existe pas"
    echo "📁 Création du répertoire..."
    sudo mkdir -p $PROJECT_DIR
fi

echo "📂 Structure du projet:"
ls -la "$PROJECT_DIR" | head -15

# 3. Corriger les permissions
echo "🔧 Correction des permissions..."
sudo chown -R $NGINX_USER:$NGINX_USER $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR

# Permissions spéciales pour les fichiers statiques
if [ -d "$PROJECT_DIR/.next" ]; then
    sudo chmod -R 755 "$PROJECT_DIR/.next"
fi

if [ -d "$PROJECT_DIR/public" ]; then
    sudo chmod -R 755 "$PROJECT_DIR/public"
fi

# 4. Vérifier les types MIME dans Nginx
echo "🔍 Vérification des types MIME..."
if ! grep -q "application/javascript" /etc/nginx/mime.types; then
    echo "⚠️ Types MIME manquants détectés"
fi

# 5. Tester quelques fichiers statiques courants
echo "🧪 Test des fichiers statiques..."

# Fonction pour tester un fichier
test_file() {
    local file_path="$1"
    local full_path="$PROJECT_DIR/$file_path"
    
    if [ -f "$full_path" ]; then
        echo "✅ $file_path existe"
        ls -la "$full_path"
    else
        echo "❌ $file_path n'existe pas"
    fi
}

# Tester quelques fichiers courants
test_file "public/favicon.ico"
test_file "public/manifest.json"

# 6. Créer des fichiers de test si nécessaire
echo "📝 Création de fichiers de test..."

# Créer un favicon de test si absent
if [ ! -f "$PROJECT_DIR/public/favicon.ico" ]; then
    echo "📄 Création d'un favicon de test..."
    sudo touch "$PROJECT_DIR/public/favicon.ico"
    sudo chown $NGINX_USER:$NGINX_USER "$PROJECT_DIR/public/favicon.ico"
fi

# Créer un robots.txt de test
if [ ! -f "$PROJECT_DIR/public/robots.txt" ]; then
    echo "🤖 Création d'un robots.txt..."
    echo "User-agent: *
Allow: /" | sudo tee "$PROJECT_DIR/public/robots.txt" > /dev/null
    sudo chown $NGINX_USER:$NGINX_USER "$PROJECT_DIR/public/robots.txt"
fi

# 7. Recharger Nginx
echo "🔄 Rechargement de Nginx..."
sudo nginx -t && sudo systemctl reload nginx

# 8. Tests de connectivité
echo "🌐 Test de connectivité..."

# Tester si l'application répond
if curl -f -s http://localhost:3000 > /dev/null; then
    echo "✅ Application Next.js accessible sur localhost:3000"
else
    echo "❌ Application Next.js non accessible sur localhost:3000"
    echo "💡 Assurez-vous que votre application Next.js est démarrée"
fi

# 9. Afficher les logs récents
echo "📋 Logs Nginx récents:"
sudo tail -n 10 /var/log/nginx/scholarify_error.log 2>/dev/null || echo "Aucun log d'erreur trouvé"

echo ""
echo "🎯 Résumé des actions à effectuer:"
echo "1. Assurez-vous que votre application Next.js est buildée: npm run build"
echo "2. Démarrez votre application: npm start ou pm2 start"
echo "3. Vérifiez que l'application répond sur localhost:3000"
echo "4. Testez votre site: curl -I https://scholarifyltd.com"
echo ""
echo "🔧 Commandes utiles:"
echo "  - Voir les logs: sudo tail -f /var/log/nginx/scholarify_*.log"
echo "  - Tester Nginx: sudo nginx -t"
echo "  - Recharger Nginx: sudo systemctl reload nginx"
