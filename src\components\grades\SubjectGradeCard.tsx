"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, User, ArrowR<PERSON>, TrendingUp } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";

interface SubjectGradeCardProps {
  subjectId: string;
  subjectName: string;
  teacherName: string;
  classAverage: number;
  gradeCount: number;
  onClick: () => void;
}

export default function SubjectGradeCard({
  subjectId,
  subjectName,
  teacherName,
  classAverage,
  gradeCount,
  onClick
}: SubjectGradeCardProps) {
  const { t } = useTranslation();
  
  // Determine grade color based on average
  const getAverageColor = (average: number) => {
    if (average >= 16) return "text-green-600 dark:text-green-400";
    if (average >= 14) return "text-blue-600 dark:text-blue-400";
    if (average >= 12) return "text-yellow-600 dark:text-yellow-400";
    if (average >= 10) return "text-orange-600 dark:text-orange-400";
    return "text-red-600 dark:text-red-400";
  };

  const getGradeText = (average: number) => {
    if (average >= 16) return "Excellent";
    if (average >= 14) return "Très bien";
    if (average >= 12) return "Assez bien";
    if (average >= 10) return "Passable";
    return "Insuffisant";
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className="bg-widget border border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:shadow-lg transition-all duration-200"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-blue-500/10 dark:bg-blue-500/20 rounded-lg flex items-center justify-center">
            <BookOpen className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground text-lg">
              {subjectName}
            </h3>
            <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
              <User className="w-3 h-3" />
              <span>{teacherName}</span>
            </div>
          </div>
        </div>
        <ArrowRight className="w-5 h-5 text-gray-400" />
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {t("dashboard.school-admin.pages.grades.classes.class_average")}
            </span>
          </div>
          <div className="text-right">
            <div className={`text-2xl font-bold ${getAverageColor(classAverage)}`}>
              {classAverage.toFixed(1)}
            </div>
            <div className={`text-xs ${getAverageColor(classAverage)}`}>
              {getGradeText(classAverage)}
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500 dark:text-gray-400">
            {t("dashboard.school-admin.pages.grades.total_grades")}
          </span>
          <span className="font-medium text-foreground">
            {gradeCount}
          </span>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button className="text-teal hover:text-teal-dark text-sm font-medium flex items-center gap-1">
          {t("dashboard.school-admin.pages.grades.manage_grades")}
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </motion.div>
  );
}
