import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://backend.scholarifyltd.com/api";

export interface GradeRecord extends Record<string, unknown> {
  _id: string;
  student_name: string;
  student_id: string;
  class_name: string;
  subject_name: string;
  exam_type: string;

  // Enhanced term information
  term: string;
  term_id?: string;
  term_number?: number;
  sequence_number?: number;
  sequence_name?: string;
  academic_year: string;

  // Grade information
  score: number;
  grade: string;
  comments?: string;
  date_entered: string;

  // Additional term metadata
  term_start_date?: string;
  term_end_date?: string;
  is_current_term?: boolean;
}

export interface GradeStats {
  totalGrades: number;
  averageScore: number;
  highestScore: number;
  lowestScore: number;
  passRate: number;
}

export interface GradeSequence {
  sequence_number: number;
  sequence_name: string;
}

export interface GradeTerm {
  _id: string;
  name: string;
  term_number: number;
  academic_year: string;
  is_current: boolean;
  sequences: GradeSequence[];
  start_date: string;
  end_date: string;
}

export interface AvailableTermsResponse {
  terms: GradeTerm[];
  current_term: GradeTerm | null;
  message: string;
}

export interface GradeFilters {
  class_id?: string;
  subject_id?: string;
  term?: string;
  term_id?: string;
  sequence_number?: number;
  exam_type_id?: string;
  academic_year?: string;
  student_id?: string;
  page?: number;
  limit?: number;
}

export interface GradeResponse {
  grade_records: GradeRecord[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    per_page: number;
  };
  message: string;
}

// Get grade records for a school with filters
export async function getGradeRecords(schoolId: string, filters: GradeFilters = {}): Promise<GradeResponse> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/grades/school/${schoolId}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching grade records:", response.statusText);
      throw new Error("Failed to fetch grade records");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch grade records error:", error);
    throw new Error("Failed to fetch grade records");
  }
}

// Get grade statistics for a school
export async function getGradeStats(schoolId: string, filters: Omit<GradeFilters, 'page' | 'limit'> = {}): Promise<{
  stats: GradeStats;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/grades/school/${schoolId}/stats${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching grade stats:", response.statusText);
      throw new Error("Failed to fetch grade statistics");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch grade stats error:", error);
    throw new Error("Failed to fetch grade statistics");
  }
}

// Create grade record (using legacy route)
export async function createGrade(gradeData: any): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/grades/create-grade`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(gradeData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating grade:", errorData);
      throw new Error(errorData.message || "Failed to create grade");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create grade error:", error);
    throw error;
  }
}

// Update grade record (using legacy route)
export async function updateGrade(gradeId: string, gradeData: any): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    // Clean the data - remove undefined or null values
    const cleanData = { ...gradeData };

    // Remove any undefined or null values
    Object.keys(cleanData).forEach(key => {
      if (cleanData[key] === undefined || cleanData[key] === null) {
        delete cleanData[key];
      }
    });

    // Ensure school_id is present (required by backend)
    if (!cleanData.school_id) {
      throw new Error("School ID is required for grade update");
    }

    console.log("Sending update payload:", cleanData);

    const response = await fetch(`${BASE_API_URL}/grades/update-grade/${gradeId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(cleanData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error updating grade:", errorData);
      throw new Error(errorData.message || "Failed to update grade");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update grade error:", error);
    throw error;
  }
}

// Delete grade record (using legacy route)
export async function deleteGrade(gradeId: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/grades/delete-grade/${gradeId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting grade:", errorData);
      throw new Error(errorData.message || "Failed to delete grade");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete grade error:", error);
    throw error;
  }
}

// Delete multiple grade records (using legacy route)
export async function deleteMultipleGrades(gradeIds: string[]): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/grades/delete-grades`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ ids: gradeIds }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting multiple grades:", errorData);
      throw new Error(errorData.message || "Failed to delete grades");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete multiple grades error:", error);
    throw error;
  }
}

// Export grades to PDF
export async function exportGradesPDF(schoolId: string, filters: any = {}): Promise<Blob> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key] !== 'all') {
        queryParams.append(key, filters[key]);
      }
    });

    const response = await fetch(`${BASE_API_URL}/grades/school/${schoolId}/export/pdf?${queryParams}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error exporting grades to PDF:", errorData);
      throw new Error(errorData.message || "Failed to export grades to PDF");
    }

    return await response.blob();
  } catch (error) {
    console.error("Export grades PDF error:", error);
    throw error;
  }
}

// Export grades to Excel
export async function exportGradesExcel(schoolId: string, filters: any = {}): Promise<Blob> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key] !== 'all') {
        queryParams.append(key, filters[key]);
      }
    });

    const response = await fetch(`${BASE_API_URL}/grades/school/${schoolId}/export/excel?${queryParams}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error exporting grades to Excel:", errorData);
      throw new Error(errorData.message || "Failed to export grades to Excel");
    }

    return await response.blob();
  } catch (error) {
    console.error("Export grades Excel error:", error);
    throw error;
  }
}

// Get available terms and sequences for grade creation
export async function getAvailableTerms(schoolId: string): Promise<AvailableTermsResponse> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/grades/school/${schoolId}/terms`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error fetching available terms:", errorData);
      throw new Error(errorData.message || "Failed to fetch available terms");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch available terms error:", error);
    throw new Error("Failed to fetch available terms");
  }
}
