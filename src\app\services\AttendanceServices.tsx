import { AttendancePopulatedSchema } from "../models/Attendance";
import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://backend.scholarifyltd.com/api";

export interface AttendanceRecord extends Record<string, unknown> {
  _id: string;
  student_name: string;
  student_id: string;
  class_name: string;
  subject_name: string;
  teacher_name: string;
  period_number: number;
  status: 'Present' | 'Absent' | 'Late' | 'Excused';
  date: string;
  academic_year: string;
  school_id: string;
}

export interface AttendanceStats {
  totalStudents: number;
  presentToday: number;
  absentToday: number;
  lateToday: number;
  excusedToday: number;
  attendanceRate: number;
}

export interface AttendanceFilters {
  date?: string;
  class_id?: string;
  status?: string;
  student_id?: string;
  subject_id?: string;
  period_number?: number | 'all'; // 'all' for all periods
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

export interface AttendanceResponse {
  attendance_records: AttendanceRecord[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    per_page: number;
  };
  message: string;
}

// Get attendance records for a school with filters
export async function getAttendanceRecords(schoolId: string, filters: AttendanceFilters = {}): Promise<AttendanceResponse> {
  const token = getTokenFromCookie("idToken");

  if (!token) {
    throw new Error("Authentication token not found");
  }

  if (!schoolId) {
    throw new Error("School ID is required");
  }

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/attendance/school/${schoolId}${queryString ? `?${queryString}` : ''}`;

    console.log('Fetching attendance records from:', url);
    console.log('Filters applied:', filters);

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error("Error fetching attendance records:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });
      throw new Error(errorData.message || `Failed to fetch attendance records: ${response.status}`);
    }

    const data = await response.json();
    console.log('Attendance records response:', {
      totalRecords: data.attendance_records?.length || 0,
      pagination: data.pagination,
      sampleRecord: data.attendance_records?.[0] || null
    });

    // Validate response structure
    if (!data.attendance_records || !Array.isArray(data.attendance_records)) {
      console.warn('Invalid response structure:', data);
      return {
        attendance_records: [],
        pagination: {
          current_page: 1,
          total_pages: 0,
          total_records: 0,
          per_page: 50
        },
        message: 'No attendance records found'
      };
    }

    return data;
  } catch (error) {
    console.error("Fetch attendance records error:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to fetch attendance records");
  }
}

// Get attendance statistics for a school
export async function getAttendanceStats(schoolId: string, date?: string, p0?: { class_id: string; }): Promise<{
  stats: AttendanceStats;
  date: string;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    if (date) {
      queryParams.append('date', date);
    }

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/attendance/school/${schoolId}/stats${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching attendance stats:", response.statusText);
      throw new Error("Failed to fetch attendance statistics");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch attendance stats error:", error);
    throw new Error("Failed to fetch attendance statistics");
  }
}

// Create attendance record (using legacy route)
export async function createAttendance(attendanceData: any): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  if (!token) {
    throw new Error("Authentication token not found");
  }

  if (!attendanceData) {
    throw new Error("Attendance data is required");
  }

  try {
    console.log('Creating attendance with data:', attendanceData);

    const response = await fetch(`${BASE_API_URL}/attendance/create-attendance`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(attendanceData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error("Error creating attendance:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        requestData: attendanceData
      });
      throw new Error(errorData.message || `Failed to create attendance: ${response.status}`);
    }

    const data = await response.json();
    console.log('Attendance created successfully:', data);
    return data;
  } catch (error) {
    console.error("Create attendance error:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to create attendance");
  }
}

// Update attendance record (using legacy route)
export async function updateAttendance(attendanceId: string, attendanceData: any, p0?: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  if (!token) {
    throw new Error("Authentication token not found");
  }

  if (!attendanceId) {
    throw new Error("Attendance ID is required");
  }

  if (!attendanceData) {
    throw new Error("Attendance data is required");
  }

  try {
    console.log('Updating attendance:', { attendanceId, attendanceData });

    const response = await fetch(`${BASE_API_URL}/attendance/update-attendance/${attendanceId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(attendanceData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error("Error updating attendance:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        attendanceId,
        requestData: attendanceData
      });
      throw new Error(errorData.message || `Failed to update attendance: ${response.status}`);
    }

    const data = await response.json();
    console.log('Attendance updated successfully:', data);
    return data;
  } catch (error) {
    console.error("Update attendance error:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to update attendance");
  }
}

// Delete attendance record (using legacy route)
export async function deleteAttendance(attendanceId: string, schoolId: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/attendance/delete-attendance/${attendanceId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ school_id: schoolId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting attendance:", errorData);
      throw new Error(errorData.message || "Failed to delete attendance");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete attendance error:", error);
    throw error;
  }
}

// Delete multiple attendance records (using legacy route)
export async function deleteMultipleAttendances(attendanceIds: string[], p0?: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/attendance/delete-attendances`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ ids: attendanceIds }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting multiple attendances:", errorData);
      throw new Error(errorData.message || "Failed to delete attendances");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete multiple attendances error:", error);
    throw error;
  }
}

// Export attendance to PDF
export async function exportAttendancePDF(schoolId: string, filters: any = {}): Promise<Blob> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key] !== 'all') {
        queryParams.append(key, filters[key]);
      }
    });

    const response = await fetch(`${BASE_API_URL}/attendance/school/${schoolId}/export/pdf?${queryParams}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error exporting attendance to PDF:", errorData);
      throw new Error(errorData.message || "Failed to export attendance to PDF");
    }

    return await response.blob();
  } catch (error) {
    console.error("Export attendance PDF error:", error);
    throw error;
  }
}

// Export attendance to Excel
export async function exportAttendanceExcel(schoolId: string, filters: any = {}): Promise<Blob> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key] !== 'all') {
        queryParams.append(key, filters[key]);
      }
    });

    const response = await fetch(`${BASE_API_URL}/attendance/school/${schoolId}/export/excel?${queryParams}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error exporting attendance to Excel:", errorData);
      throw new Error(errorData.message || "Failed to export attendance to Excel");
    }

    return await response.blob();
  } catch (error) {
    console.error("Export attendance Excel error:", error);
    throw error;
  }
}


interface GetAttendanceBySchedulesPayload {
  school_id: string;
  schedule_ids: string[];
  date?: string; // Add this line
}
interface AttendancePopulatedResponse {
  success: boolean;
  data: AttendancePopulatedSchema[];
}

/**
 * Get attendance by multiple schedule_ids and school_id
 */
export async function getAttendanceBySchedules(
  payload: GetAttendanceBySchedulesPayload
): Promise<AttendancePopulatedSchema[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/attendance/get-by-schedules`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error getting attendance by schedules:", errorData);
      throw new Error(errorData.message || "Failed to fetch attendance");
    }

    const result: AttendancePopulatedResponse = await response.json();
    return result.data;
  } catch (error) {
    console.error("Fetch attendance by schedules error:", error);
    throw error;
  }
}

export const createOrUpdateAttendance = async (
  attendanceRecords: AttendancePopulatedSchema[] // This payload comes from the frontend
) => {
  const token = getTokenFromCookie("idToken"); // Get the token

  try {
    const response = await fetch(`${BASE_API_URL}/attendance/create-or-update-attendance`, { // Adjust endpoint as necessary
      method: "POST", // Or "PUT" if your backend differentiates between create/update
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`, // Include the authorization header
      },
      body: JSON.stringify({ attendanceRecords }), // Wrap in an object if your backend expects it this way
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating or updating attendance:", errorData);
      throw new Error(errorData.message || "Failed to submit attendance");
    }

    // Assuming your backend returns some data upon success (e.g., created/updated records)
    const result = await response.json();
    return result.data; // Or just `result` depending on your backend's response structure
  } catch (error) {
    console.error("Fetch create/update attendance error:", error);
    throw error;
  }
};