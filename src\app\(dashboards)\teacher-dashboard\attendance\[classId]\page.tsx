"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";
import { ClipboardList, Calendar, ArrowLeft, Plus } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import TeacherClassAttendanceSkeleton from "@/components/skeletons/TeacherClassAttendanceSkeleton";
import TeacherAttendanceModal from "@/components/modals/TeacherAttendanceModal";
import DataTableFix from "@/components/utils/TableFix";
import { AttendanceRecord } from "@/app/services/AttendanceServices";
import { SubjectSchema } from "@/app/models/Subject";
import { debugClassAttendance } from "@/utils/attendanceDebugger";
import { PeriodSchema } from "@/app/models/Period";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface ClassInfo {
  _id: string;
  name: string;
  level: string;
  section: string;
  class_level?: string;
  class_code?: string;
}

// Period and Subject interfaces are now imported from models

interface Schedule {
  _id: string;
  class_id: string;
  period_id: string;
  subject_id: string;
  teacher_id: string;
  day_of_week: string;
}

interface Student {
  _id: string;
  first_name: string;
  last_name: string;
  roll_number?: string;
  class_id?: string;
}

// AttendanceRecord interface is now imported from AttendanceServices

const navigation = {
  icon: ClipboardList,
  baseHref: "/teacher-dashboard/attendance",
  title: "Class Attendance"
};

export default function TeacherClassAttendancePage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const params = useParams();
  const classId = params.classId as string;
  
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [classInfo, setClassInfo] = useState<ClassInfo | null>(null);
  const [periods, setPeriods] = useState<PeriodSchema[]>([]);
  const [subjects, setSubjects] = useState<SubjectSchema[]>([]);
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // DataTable states
  const [selectedRecords, setSelectedRecords] = useState<string[]>([]);
  const [itemsPerPage] = useState(10);
  
  // Modal states
  const [isAttendanceModalOpen, setIsAttendanceModalOpen] = useState(false);
  const [attendanceToEdit, setAttendanceToEdit] = useState<any>(null);

  // Define fetchClassData with useCallback first
  const fetchClassData = useCallback(async (schoolId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Import services
      const { getTeacherPermissions } = await import("@/app/services/TeacherPermissionServices");
      const { getTeacherSchedule } = await import("@/app/services/TeacherPermissionServices");
      const { getStudentsBySchool } = await import("@/app/services/StudentServices");
      const { getAttendanceRecords } = await import("@/app/services/AttendanceServices");
      const { getPeriodsBySchool } = await import("@/app/services/PeriodServices");

      // Fetch teacher's permissions and assignments first
      const teacherData = await getTeacherPermissions(schoolId);

      if (!teacherData.assigned_classes || teacherData.assigned_classes.length === 0) {
        throw new Error("No classes assigned to this teacher");
      }

      // Fetch teacher's schedule and other data in parallel
      const [
        teacherScheduleData,
        studentsData,
        attendanceResponse,
        periodsData
      ] = await Promise.all([
        getTeacherSchedule(schoolId),
        getStudentsBySchool(schoolId),
        getAttendanceRecords(schoolId),
        getPeriodsBySchool(schoolId)
      ]);

      // Extract attendance records from response
      const attendanceData = attendanceResponse.attendance_records || [];

      // Find the specific class from teacher's assigned classes
      const currentClass = teacherData.assigned_classes.find((cls: any) => cls._id === classId);
      if (!currentClass) {
        console.error("Class not found in teacher's assignments. ClassId:", classId, "Available classes:", teacherData.assigned_classes);
        throw new Error("Class not found in teacher's assignments");
      }



      // Get teacher's subjects for this specific class from assigned_subjects
      // Transform to SubjectSchema format
      const teacherSubjects: SubjectSchema[] = teacherData.assigned_subjects
        .filter((subject: any) => subject.class_id === classId)
        .map((subject: any) => ({
          _id: subject._id || `${subject.name}-${classId}`,
          subject_id: subject._id || `${subject.name}-${classId}`,
          name: subject.name,
          subject_code: subject.name.substring(0, 3).toUpperCase(),
          compulsory: true,
          school_id: schoolId,
          class_id: [classId],
          description: subject.description || "",
          coefficient: subject.coefficient || 1,
          department: subject.department,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }));

      // Get periods used by this teacher from the schedule
      const teacherPeriodIds = teacherScheduleData
        .filter((schedule: any) => schedule.class_id === classId)
        .map((schedule: any) => schedule.period_id);

      // Filter periods to only those used by the teacher for this class
      const teacherPeriods = periodsData.filter((period: any) =>
        teacherPeriodIds.includes(period._id)
      );

      // Filter students for this class and transform to local Student interface
      const classStudents: Student[] = studentsData
        .filter((student: any) => student.class_id === classId)
        .map((student: any) => ({
          _id: student._id,
          first_name: student.first_name,
          last_name: student.last_name,
          roll_number: student.roll_number,
          class_id: student.class_id
        }));


      // Filter attendance records for this class
      // Try multiple filtering approaches to ensure we get the right data
      let classAttendance = attendanceData.filter((record: any) =>
        record.class_name === currentClass.name
      );


      // If no records found by class name, try filtering by students in the class
      if (classAttendance.length === 0) {
        const classStudentIds = classStudents.map(student => student._id);
        classAttendance = attendanceData.filter((record: any) =>
          classStudentIds.includes(record.student_id)
        );
      }

      // If still no records, try a broader search
      if (classAttendance.length === 0) {
        console.log("No attendance records found. Checking all available records:");
        console.log("Available class names in attendance:", [...new Set(attendanceData.map((r: any) => r.class_name))]);
        console.log("Current class name:", currentClass.name);
        console.log("Available student IDs in attendance:", [...new Set(attendanceData.map((r: any) => r.student_id))]);
        console.log("Class student IDs:", classStudents.map(s => s._id));
      }


      // Debug class attendance if no records found
      if (classAttendance.length === 0) {
        console.log("🔍 No attendance records found, running detailed debug...");
        const debugResult = await debugClassAttendance(schoolId, classId);
        console.log("🔍 Debug result:", debugResult);
      }

      // Transform ClassSchema to ClassInfo
      const classInfo: ClassInfo = {
        _id: currentClass._id,
        name: currentClass.name,
        level: currentClass.level || "",
        section: "",
        class_level: currentClass.level,
        class_code: ""
      };
      setClassInfo(classInfo);
      setPeriods(teacherPeriods);
      setSubjects(teacherSubjects);

      // Use real teacher schedule instead of mock data
      const realSchedules = teacherScheduleData.filter((schedule: any) =>
        schedule.class_id === classId
      );
      setSchedules(realSchedules);
      setStudents(classStudents);
      setAttendanceRecords(classAttendance);
      console.log("schedules ", realSchedules, 'teacherScheduleData', teacherScheduleData)

    } catch (error) {
      console.error("Error fetching class data:", error);
      setError(error instanceof Error ? error.message : "Failed to load class data");
    } finally {
      setLoading(false);
    }
  }, [classId]); // Ajout des dépendances pour useCallback

  // Check authentication and get class data
  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        fetchClassData(school.school_id);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router, classId, fetchClassData]);

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const handleCreateAttendance = () => {
    setAttendanceToEdit(null);
    setIsAttendanceModalOpen(true);
  };

  const handleEditAttendance = (attendance: AttendanceRecord) => {
    console.log("🔍 Editing attendance:", attendance);
    console.log("🔍 Available schedules:", schedules);
    console.log("🔍 Available periods:", periods);

    // Find the corresponding subject ID
    const subjectId = subjects.find(s => s.name === attendance.subject_name)?._id || "";

    // Find the matching schedule by subject and period number
    const matchingSchedule = schedules.find(s => {
      const matchingSubject = subjects.find(sub => sub._id === s.subject_id && sub.name === attendance.subject_name);
      const matchingPeriod = periods.find(p => p._id === s.period_id && p.period_number === attendance.period_number);

      return matchingSubject && matchingPeriod;
    });

    console.log("🔍 Found matching schedule:", matchingSchedule);

    // Find the period ID from the matching schedule
    const periodId = matchingSchedule?.period_id || "";

    // Get the day of week from the matching schedule
    const dayOfWeek = matchingSchedule?.day_of_week || "";

    console.log("🔍 Extracted data:", {
      subjectId,
      periodId,
      dayOfWeek,
      scheduleId: matchingSchedule?._id || ""
    });

    // Transform AttendanceRecord to the format expected by the modal
    const transformedAttendance = {
      _id: attendance._id,
      status: attendance.status,
      date: attendance.date,
      academic_year: attendance.academic_year,
      student_id: attendance.student_id,
      student_name: attendance.student_name,
      class_name: attendance.class_name,
      subject_name: attendance.subject_name,
      period_number: attendance.period_number,
      teacher_name: attendance.teacher_name,

      // Required fields for the modal
      day_of_week: dayOfWeek,
      period_id: periodId,
      subject_id: subjectId,
      class_id: classInfo?._id || "",

      // Schedule ID for reference
      schedule_id: matchingSchedule?._id || ""
    };

    console.log("🔍 Transformed attendance for editing:", transformedAttendance);

    setAttendanceToEdit(transformedAttendance);
    setIsAttendanceModalOpen(true);
  };
  console.log("teacher ", user, " AttendanceRecord ", attendanceRecords);
  const handleAttendanceSubmit = async (data: any) => {
    try {
      if (attendanceToEdit) {
        // Update existing attendance
        const { updateAttendance } = await import("@/app/services/AttendanceServices");
        const updateData = {
          ...data,
          school_id: selectedSchool?.school_id,
          teacher_name: user?.name || `${user?.firstName || ''} ${user?.lastName || ''}`.trim()
        };
        await updateAttendance(attendanceToEdit._id, updateData);
      } else {
        // Create new attendance - add school_id and teacher info to data
        const { createAttendance } = await import("@/app/services/AttendanceServices");
        const attendanceData = {
          ...data,
          school_id: selectedSchool?.school_id,
          teacher_id: user?._id,
          teacher_name: user?.name || `${user?.firstName || ''} ${user?.lastName || ''}`.trim()
        };
        await createAttendance(attendanceData);
      }

      // Refresh data
      if (selectedSchool?.school_id) {
        await fetchClassData(selectedSchool.school_id);
      }

      setIsAttendanceModalOpen(false);
      setAttendanceToEdit(null);
    } catch (error) {
      console.error("Error submitting attendance:", error);
      throw error;
    }
  };

  // DataTable functions - simplified for new DataTableFix
  const handleSelectionChange = useCallback((selection: AttendanceRecord[]) => {
    setSelectedRecords(selection.map(record => record._id));
  }, []);

  // handleDeleteSelected is now replaced by handleDeleteMultiple

  const handleDeleteMultiple = useCallback(async (selectedIds: string[]) => {
    if (selectedIds.length === 0) return;

    try {
      const { deleteMultipleAttendances } = await import("@/app/services/AttendanceServices");
      await deleteMultipleAttendances(selectedIds);

      // Refresh data
      if (selectedSchool?.school_id) {
        await fetchClassData(selectedSchool.school_id);
      }

      setSelectedRecords([]);
    } catch (error) {
      console.error("Error deleting attendance records:", error);
    }
  }, [selectedSchool?.school_id, fetchClassData]);

  const handleDeleteRecord = async (recordId: string) => {
    try {
      const { deleteAttendance } = await import("@/app/services/AttendanceServices");
      await deleteAttendance(recordId, selectedSchool?.school_id as string);

      // Refresh data
      if (selectedSchool?.school_id) {
        await fetchClassData(selectedSchool.school_id);
      }
    } catch (error) {
      console.error("Error deleting attendance record:", error);
    }
  };

  // Define columns for DataTableFix
  const columns = [
    {
      header: "Student",
      accessor: (record: AttendanceRecord) => (
        <div className="font-medium text-foreground">
          {record.student_name}
        </div>
      )
    },
    {
      header: "Subject",
      accessor: (record: AttendanceRecord) => (
        <div className="text-foreground/80">
          {record.subject_name}
        </div>
      )
    },
    {
      header: "Period",
      accessor: (record: AttendanceRecord) => (
        <div className="text-foreground/80">
          Period {record.period_number}
        </div>
      )
    },
    {
      header: "Status",
      accessor: (record: AttendanceRecord) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            record.status === 'Present'
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
              : record.status === 'Absent'
              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
              : record.status === 'Late'
              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
              : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
          }`}
        >
          {record.status}
        </span>
      )
    },
    {
      header: "Date",
      accessor: (record: AttendanceRecord) => (
        <div className="text-foreground/80">
          {new Date(record.date).toLocaleDateString()}
        </div>
      )
    },
    {
      header: "Teacher",
      accessor: (record: AttendanceRecord) => (
        <div className="text-foreground/80">
          {record.teacher_name}
        </div>
      )
    }
  ];

  // Define actions for DataTableFix
  const actions = [
    {
      label: "edit",
      onClick: (record: AttendanceRecord) => handleEditAttendance(record)
    },
    {
      label: "delete",
      onClick: (record: AttendanceRecord) => handleDeleteRecord(record._id)
    }
  ];

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <TeacherClassAttendanceSkeleton />
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <div className="text-center py-12">
            <ClipboardList className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">Error Loading Class</h3>
            <p className="text-foreground/60 mb-6">{error}</p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => router.push("/teacher-dashboard/attendance")}
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 inline mr-2" />
                Back to Attendance
              </button>
              <button
                onClick={() => fetchClassData(selectedSchool?.school_id as string)}
                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.push("/teacher-dashboard/attendance")}
                className="p-2 text-foreground/60 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  {classInfo?.name} - Attendance
                </h1>
                <p className="text-foreground/60">
                  {classInfo?.level} • {students.length} students
                </p>
              </div>
            </div>
            
            <button
              onClick={handleCreateAttendance}
              className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Mark Attendance</span>
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-widget rounded-lg border border-stroke p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {attendanceRecords.filter(r => r.status === 'Present').length}
                </div>
                <div className="text-sm text-foreground/60">Present Today</div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {attendanceRecords.filter(r => r.status === 'Absent').length}
                </div>
                <div className="text-sm text-foreground/60">Absent Today</div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                  {attendanceRecords.filter(r => r.status === 'Late').length}
                </div>
                <div className="text-sm text-foreground/60">Late Today</div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">
                  {students.length}
                </div>
                <div className="text-sm text-foreground/60">Total Students</div>
              </div>
            </div>
          </div>

          {/* Attendance Records with DataTableFix */}
          <div className="bg-widget rounded-lg border border-stroke">
            <div className="p-6 border-b border-stroke">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <h2 className="text-lg font-semibold text-foreground">
                  Attendance Records
                </h2>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-foreground/60" />
                  <span className="text-sm text-foreground/60">
                    {new Date().toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
              </div>
            </div>

            {attendanceRecords.length === 0 ? (
              <div className="text-center py-12">
                <ClipboardList className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No Attendance Records</h3>
                <p className="text-foreground/60 mb-6">
                  No attendance has been marked for this class yet.
                </p>
                <button
                  onClick={handleCreateAttendance}
                  className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
                >
                  <Plus className="h-4 w-4 inline mr-2" />
                  Mark First Attendance
                </button>
              </div>
            ) : (
              <DataTableFix
                data={attendanceRecords}
                columns={columns}
                actions={actions}
                hasSearch={true}
                defaultItemsPerPage={itemsPerPage}
                loading={loading}
                onLoadingChange={setLoading}
                onSelectionChange={handleSelectionChange}
                showCheckbox={true}
                handleDeleteMultiple={handleDeleteMultiple}
                idAccessor="_id"
                enableBulkActions={true}
              />
            )}
          </div>
        </div>

        {/* Teacher Attendance Modal */}
        <TeacherAttendanceModal
          isOpen={isAttendanceModalOpen}
          onClose={() => {
            setIsAttendanceModalOpen(false);
            setAttendanceToEdit(null);
          }}
          onSubmit={handleAttendanceSubmit}
          attendance={attendanceToEdit}
          isEditing={!!attendanceToEdit}
          classInfo={classInfo || { _id: "", name: "", level: "", section: "" }}
          subjects={subjects}
          periods={periods}
          schedules={schedules}
          students={students}
          loading={loading}
        />
      </TeacherLayout>
    </ProtectedRoute>
  );
}
