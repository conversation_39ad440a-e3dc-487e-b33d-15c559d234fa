const CreditPurchase = require('../models/CreditPurchase');
const fapshi = require('../utils/fapshi');
const cron = require('node-cron');

/**
 * Service de surveillance des paiements
 * Détecte automatiquement les transactions problématiques et propose des actions
 */
class PaymentMonitoringService {
  constructor() {
    this.isRunning = false;
    this.monitoringInterval = null;
  }

  /**
   * Démarre la surveillance automatique des paiements
   */
  start() {
    if (this.isRunning) {
      console.log('🔍 Payment monitoring service is already running');
      return;
    }

    console.log('🚀 Starting payment monitoring service...');
    this.isRunning = true;

    // Vérifier toutes les 30 minutes
    this.monitoringInterval = cron.schedule('*/30 * * * *', async () => {
      await this.checkProblematicTransactions();
    }, {
      scheduled: true,
      timezone: "Africa/Douala"
    });

    // Vérification initiale
    this.checkProblematicTransactions();
  }

  /**
   * Arrête la surveillance
   */
  stop() {
    if (this.monitoringInterval) {
      this.monitoringInterval.destroy();
      this.monitoringInterval = null;
    }
    this.isRunning = false;
    console.log('⏹️ Payment monitoring service stopped');
  }

  /**
   * Vérifie les transactions problématiques
   */
  async checkProblematicTransactions() {
    try {
      console.log('🔍 Checking for problematic transactions...');
      
      const currentTime = new Date();
      const oneHourAgo = new Date(currentTime.getTime() - 60 * 60 * 1000);
      const sixHoursAgo = new Date(currentTime.getTime() - 6 * 60 * 60 * 1000);
      const oneDayAgo = new Date(currentTime.getTime() - 24 * 60 * 60 * 1000);

      // Rechercher les transactions en attente
      const pendingTransactions = await CreditPurchase.find({
        payment_status: 'pending',
        payment_method: 'fapshi',
        purchase_date: { $lt: oneHourAgo } // Plus d'1 heure
      }).populate('school_id', 'name email phone');

      console.log(`📊 Found ${pendingTransactions.length} pending transactions to check`);

      for (const transaction of pendingTransactions) {
        await this.checkAndUpdateTransaction(transaction);
      }

      // Identifier les transactions nécessitant une intervention manuelle
      await this.identifyTransactionsNeedingAttention();

    } catch (error) {
      console.error('❌ Error in payment monitoring:', error);
    }
  }

  /**
   * Vérifie et met à jour une transaction spécifique
   */
  async checkAndUpdateTransaction(transaction) {
    try {
      console.log(`🔍 Checking transaction ${transaction.transaction_id}...`);

      // Vérifier le statut auprès de Fapshi
      const fapshiStatus = await fapshi.paymentStatus(transaction.transaction_id);
      
      if (fapshiStatus.statusCode === 200) {
        const status = fapshiStatus.status;
        
        switch (status) {
          case 'SUCCESSFUL':
            if (transaction.payment_status === 'pending') {
              await this.markTransactionAsCompleted(transaction, fapshiStatus);
              console.log(`✅ Transaction ${transaction.transaction_id} marked as completed`);
            }
            break;
            
          case 'FAILED':
            if (transaction.payment_status === 'pending') {
              await this.markTransactionAsFailed(transaction, 'Payment failed on Fapshi');
              console.log(`❌ Transaction ${transaction.transaction_id} marked as failed`);
              await this.notifyFailedTransaction(transaction);
            }
            break;
            
          case 'EXPIRED':
            if (transaction.payment_status === 'pending') {
              await this.markTransactionAsExpired(transaction);
              console.log(`⏰ Transaction ${transaction.transaction_id} marked as expired`);
              await this.notifyExpiredTransaction(transaction);
            }
            break;
            
          case 'PENDING':
            // Vérifier si la transaction est en attente depuis trop longtemps
            const hoursPending = (Date.now() - new Date(transaction.purchase_date).getTime()) / (1000 * 60 * 60);
            if (hoursPending > 24) {
              console.log(`⚠️ Transaction ${transaction.transaction_id} pending for ${hoursPending.toFixed(1)} hours`);
              await this.notifyLongPendingTransaction(transaction, hoursPending);
            }
            break;
        }
      } else {
        console.log(`⚠️ Could not verify status for transaction ${transaction.transaction_id}`);
      }
    } catch (error) {
      console.error(`❌ Error checking transaction ${transaction.transaction_id}:`, error);
    }
  }

  /**
   * Marque une transaction comme complétée
   */
  async markTransactionAsCompleted(transaction, fapshiData) {
    transaction.payment_status = 'completed';
    transaction.payment_completed_date = new Date();
    transaction.metadata = {
      ...transaction.metadata,
      fapshi_confirmation: fapshiData,
      auto_completed: true,
      auto_completed_date: new Date()
    };
    
    await transaction.save();
    
    // Ajouter les crédits à l'école
    await this.addCreditsToSchool(transaction);
  }

  /**
   * Marque une transaction comme échouée
   */
  async markTransactionAsFailed(transaction, reason) {
    transaction.payment_status = 'failed';
    transaction.metadata = {
      ...transaction.metadata,
      failure_reason: reason,
      auto_failed: true,
      auto_failed_date: new Date()
    };
    
    await transaction.save();
  }

  /**
   * Marque une transaction comme expirée
   */
  async markTransactionAsExpired(transaction) {
    transaction.payment_status = 'expired';
    transaction.metadata = {
      ...transaction.metadata,
      auto_expired: true,
      auto_expired_date: new Date()
    };
    
    await transaction.save();
  }

  /**
   * Ajoute les crédits à l'école
   */
  async addCreditsToSchool(transaction) {
    try {
      const SchoolSubscription = require('../models/SchoolSubscription');
      
      const subscription = await SchoolSubscription.findOne({ 
        school_id: transaction.school_id._id 
      });
      
      if (subscription) {
        subscription.available_credits += transaction.credits_purchased;
        subscription.total_credits_purchased += transaction.credits_purchased;
        subscription.total_amount_paid += transaction.total_amount;
        await subscription.save();
        
        console.log(`💰 Added ${transaction.credits_purchased} credits to school ${transaction.school_id.name}`);
      }
    } catch (error) {
      console.error('Error adding credits to school:', error);
    }
  }

  /**
   * Identifie les transactions nécessitant une attention manuelle
   */
  async identifyTransactionsNeedingAttention() {
    try {
      const currentTime = new Date();
      const oneDayAgo = new Date(currentTime.getTime() - 24 * 60 * 60 * 1000);
      
      const problematicTransactions = await CreditPurchase.find({
        $or: [
          {
            payment_status: 'pending',
            purchase_date: { $lt: oneDayAgo }
          },
          {
            payment_status: 'failed'
          },
          {
            payment_status: 'expired'
          }
        ]
      }).populate('school_id', 'name email phone');

      if (problematicTransactions.length > 0) {
        console.log(`🚨 Found ${problematicTransactions.length} transactions needing attention`);
        
        // Ici, vous pourriez envoyer une notification aux administrateurs
        await this.notifyAdministrators(problematicTransactions);
      }
    } catch (error) {
      console.error('Error identifying transactions needing attention:', error);
    }
  }

  /**
   * Notifie les administrateurs des transactions problématiques
   */
  async notifyAdministrators(transactions) {
    // Implémentation des notifications (email, Slack, etc.)
    console.log(`📧 Notifying administrators about ${transactions.length} problematic transactions`);
    
    // Exemple: log détaillé pour les administrateurs
    transactions.forEach(transaction => {
      console.log(`⚠️ ATTENTION NEEDED: Transaction ${transaction.transaction_id} - ${transaction.school_id.name} - ${transaction.total_amount} XAF - Status: ${transaction.payment_status}`);
    });
  }

  /**
   * Notifie une transaction échouée
   */
  async notifyFailedTransaction(transaction) {
    console.log(`📧 Notifying failed transaction: ${transaction.transaction_id}`);
    // Implémentation de notification spécifique pour les échecs
  }

  /**
   * Notifie une transaction expirée
   */
  async notifyExpiredTransaction(transaction) {
    console.log(`📧 Notifying expired transaction: ${transaction.transaction_id}`);
    // Implémentation de notification spécifique pour les expirations
  }

  /**
   * Notifie une transaction en attente depuis longtemps
   */
  async notifyLongPendingTransaction(transaction, hoursPending) {
    console.log(`📧 Notifying long pending transaction: ${transaction.transaction_id} (${hoursPending.toFixed(1)}h)`);
    // Implémentation de notification spécifique pour les attentes prolongées
  }

  /**
   * Obtient les statistiques de surveillance
   */
  async getMonitoringStats() {
    try {
      const currentTime = new Date();
      const oneDayAgo = new Date(currentTime.getTime() - 24 * 60 * 60 * 1000);
      
      const stats = await CreditPurchase.aggregate([
        {
          $match: {
            purchase_date: { $gte: oneDayAgo }
          }
        },
        {
          $group: {
            _id: '$payment_status',
            count: { $sum: 1 },
            totalAmount: { $sum: '$total_amount' }
          }
        }
      ]);

      return {
        period: '24h',
        stats,
        isMonitoring: this.isRunning
      };
    } catch (error) {
      console.error('Error getting monitoring stats:', error);
      return null;
    }
  }
}

// Instance singleton
const paymentMonitoringService = new PaymentMonitoringService();

module.exports = paymentMonitoringService;
