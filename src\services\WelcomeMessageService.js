/**
 * Service pour l'envoi de messages de bienvenue par email et SMS
 * avec redirection selon le rôle de l'utilisateur
 */

const { sendEmail } = require('../utils/emailService');
const SMSService = require('../utils/smsService');
const { getEmailTemplate } = require('../templates/emailTemplates');

class WelcomeMessageService {
  
  /**
   * Obtient l'URL de redirection selon le rôle
   */
  static getDashboardUrl(role) {
    const roleRedirectMap = {
      'super': '/super-admin/dashboard',
      'admin': '/school-admin/dashboard', 
      'school_admin': '/school-admin/dashboard',
      'dean_of_studies': '/school-admin/dashboard',
      'bursar': '/school-admin/dashboard',
      'teacher': '/teacher-dashboard'
    };

    return `${process.env.FRONTEND_URL}${roleRedirectMap[role] || '/dashboard'}`;
  }

  /**
   * Obtient le nom d'affichage du rôle
   */
  static getRoleDisplayName(role) {
    const roleDisplayNames = {
      'super': 'Super Administrateur',
      'admin': 'Administrateur d\'École',
      'school_admin': 'Administrateur d\'École',
      'dean_of_studies': 'Directeur des Études',
      'bursar': 'Économe',
      'teacher': 'Enseignant',
      'student': 'Étudiant',
      'parent': 'Parent'
    };

    return roleDisplayNames[role] || role;
  }

  /**
   * Obtient le message de bienvenue personnalisé selon le rôle
   */
  static getWelcomeMessage(role, firstName, schoolName) {
    const roleMessages = {
      'super': `Bienvenue ${firstName} ! Vous avez été ajouté comme Super Administrateur sur Scholarify. Vous avez accès à toutes les fonctionnalités de la plateforme.`,
      'admin': `Bienvenue ${firstName} ! Vous avez été ajouté comme Administrateur de ${schoolName}. Gérez votre école efficacement avec Scholarify.`,
      'school_admin': `Bienvenue ${firstName} ! Vous avez été ajouté comme Administrateur de ${schoolName}. Gérez votre école efficacement avec Scholarify.`,
      'dean_of_studies': `Bienvenue ${firstName} ! Vous avez été nommé Directeur des Études à ${schoolName}. Supervisez les programmes académiques avec Scholarify.`,
      'bursar': `Bienvenue ${firstName} ! Vous avez été nommé Économe à ${schoolName}. Gérez les finances de l'école avec Scholarify.`,
      'teacher': `Bienvenue ${firstName} ! Vous avez rejoint l'équipe enseignante de ${schoolName}. Enseignez et suivez vos élèves avec Scholarify.`,
      'student': `Bienvenue ${firstName} ! Vous êtes maintenant inscrit à ${schoolName}. Suivez vos cours et résultats avec Scholarify.`,
      'parent': `Bienvenue ${firstName} ! Vous pouvez maintenant suivre la scolarité de votre enfant à ${schoolName} avec Scholarify.`
    };

    return roleMessages[role] || `Bienvenue ${firstName} ! Vous avez été ajouté à ${schoolName} sur Scholarify.`;
  }

  /**
   * Envoie un email de bienvenue avec redirection selon le rôle
   */
  static async sendWelcomeEmail(userData) {
    try {
      const {
        user,
        role,
        schoolName,
        resetToken,
        accessCode = null,
        isNewUser = true
      } = userData;

      const dashboardUrl = this.getDashboardUrl(role);
      const roleDisplay = this.getRoleDisplayName(role);
      const welcomeMessage = this.getWelcomeMessage(role, user.first_name, schoolName);

      // URL de configuration du mot de passe
      const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}&email=${user.email}`;

      // Données pour le template d'email
      const emailData = {
        firstName: user.first_name,
        lastName: user.last_name,
        email: user.email,
        role: role,
        roleDisplay: roleDisplay,
        schoolName: schoolName,
        welcomeMessage: welcomeMessage,
        dashboardUrl: dashboardUrl,
        resetUrl: resetUrl,
        accessCode: accessCode,
        isNewUser: isNewUser,
        expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleString('fr-FR'),
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        companyName: 'Scholarify',
        currentYear: new Date().getFullYear()
      };

      // Choisir le template selon le rôle
      const templateType = this.getEmailTemplate(role);
      const htmlContent = getEmailTemplate(templateType, emailData);

      // Sujet personnalisé selon le rôle
      const subject = this.getEmailSubject(role, schoolName, isNewUser);

      const emailOptions = {
        to: user.email,
        subject: subject,
        html: htmlContent
      };

      const result = await sendEmail(emailOptions);
      
      console.log(`✅ Email de bienvenue envoyé à ${user.email} (${roleDisplay})`);
      return { success: true, result };

    } catch (error) {
      console.error('❌ Erreur envoi email de bienvenue:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Envoie un SMS de bienvenue avec lien de redirection
   */
  static async sendWelcomeSMS(userData) {
    try {
      const {
        user,
        role,
        schoolName,
        resetToken
      } = userData;

      if (!user.phone) {
        console.log('⚠️ Pas de numéro de téléphone pour l\'utilisateur:', user.email);
        return { success: false, error: 'No phone number' };
      }

      const dashboardUrl = this.getDashboardUrl(role);
      const roleDisplay = this.getRoleDisplayName(role);

      // URL courte pour le SMS (vous pouvez utiliser un service de raccourcissement d'URL)
      const shortResetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

      // Message SMS personnalisé selon le rôle
      const smsMessage = this.getSMSMessage(role, user.first_name, schoolName, shortResetUrl);

      const smsService = new SMSService();
      const result = await smsService.sendSMS({
        to: user.phone,
        message: smsMessage,
        from: 'Scholarify'
      });

      if (result.success) {
        console.log(`✅ SMS de bienvenue envoyé à ${user.phone} (${roleDisplay})`);
      } else {
        console.error(`❌ Échec envoi SMS à ${user.phone}:`, result.error);
      }

      return result;

    } catch (error) {
      console.error('❌ Erreur envoi SMS de bienvenue:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Envoie les messages de bienvenue (email + SMS)
   */
  static async sendWelcomeMessages(userData) {
    const results = {
      email: { success: false },
      sms: { success: false }
    };

    try {
      // Envoi de l'email
      results.email = await this.sendWelcomeEmail(userData);

      // Envoi du SMS si un numéro de téléphone est disponible
      if (userData.user.phone) {
        results.sms = await this.sendWelcomeSMS(userData);
      } else {
        results.sms = { success: false, error: 'No phone number provided' };
      }

      const emailSuccess = results.email.success;
      const smsSuccess = results.sms.success || !userData.user.phone; // SMS optionnel

      console.log(`📊 Résultats envoi bienvenue pour ${userData.user.email}:`);
      console.log(`   Email: ${emailSuccess ? '✅' : '❌'}`);
      console.log(`   SMS: ${smsSuccess ? '✅' : '❌'}`);

      return {
        success: emailSuccess, // L'email est obligatoire
        results: results,
        summary: {
          email: emailSuccess,
          sms: smsSuccess,
          user: {
            name: `${userData.user.first_name} ${userData.user.last_name}`,
            email: userData.user.email,
            phone: userData.user.phone,
            role: userData.role
          }
        }
      };

    } catch (error) {
      console.error('❌ Erreur générale envoi messages de bienvenue:', error);
      return {
        success: false,
        error: error.message,
        results: results
      };
    }
  }

  /**
   * Obtient le template d'email selon le rôle
   */
  static getEmailTemplate(role) {
    const roleTemplates = {
      'super': 'superAdminWelcome',
      'admin': 'schoolAdminWelcome',
      'school_admin': 'schoolAdminWelcome',
      'dean_of_studies': 'schoolAdminWelcome',
      'bursar': 'schoolAdminWelcome',
      'teacher': 'teacherWelcome',
      'student': 'studentWelcome',
      'parent': 'parentWelcome'
    };

    return roleTemplates[role] || 'staffWelcome';
  }

  /**
   * Obtient le sujet de l'email selon le rôle
   */
  static getEmailSubject(role, schoolName, isNewUser = true) {
    const roleDisplay = this.getRoleDisplayName(role);
    
    if (isNewUser) {
      if (role === 'super') {
        return `🎉 Bienvenue sur Scholarify - Accès Super Administrateur`;
      } else if (['admin', 'school_admin', 'dean_of_studies', 'bursar'].includes(role)) {
        return `🎉 Bienvenue à ${schoolName} - Accès ${roleDisplay}`;
      } else {
        return `🎉 Bienvenue à ${schoolName} - Compte ${roleDisplay} créé`;
      }
    } else {
      return `🔐 Scholarify - Réinitialisation de mot de passe`;
    }
  }

  /**
   * Obtient le message SMS selon le rôle
   */
  static getSMSMessage(role, firstName, schoolName, resetUrl) {
    const roleDisplay = this.getRoleDisplayName(role);
    
    if (role === 'super') {
      return `Bienvenue ${firstName}! Vous êtes Super Admin sur Scholarify. Configurez votre mot de passe: ${resetUrl}`;
    } else if (['admin', 'school_admin', 'dean_of_studies', 'bursar'].includes(role)) {
      return `Bienvenue ${firstName}! Vous êtes ${roleDisplay} à ${schoolName}. Configurez votre mot de passe: ${resetUrl}`;
    } else {
      return `Bienvenue ${firstName}! Votre compte ${roleDisplay} à ${schoolName} est créé. Configurez votre mot de passe: ${resetUrl}`;
    }
  }
}

module.exports = WelcomeMessageService;
