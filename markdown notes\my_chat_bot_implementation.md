# Implémentation Chatbot Scholarify Multi-Dashboard avec n8n

## Vue d'ensemble de l'implémentation

Cette documentation détaille l'implémentation technique du chatbot Scholarify intégré avec n8n pour gérer la logique métier spécifique à chaque dashboard.

## Architecture Système

### Composants Principaux

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │      n8n        │    │   Backend API   │
│   (Next.js)     │◄──►│   Workflows     │◄──►│   (Node.js)     │
│                 │    │                 │    │                 │
│ ChatbotWidget   │    │ Router          │    │ MongoDB         │
│ Context Manager │    │ Super-Admin     │    │ Firebase Auth   │
│ Message Handler │    │ School-Admin    │    │ Scholarify APIs │
│                 │    │ Teacher         │    │                 │
│                 │    │ Counselor       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Flux de Données

1. **Utilisateur** → Message dans ChatbotWidget
2. **Frontend** → Enrichit avec contexte utilisateur
3. **n8n** → Route vers workflow approprié
4. **Workflow** → Traite la logique métier
5. **Backend APIs** → Exécute les actions
6. **n8n** → Formate la réponse
7. **Frontend** → Affiche la réponse + actions

## Structure des Dashboards

### 1. Super Admin Dashboard (`/super-admin/*`)

**Rôle** : `super`
**Capacités** : Toutes les fonctionnalités des autres dashboards + administration globale

**Navigation disponible** :
- Dashboard global
- Gestion des écoles
- Gestion des utilisateurs
- Subscriptions
- Classes globales
- Étudiants globaux
- Invitation des parents

**Fonctionnalités chatbot** :
```typescript
interface SuperAdminCapabilities {
  schoolManagement: {
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
    bulkOperations: boolean;
  };
  userManagement: {
    createAdmin: boolean;
    createTeacher: boolean;
    createCounselor: boolean;
    managePermissions: boolean;
  };
  globalAnalytics: {
    crossSchoolReports: boolean;
    systemMetrics: boolean;
    subscriptionAnalytics: boolean;
  };
  systemConfiguration: {
    globalSettings: boolean;
    featureFlags: boolean;
    maintenanceMode: boolean;
  };
}
```

### 2. School Admin Dashboard (`/school-admin/*`)

**Rôle** : `admin`
**Capacités** : Gestion complète d'une école spécifique

**Navigation disponible** :
- Dashboard école
- Informations école
- Classes de l'école
- Étudiants de l'école
- Professeurs de l'école
- Ressources scolaires
- Frais et paiements
- Parents de l'école

**Fonctionnalités chatbot** :
```typescript
interface SchoolAdminCapabilities {
  schoolScope: string; // ID de l'école
  classManagement: {
    create: boolean;
    assignTeachers: boolean;
    manageSchedules: boolean;
  };
  studentManagement: {
    enrollment: boolean;
    academicRecords: boolean;
    disciplinaryActions: boolean;
  };
  teacherManagement: {
    hire: boolean;
    assignClasses: boolean;
    performanceReview: boolean;
  };
  financialManagement: {
    feeStructure: boolean;
    paymentTracking: boolean;
    scholarships: boolean;
  };
}
```

### 3. Teacher Dashboard (`/teacher/*`)

**Rôle** : `teacher`
**Capacités** : Fonctionnalités pédagogiques et gestion de classe

**Navigation disponible** :
- Dashboard enseignant
- Mes classes
- Mes étudiants
- Notes et évaluations
- Présences
- Planning des cours
- Ressources pédagogiques
- Communication parents

**Fonctionnalités chatbot** :
```typescript
interface TeacherCapabilities {
  assignedClasses: string[]; // IDs des classes assignées
  gradeManagement: {
    enterGrades: boolean;
    generateReports: boolean;
    trackProgress: boolean;
  };
  attendanceManagement: {
    markAttendance: boolean;
    viewAttendanceReports: boolean;
    notifyParents: boolean;
  };
  lessonPlanning: {
    createLessonPlans: boolean;
    scheduleClasses: boolean;
    manageResources: boolean;
  };
  parentCommunication: {
    sendMessages: boolean;
    scheduleConferences: boolean;
    shareProgress: boolean;
  };
}
```

### 4. Counselor Dashboard (`/counselor/*`)

**Rôle** : `counselor`
**Capacités** : Enregistrement et suivi des étudiants

**Navigation disponible** :
- Dashboard conseiller
- Inscription étudiants
- Dossiers étudiants
- Suivi académique
- Suivi disciplinaire
- Communication familles
- Orientation scolaire
- Rapports individuels

**Fonctionnalités chatbot** :
```typescript
interface CounselorCapabilities {
  studentEnrollment: {
    newRegistrations: boolean;
    documentVerification: boolean;
    feeAssignment: boolean;
  };
  studentRecords: {
    academicHistory: boolean;
    disciplinaryRecords: boolean;
    healthRecords: boolean;
  };
  familyCommunication: {
    parentMeetings: boolean;
    progressUpdates: boolean;
    emergencyContacts: boolean;
  };
  guidanceServices: {
    careerCounseling: boolean;
    academicAdvising: boolean;
    personalSupport: boolean;
  };
}
```

## Implémentation Frontend

### 1. Contexte Utilisateur Automatique

```typescript
// hooks/useChatbotContext.ts
import { useAuth } from '@/app/hooks/useAuth';
import { useRouter } from 'next/navigation';

export interface ChatbotContext {
  user: {
    id: string;
    role: 'super' | 'admin' | 'teacher' | 'counselor';
    name: string;
    email: string;
    school_ids?: string[];
    permissions: string[];
  };
  dashboard: {
    type: 'super-admin' | 'school-admin' | 'teacher' | 'counselor';
    current_page: string;
    available_actions: string[];
  };
  session: {
    id: string;
    started_at: Date;
    last_activity: Date;
  };
}

export const useChatbotContext = (): ChatbotContext => {
  const { user } = useAuth();
  const router = useRouter();

  const getDashboardType = (pathname: string) => {
    if (pathname.startsWith('/super-admin')) return 'super-admin';
    if (pathname.startsWith('/school-admin')) return 'school-admin';
    if (pathname.startsWith('/teacher')) return 'teacher';
    if (pathname.startsWith('/counselor')) return 'counselor';
    return 'super-admin'; // default
  };

  const getAvailableActions = (dashboardType: string, role: string) => {
    // Logic to determine available actions based on dashboard and role
    const actionMap = {
      'super-admin': ['create_school', 'manage_users', 'view_analytics', 'system_config'],
      'school-admin': ['manage_classes', 'manage_students', 'manage_teachers', 'view_reports'],
      'teacher': ['manage_grades', 'mark_attendance', 'plan_lessons', 'communicate_parents'],
      'counselor': ['enroll_students', 'manage_records', 'schedule_meetings', 'provide_guidance']
    };
    return actionMap[dashboardType] || [];
  };

  return {
    user: {
      id: user?._id || '',
      role: user?.role || 'super',
      name: user?.name || '',
      email: user?.email || '',
      school_ids: user?.school_ids || [],
      permissions: user?.permissions || []
    },
    dashboard: {
      type: getDashboardType(router.pathname),
      current_page: router.pathname,
      available_actions: getAvailableActions(getDashboardType(router.pathname), user?.role || 'super')
    },
    session: {
      id: `session_${Date.now()}`,
      started_at: new Date(),
      last_activity: new Date()
    }
  };
};
```

### 2. Service de Communication n8n

```typescript
// services/ChatbotService.ts
import { ChatbotContext } from '@/hooks/useChatbotContext';

export interface ChatbotMessage {
  message: string;
  context: ChatbotContext;
  timestamp: Date;
}

export interface ChatbotResponse {
  response: string;
  actions?: ChatbotAction[];
  suggestions?: string[];
  metadata?: {
    confidence: number;
    processing_time: number;
    workflow_used: string;
  };
}

export interface ChatbotAction {
  type: 'navigate' | 'open_modal' | 'execute_function' | 'show_data';
  target: string;
  params?: Record<string, any>;
}

class ChatbotService {
  private n8nWebhookUrl: string;

  constructor() {
    this.n8nWebhookUrl = process.env.NEXT_PUBLIC_N8N_WEBHOOK_URL || 'http://localhost:5678/webhook/chatbot';
  }

  async sendMessage(message: string, context: ChatbotContext): Promise<ChatbotResponse> {
    try {
      const payload: ChatbotMessage = {
        message,
        context,
        timestamp: new Date()
      };

      const response = await fetch(this.n8nWebhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data as ChatbotResponse;
    } catch (error) {
      console.error('Chatbot service error:', error);
      return {
        response: "Désolé, je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants.",
        suggestions: ["Rafraîchir la page", "Contacter le support"]
      };
    }
  }

  private getAuthToken(): string {
    // Get token from localStorage or auth context
    return localStorage.getItem('idToken') || '';
  }
}

export const chatbotService = new ChatbotService();
```

### 3. Composant ChatbotWidget Amélioré

```typescript
// components/chatbot/ChatbotWidget.tsx
'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X, Sparkles } from 'lucide-react';
import { useChatbotContext } from '@/hooks/useChatbotContext';
import { chatbotService, ChatbotResponse } from '@/services/ChatbotService';
import ChatHeader from './ChatHeader';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';
import ThinkingIndicator from './ThinkingIndicator';
import ActionButtons from './ActionButtons';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  actions?: any[];
  suggestions?: string[];
}

const ChatbotWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isThinking, setIsThinking] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const context = useChatbotContext();

  // Welcome message based on dashboard type
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage = getWelcomeMessage(context.dashboard.type, context.user.role);
      setMessages([{
        id: 'welcome',
        content: welcomeMessage.content,
        role: 'assistant',
        timestamp: new Date(),
        suggestions: welcomeMessage.suggestions
      }]);
    }
  }, [isOpen, context.dashboard.type, context.user.role]);

  const getWelcomeMessage = (dashboardType: string, role: string) => {
    const messages = {
      'super-admin': {
        content: `Bonjour ! Je suis votre assistant Scholarify pour le dashboard Super Admin. Je peux vous aider avec la gestion globale des écoles, des utilisateurs, des subscriptions et bien plus encore.`,
        suggestions: [
          "Créer une nouvelle école",
          "Voir les statistiques globales",
          "Gérer les utilisateurs",
          "Vérifier les subscriptions"
        ]
      },
      'school-admin': {
        content: `Bonjour ! Je suis votre assistant pour la gestion de votre école. Je peux vous aider avec les classes, les étudiants, les professeurs et les rapports.`,
        suggestions: [
          "Ajouter une nouvelle classe",
          "Voir les étudiants non payés",
          "Créer un compte professeur",
          "Générer un rapport"
        ]
      },
      'teacher': {
        content: `Bonjour ! Je suis votre assistant pédagogique. Je peux vous aider avec les notes, les présences, la planification des cours et la communication avec les parents.`,
        suggestions: [
          "Marquer les présences",
          "Ajouter des notes",
          "Voir mon planning",
          "Contacter les parents"
        ]
      },
      'counselor': {
        content: `Bonjour ! Je suis votre assistant pour le conseil scolaire. Je peux vous aider avec l'inscription des étudiants, le suivi des dossiers et l'orientation.`,
        suggestions: [
          "Inscrire un nouvel étudiant",
          "Voir un dossier étudiant",
          "Planifier un rendez-vous",
          "Ajouter une note"
        ]
      }
    };

    return messages[dashboardType] || messages['super-admin'];
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setIsThinking(true);

    try {
      const response: ChatbotResponse = await chatbotService.sendMessage(content, context);

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.response,
        role: 'assistant',
        timestamp: new Date(),
        actions: response.actions,
        suggestions: response.suggestions
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Désolé, une erreur est survenue. Veuillez réessayer.',
        role: 'assistant',
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsThinking(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const handleActionClick = (action: any) => {
    // Handle different action types
    switch (action.type) {
      case 'navigate':
        window.location.href = action.target;
        break;
      case 'open_modal':
        // Trigger modal opening logic
        console.log('Opening modal:', action.target);
        break;
      case 'execute_function':
        // Execute specific function
        console.log('Executing function:', action.target);
        break;
      default:
        console.log('Unknown action type:', action.type);
    }
  };

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isThinking]);

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Chat Toggle Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.button
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setIsOpen(true)}
            className="bg-teal-600 hover:bg-teal-700 text-white rounded-full p-4 shadow-lg transition-colors duration-200 relative"
          >
            <MessageCircle size={24} />
            {/* Dashboard indicator */}
            <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full px-1.5 py-0.5 font-bold">
              {context.dashboard.type.split('-')[0].toUpperCase()}
            </div>
          </motion.button>
        )}
      </AnimatePresence>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl w-80 h-96 flex flex-col border border-gray-200 dark:border-gray-700"
          >
            <ChatHeader
              onClose={() => setIsOpen(false)}
              dashboardType={context.dashboard.type}
              userName={context.user.name}
            />

            <div className="flex-1 overflow-y-auto p-4 space-y-4
              scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600
              scrollbar-track-transparent hover:scrollbar-thumb-gray-400
              dark:hover:scrollbar-thumb-gray-500"
            >
              {messages.map((message) => (
                <div key={message.id}>
                  <ChatMessage message={message} />
                  {message.actions && (
                    <ActionButtons
                      actions={message.actions}
                      onActionClick={handleActionClick}
                    />
                  )}
                  {message.suggestions && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {message.suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion)}
                          className="text-xs bg-teal-100 dark:bg-teal-900 text-teal-700 dark:text-teal-300 px-2 py-1 rounded-full hover:bg-teal-200 dark:hover:bg-teal-800 transition-colors"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              {isThinking && <ThinkingIndicator />}
              <div ref={messagesEndRef} />
            </div>

            <ChatInput onSend={handleSendMessage} disabled={isThinking} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ChatbotWidget;
```

### 4. Composants Supplémentaires

#### ChatHeader avec Indicateur Dashboard

```typescript
// components/chatbot/ChatHeader.tsx
'use client';

import React from 'react';
import { X, Crown, School, GraduationCap, Users } from 'lucide-react';

interface ChatHeaderProps {
  onClose: () => void;
  dashboardType: string;
  userName: string;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ onClose, dashboardType, userName }) => {
  const getDashboardIcon = (type: string) => {
    switch (type) {
      case 'super-admin': return Crown;
      case 'school-admin': return School;
      case 'teacher': return GraduationCap;
      case 'counselor': return Users;
      default: return Crown;
    }
  };

  const getDashboardTitle = (type: string) => {
    switch (type) {
      case 'super-admin': return 'Super Admin Assistant';
      case 'school-admin': return 'School Admin Assistant';
      case 'teacher': return 'Teacher Assistant';
      case 'counselor': return 'Counselor Assistant';
      default: return 'Scholarify Assistant';
    }
  };

  const getDashboardColor = (type: string) => {
    switch (type) {
      case 'super-admin': return 'text-purple-600 bg-purple-100';
      case 'school-admin': return 'text-blue-600 bg-blue-100';
      case 'teacher': return 'text-green-600 bg-green-100';
      case 'counselor': return 'text-orange-600 bg-orange-100';
      default: return 'text-teal-600 bg-teal-100';
    }
  };

  const Icon = getDashboardIcon(dashboardType);

  return (
    <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-teal-50 to-blue-50 dark:from-gray-800 dark:to-gray-700">
      <div className="flex items-center space-x-3">
        <div className={`p-2 rounded-full ${getDashboardColor(dashboardType)}`}>
          <Icon size={16} />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
            {getDashboardTitle(dashboardType)}
          </h3>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Hello, {userName}
          </p>
        </div>
      </div>
      <button
        onClick={onClose}
        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
      >
        <X size={18} />
      </button>
    </div>
  );
};

export default ChatHeader;
```

#### ActionButtons Component

```typescript
// components/chatbot/ActionButtons.tsx
'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Plus, Eye, Edit, Trash2 } from 'lucide-react';

interface ActionButtonsProps {
  actions: any[];
  onActionClick: (action: any) => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ actions, onActionClick }) => {
  const getActionIcon = (type: string) => {
    switch (type) {
      case 'navigate': return ExternalLink;
      case 'open_modal': return Plus;
      case 'show_data': return Eye;
      case 'execute_function': return Edit;
      default: return Plus;
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case 'navigate': return 'bg-blue-500 hover:bg-blue-600';
      case 'open_modal': return 'bg-green-500 hover:bg-green-600';
      case 'show_data': return 'bg-purple-500 hover:bg-purple-600';
      case 'execute_function': return 'bg-orange-500 hover:bg-orange-600';
      default: return 'bg-teal-500 hover:bg-teal-600';
    }
  };

  return (
    <div className="mt-3 flex flex-wrap gap-2">
      {actions.map((action, index) => {
        const Icon = getActionIcon(action.type);
        return (
          <motion.button
            key={index}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onActionClick(action)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-white text-sm font-medium transition-colors ${getActionColor(action.type)}`}
          >
            <Icon size={14} />
            <span>{action.label || action.target}</span>
          </motion.button>
        );
      })}
    </div>
  );
};

export default ActionButtons;
```

## Configuration n8n

### 1. Workflow Principal (Router)

```json
{
  "name": "Scholarify Chatbot Router",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "chatbot",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-trigger",
      "name": "Webhook Trigger",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "functionCode": "// Extract user context and route to appropriate workflow\nconst payload = $input.first().json;\nconst { message, context } = payload;\n\n// Determine target workflow based on dashboard type\nconst workflowMap = {\n  'super-admin': 'super-admin-workflow',\n  'school-admin': 'school-admin-workflow', \n  'teacher': 'teacher-workflow',\n  'counselor': 'counselor-workflow'\n};\n\nconst targetWorkflow = workflowMap[context.dashboard.type] || 'super-admin-workflow';\n\n// Add routing information\nreturn {\n  message,\n  context,\n  targetWorkflow,\n  timestamp: new Date().toISOString()\n};"
      },
      "id": "router-function",
      "name": "Router Function",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.targetWorkflow}}",
              "operation": "equal",
              "value2": "super-admin-workflow"
            }
          ]
        }
      },
      "id": "super-admin-switch",
      "name": "Super Admin Switch",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 1,
      "position": [680, 200]
    }
  ],
  "connections": {
    "Webhook Trigger": {
      "main": [
        [
          {
            "node": "Router Function",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Router Function": {
      "main": [
        [
          {
            "node": "Super Admin Switch",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

### 2. Workflow Super Admin

```json
{
  "name": "Super Admin Workflow",
  "nodes": [
    {
      "parameters": {
        "functionCode": "// Process super admin requests\nconst { message, context } = $input.first().json;\nconst userMessage = message.toLowerCase();\n\n// Intent detection for super admin\nlet intent = 'general';\nlet response = '';\nlet actions = [];\nlet suggestions = [];\n\nif (userMessage.includes('créer') && userMessage.includes('école')) {\n  intent = 'create_school';\n  response = 'Je vais vous aider à créer une nouvelle école. Cliquez sur le bouton ci-dessous pour ouvrir le formulaire de création.';\n  actions = [{\n    type: 'open_modal',\n    target: 'CreateSchoolModal',\n    label: 'Créer École'\n  }];\n  suggestions = ['Voir la liste des écoles', 'Gérer les utilisateurs'];\n} else if (userMessage.includes('statistiques') || userMessage.includes('analytics')) {\n  intent = 'view_analytics';\n  response = 'Voici un aperçu des statistiques globales de la plateforme.';\n  actions = [{\n    type: 'navigate',\n    target: '/super-admin/dashboard',\n    label: 'Voir Dashboard'\n  }];\n  suggestions = ['Rapports détaillés', 'Métriques par école'];\n} else if (userMessage.includes('utilisateur') || userMessage.includes('user')) {\n  intent = 'manage_users';\n  response = 'Je peux vous aider avec la gestion des utilisateurs. Que souhaitez-vous faire ?';\n  actions = [{\n    type: 'navigate',\n    target: '/super-admin/users',\n    label: 'Gérer Utilisateurs'\n  }];\n  suggestions = ['Créer un admin', 'Créer un professeur', 'Voir les utilisateurs'];\n} else {\n  response = 'En tant que Super Admin, vous avez accès à toutes les fonctionnalités. Comment puis-je vous aider ?';\n  suggestions = ['Créer une école', 'Voir les statistiques', 'Gérer les utilisateurs', 'Vérifier les subscriptions'];\n}\n\nreturn {\n  response,\n  actions,\n  suggestions,\n  metadata: {\n    intent,\n    confidence: 0.8,\n    processing_time: Date.now() - new Date(context.timestamp).getTime(),\n    workflow_used: 'super-admin-workflow'\n  }\n};"
      },
      "id": "super-admin-processor",
      "name": "Super Admin Processor",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{$json}}"
      },
      "id": "response-node",
      "name": "Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [460, 300]
    }
  ],
  "connections": {
    "Super Admin Processor": {
      "main": [
        [
          {
            "node": "Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

### 3. Variables d'Environnement

```env
# .env.local (Frontend)
NEXT_PUBLIC_N8N_WEBHOOK_URL=http://localhost:5678/webhook/chatbot
NEXT_PUBLIC_N8N_API_KEY=your_n8n_api_key

# n8n Environment
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your_password
WEBHOOK_URL=http://localhost:3000
DB_TYPE=sqlite
```

## Déploiement et Configuration

### 1. Setup n8n Local

```bash
# Installation n8n
npm install -g n8n

# Démarrage n8n
n8n start

# Accès interface: http://localhost:5678
```

### 2. Configuration Webhooks

1. **Créer le webhook principal** : `/webhook/chatbot`
2. **Configurer l'authentification** : Bearer token
3. **Tester la connectivité** : Frontend → n8n

### 3. Import des Workflows

```bash
# Export depuis n8n UI
# Import dans nouvelle instance
# Configuration des credentials
```

## Tests et Validation

### 1. Tests Unitaires Frontend

```typescript
// __tests__/chatbot/ChatbotWidget.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ChatbotWidget from '@/components/chatbot/ChatbotWidget';
import { useChatbotContext } from '@/hooks/useChatbotContext';

jest.mock('@/hooks/useChatbotContext');
jest.mock('@/services/ChatbotService');

describe('ChatbotWidget', () => {
  beforeEach(() => {
    (useChatbotContext as jest.Mock).mockReturnValue({
      user: { role: 'super', name: 'Test User' },
      dashboard: { type: 'super-admin' },
      session: { id: 'test-session' }
    });
  });

  it('shows correct welcome message for super admin', () => {
    render(<ChatbotWidget />);
    fireEvent.click(screen.getByRole('button'));

    expect(screen.getByText(/Super Admin/)).toBeInTheDocument();
    expect(screen.getByText(/Créer une nouvelle école/)).toBeInTheDocument();
  });

  it('sends message to n8n service', async () => {
    render(<ChatbotWidget />);
    fireEvent.click(screen.getByRole('button'));

    const input = screen.getByPlaceholderText(/Tapez votre message/);
    fireEvent.change(input, { target: { value: 'Créer une école' } });
    fireEvent.submit(input.closest('form'));

    await waitFor(() => {
      expect(chatbotService.sendMessage).toHaveBeenCalledWith(
        'Créer une école',
        expect.objectContaining({
          dashboard: { type: 'super-admin' }
        })
      );
    });
  });
});
```

### 2. Tests d'Intégration n8n

```javascript
// n8n workflow test
const testPayload = {
  message: "Créer une nouvelle école",
  context: {
    user: { role: "super", id: "test-user" },
    dashboard: { type: "super-admin" }
  }
};

// Test webhook response
const response = await fetch('http://localhost:5678/webhook/chatbot', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(testPayload)
});

const result = await response.json();
expect(result.response).toContain('créer une nouvelle école');
expect(result.actions).toHaveLength(1);
expect(result.actions[0].type).toBe('open_modal');
```

## Monitoring et Analytics

### 1. Métriques Chatbot

```typescript
// services/ChatbotAnalytics.ts
export class ChatbotAnalytics {
  static trackMessage(message: string, context: ChatbotContext) {
    // Track user interactions
    analytics.track('chatbot_message_sent', {
      dashboard_type: context.dashboard.type,
      user_role: context.user.role,
      message_length: message.length,
      timestamp: new Date()
    });
  }

  static trackResponse(response: ChatbotResponse, processingTime: number) {
    // Track response quality
    analytics.track('chatbot_response_generated', {
      workflow_used: response.metadata?.workflow_used,
      processing_time: processingTime,
      has_actions: response.actions?.length > 0,
      has_suggestions: response.suggestions?.length > 0
    });
  }
}
```

### 2. Dashboard Monitoring

```typescript
// hooks/useChatbotMetrics.ts
export const useChatbotMetrics = () => {
  const [metrics, setMetrics] = useState({
    totalMessages: 0,
    averageResponseTime: 0,
    successRate: 0,
    popularActions: []
  });

  useEffect(() => {
    // Fetch metrics from analytics service
    fetchChatbotMetrics().then(setMetrics);
  }, []);

  return metrics;
};
```

## Implémentation du Workflow n8n - Guide Étape par Étape

Cette section détaille l'implémentation pratique du workflow n8n pour intégrer le chatbot avec la documentation et les APIs Scholarify.

### Architecture du Workflow n8n

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Webhook       │    │   AI Agent      │    │   Memory        │    │   Tool          │
│   (Trigger)     │───►│   (Model)       │◄──►│   (Context)     │◄──►│   (APIs)        │
│                 │    │                 │    │                 │    │                 │
│ Reçoit message  │    │ Claude/GPT      │    │ Documentation   │    │ Scholarify APIs │
│ + contexte      │    │ + Instructions  │    │ + Historique    │    │ + Actions       │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Response      │
                       │   (Output)      │
                       │                 │
                       │ Réponse +       │
                       │ Actions +       │
                       │ Suggestions     │
                       └─────────────────┘
```

### Étape 1 : Configuration de l'environnement n8n

#### 1.1 Installation et démarrage de n8n
```bash
# Installation globale de n8n
npm install -g n8n

# Ou installation locale dans le projet
cd dashboard
npm install n8n

# Démarrage de n8n
n8n start

# Accès à l'interface : http://localhost:5678
```

#### 1.2 Configuration des variables d'environnement
```env
# Fichier .env pour n8n
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=scholarify_admin_2024

# URLs et endpoints
SCHOLARIFY_API_BASE_URL=http://localhost:5000/api
SCHOLARIFY_FRONTEND_URL=http://localhost:3001
WEBHOOK_URL=http://localhost:5678/webhook

# Clés API
OPENAI_API_KEY=your_openai_api_key
CLAUDE_API_KEY=your_claude_api_key

# Base de données pour la mémoire
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/scholarify_chatbot
```

### Étape 2 : Création du Workflow Principal (Router)

#### 2.1 Webhook Trigger Node
```json
{
  "name": "Scholarify Chatbot Webhook",
  "type": "n8n-nodes-base.webhook",
  "parameters": {
    "httpMethod": "POST",
    "path": "scholarify-chatbot",
    "responseMode": "responseNode",
    "options": {
      "allowedOrigins": "http://localhost:3001,https://your-domain.com"
    }
  }
}
```

#### 2.2 Context Validation Node
```javascript
// Node Function: Validation du contexte
const payload = $input.first().json;

// Validation des données requises
if (!payload.message || !payload.context || !payload.context.user) {
  return [{
    json: {
      error: "Données manquantes dans la requête",
      response: "Désolé, je n'ai pas reçu toutes les informations nécessaires.",
      suggestions: ["Rafraîchir la page", "Réessayer"]
    }
  }];
}

// Extraction et validation du contexte utilisateur
const { message, context } = payload;
const { user, dashboard, session } = context;

// Détermination du workflow cible selon le rôle
const workflowMap = {
  'super': 'super-admin-agent',
  'admin': 'school-admin-agent',
  'teacher': 'teacher-agent',
  'counselor': 'counselor-agent',
  'parent': 'parent-agent'
};

const targetWorkflow = workflowMap[user.role] || 'general-agent';

// Préparation des données pour l'AI Agent
return [{
  json: {
    message: message.trim(),
    user_role: user.role,
    user_id: user.id,
    user_name: user.name,
    dashboard_type: dashboard.type,
    current_page: dashboard.current_page,
    available_actions: dashboard.available_actions,
    school_ids: user.school_ids || [],
    permissions: user.permissions || [],
    session_id: session.id,
    target_workflow: targetWorkflow,
    timestamp: new Date().toISOString(),
    language: detectLanguage(message) // Détection de la langue
  }
}];

// Fonction de détection de langue simple
function detectLanguage(text) {
  const frenchWords = ['le', 'la', 'les', 'de', 'du', 'des', 'et', 'ou', 'est', 'sont', 'avoir', 'être'];
  const englishWords = ['the', 'and', 'or', 'is', 'are', 'have', 'has', 'been', 'will', 'would'];

  const words = text.toLowerCase().split(' ');
  let frenchScore = 0;
  let englishScore = 0;

  words.forEach(word => {
    if (frenchWords.includes(word)) frenchScore++;
    if (englishWords.includes(word)) englishScore++;
  });

  return frenchScore > englishScore ? 'fr' : 'en';
}
```

### Étape 3 : Configuration de l'AI Agent (Model Node)

#### 3.1 AI Agent Node Configuration
```json
{
  "name": "Scholarify AI Agent",
  "type": "n8n-nodes-base.openAi",
  "parameters": {
    "resource": "chat",
    "operation": "message",
    "model": "gpt-4-turbo-preview",
    "messages": {
      "values": [
        {
          "role": "system",
          "content": "={{$node['Load AI Context'].json['system_prompt']}}"
        },
        {
          "role": "user",
          "content": "={{$json['message']}}"
        }
      ]
    },
    "options": {
      "temperature": 0.7,
      "maxTokens": 1000,
      "topP": 1,
      "frequencyPenalty": 0,
      "presencePenalty": 0
    }
  }
}
```

#### 3.2 Load AI Context Node
```javascript
// Node Function: Chargement du contexte AI
const data = $input.first().json;

// Chargement du contexte de base depuis ai_agent_context.md
const baseContext = `
# Contexte AI Agent - Scholarify Chatbot

Vous êtes l'assistant IA de Scholarify, une plateforme de gestion scolaire.

## Utilisateur actuel
- Rôle: ${data.user_role}
- Nom: ${data.user_name}
- Dashboard: ${data.dashboard_type}
- Page actuelle: ${data.current_page}
- Langue détectée: ${data.language}

## Instructions de langue
- Répondez TOUJOURS dans la langue du message de l'utilisateur
- Si français détecté, répondez en français
- Si anglais détecté, répondez en anglais
- Même si la documentation est dans une autre langue, traduisez votre réponse

## Permissions utilisateur
${JSON.stringify(data.permissions, null, 2)}

## Actions disponibles
${JSON.stringify(data.available_actions, null, 2)}
`;

// Chargement de la documentation spécifique au rôle
let roleSpecificContext = '';
switch(data.user_role) {
  case 'super':
    roleSpecificContext = loadSuperAdminContext();
    break;
  case 'admin':
    roleSpecificContext = loadSchoolAdminContext();
    break;
  case 'teacher':
    roleSpecificContext = loadTeacherContext();
    break;
  case 'counselor':
    roleSpecificContext = loadCounselorContext();
    break;
  case 'parent':
    roleSpecificContext = loadParentContext();
    break;
}

const systemPrompt = baseContext + '\n\n' + roleSpecificContext + `

## Format de réponse requis
Répondez UNIQUEMENT en JSON avec cette structure:
{
  "response": "Votre réponse en ${data.language === 'fr' ? 'français' : 'anglais'}",
  "actions": [
    {
      "type": "navigate|open_modal|execute_function|show_data",
      "target": "URL ou fonction",
      "label": "Texte du bouton",
      "params": {}
    }
  ],
  "suggestions": ["Suggestion 1", "Suggestion 2", "Suggestion 3"],
  "metadata": {
    "intent": "intention_detectee",
    "confidence": 0.85,
    "language": "${data.language}"
  }
}
`;

return [{
  json: {
    system_prompt: systemPrompt,
    user_context: data
  }
}];

// Fonctions de chargement de contexte par rôle
function loadSuperAdminContext() {
  return `
## Super Admin - Accès complet
Vous avez accès à TOUTES les fonctionnalités:
- Gestion globale des écoles
- Gestion de tous les utilisateurs
- Analytics et rapports globaux
- Configuration système
- Gestion des subscriptions

Documentation accessible: Tous les dossiers et fichiers
  `;
}

function loadSchoolAdminContext() {
  return `
## School Admin - Gestion d'école
Vous gérez une école spécifique:
- Classes et niveaux de votre école
- Étudiants de votre école
- Professeurs et staff
- Ressources scolaires
- Frais et paiements

Documentation accessible: Fonctionnalités école uniquement
  `;
}

function loadTeacherContext() {
  return `
## Teacher - Fonctionnalités pédagogiques
Vous êtes enseignant avec accès à:
- Gestion des notes et évaluations
- Marquage des présences
- Planification des cours
- Communication avec parents
- Ressources pédagogiques

Documentation accessible: Outils d'enseignement uniquement
  `;
}

function loadCounselorContext() {
  return `
## Counselor - Suivi des étudiants
Vous êtes conseiller avec accès à:
- Inscription de nouveaux étudiants
- Gestion des dossiers étudiants
- Suivi académique et disciplinaire
- Communication avec familles
- Orientation scolaire

Documentation accessible: Outils de conseil uniquement
  `;
}

function loadParentContext() {
  return `
## Parent - Suivi des enfants
Vous êtes parent avec accès à:
- Notes et évaluations de vos enfants
- Suivi des présences
- Communication avec professeurs
- Paiement des frais scolaires
- Consultation des annonces

Documentation accessible: Informations de vos enfants uniquement
  `;
}
```

### Étape 4 : Configuration de la Mémoire (Memory Node)

#### 4.1 Memory Storage Node
```javascript
// Node Function: Gestion de la mémoire conversationnelle
const data = $input.first().json;
const aiResponse = $input.last().json;

// Connexion à MongoDB pour stocker l'historique
const { MongoClient } = require('mongodb');
const client = new MongoClient(process.env.MONGODB_CONNECTION_STRING);

try {
  await client.connect();
  const db = client.db('scholarify_chatbot');
  const collection = db.collection('conversation_history');

  // Sauvegarde de l'interaction
  const interaction = {
    session_id: data.session_id,
    user_id: data.user_id,
    user_role: data.user_role,
    message: data.message,
    response: aiResponse.response,
    actions: aiResponse.actions || [],
    suggestions: aiResponse.suggestions || [],
    metadata: aiResponse.metadata || {},
    timestamp: new Date(),
    language: data.language
  };

  await collection.insertOne(interaction);

  // Récupération de l'historique récent (dernières 10 interactions)
  const recentHistory = await collection
    .find({ session_id: data.session_id })
    .sort({ timestamp: -1 })
    .limit(10)
    .toArray();

  return [{
    json: {
      ...aiResponse,
      conversation_history: recentHistory.reverse(),
      memory_updated: true
    }
  }];

} catch (error) {
  console.error('Erreur mémoire:', error);
  return [{
    json: {
      ...aiResponse,
      memory_error: error.message
    }
  }];
} finally {
  await client.close();
}
```

### Étape 5 : Configuration des Outils (Tool Nodes)

#### 5.1 API Tool Node
```javascript
// Node Function: Intégration avec les APIs Scholarify
const data = $input.first().json;
const aiResponse = $input.last().json;

// Extraction des actions à exécuter
const actions = aiResponse.actions || [];
const apiCalls = [];

for (const action of actions) {
  if (action.type === 'execute_function' && action.target.startsWith('api_')) {
    const apiCall = await executeScholarifyAPI(action, data);
    apiCalls.push(apiCall);
  }
}

return [{
  json: {
    ...aiResponse,
    api_results: apiCalls,
    tools_executed: apiCalls.length
  }
}];

async function executeScholarifyAPI(action, context) {
  const baseURL = process.env.SCHOLARIFY_API_BASE_URL;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${context.auth_token}`
  };

  try {
    switch (action.target) {
      case 'api_get_schools':
        if (context.user_role === 'super') {
          const response = await fetch(`${baseURL}/school/list`, { headers });
          return { action: action.target, data: await response.json() };
        }
        break;

      case 'api_get_students':
        const schoolFilter = context.user_role === 'super' ? '' : `?school_id=${context.school_ids[0]}`;
        const response = await fetch(`${baseURL}/student/list${schoolFilter}`, { headers });
        return { action: action.target, data: await response.json() };

      case 'api_mark_attendance':
        if (context.user_role === 'teacher') {
          const response = await fetch(`${baseURL}/attendance/mark`, {
            method: 'POST',
            headers,
            body: JSON.stringify(action.params)
          });
          return { action: action.target, data: await response.json() };
        }
        break;

      default:
        return { action: action.target, error: 'API non supportée' };
    }
  } catch (error) {
    return { action: action.target, error: error.message };
  }
}
```

### Étape 6 : Configuration de la Sortie (Output Node)

#### 6.1 Response Formatter Node
```javascript
// Node Function: Formatage de la réponse finale
const data = $input.first().json;

// Validation et nettoyage de la réponse
let response = data.response || "Désolé, je n'ai pas pu traiter votre demande.";
let actions = data.actions || [];
let suggestions = data.suggestions || [];

// Ajout d'actions contextuelles selon le rôle
const contextualActions = getContextualActions(data.user_role, data.dashboard_type);
actions = [...actions, ...contextualActions];

// Limitation du nombre de suggestions
suggestions = suggestions.slice(0, 4);

// Ajout de suggestions par défaut si aucune n'est fournie
if (suggestions.length === 0) {
  suggestions = getDefaultSuggestions(data.user_role, data.language);
}

// Formatage final de la réponse
const finalResponse = {
  response: response,
  actions: actions.slice(0, 3), // Maximum 3 actions
  suggestions: suggestions,
  metadata: {
    intent: data.metadata?.intent || 'general',
    confidence: data.metadata?.confidence || 0.8,
    processing_time: Date.now() - new Date(data.timestamp).getTime(),
    workflow_used: `${data.user_role}-agent`,
    language: data.language || 'fr',
    user_role: data.user_role,
    session_id: data.session_id
  }
};

return [{ json: finalResponse }];

function getContextualActions(role, dashboardType) {
  const actionMap = {
    'super': [
      { type: 'navigate', target: '/super-admin/dashboard', label: 'Dashboard' },
      { type: 'navigate', target: '/super-admin/schools', label: 'Écoles' }
    ],
    'admin': [
      { type: 'navigate', target: '/school-admin/dashboard', label: 'Dashboard' },
      { type: 'navigate', target: '/school-admin/students', label: 'Étudiants' }
    ],
    'teacher': [
      { type: 'navigate', target: '/teacher/classes', label: 'Mes Classes' },
      { type: 'navigate', target: '/teacher/grades', label: 'Notes' }
    ],
    'counselor': [
      { type: 'navigate', target: '/counselor/students', label: 'Étudiants' },
      { type: 'navigate', target: '/counselor/enrollment', label: 'Inscription' }
    ],
    'parent': [
      { type: 'navigate', target: '/parent/children', label: 'Mes Enfants' },
      { type: 'navigate', target: '/parent/payments', label: 'Paiements' }
    ]
  };

  return actionMap[role] || [];
}

function getDefaultSuggestions(role, language) {
  const suggestions = {
    'super': {
      'fr': ['Créer une école', 'Voir les statistiques', 'Gérer les utilisateurs', 'Vérifier les subscriptions'],
      'en': ['Create a school', 'View statistics', 'Manage users', 'Check subscriptions']
    },
    'admin': {
      'fr': ['Ajouter une classe', 'Voir les étudiants', 'Créer un professeur', 'Générer un rapport'],
      'en': ['Add a class', 'View students', 'Create a teacher', 'Generate a report']
    },
    'teacher': {
      'fr': ['Marquer les présences', 'Ajouter des notes', 'Voir mon planning', 'Contacter les parents'],
      'en': ['Mark attendance', 'Add grades', 'View my schedule', 'Contact parents']
    },
    'counselor': {
      'fr': ['Inscrire un étudiant', 'Voir un dossier', 'Planifier un rendez-vous', 'Ajouter une note'],
      'en': ['Enroll a student', 'View a file', 'Schedule an appointment', 'Add a note']
    },
    'parent': {
      'fr': ['Voir les notes', 'Vérifier les absences', 'Contacter le professeur', 'Payer les frais'],
      'en': ['View grades', 'Check absences', 'Contact teacher', 'Pay fees']
    }
  };

  return suggestions[role]?.[language] || suggestions[role]?.['fr'] || [];
}
```

#### 6.2 Response Node (Final Output)
```json
{
  "name": "Send Response",
  "type": "n8n-nodes-base.respondToWebhook",
  "parameters": {
    "respondWith": "json",
    "responseBody": "={{$json}}",
    "options": {
      "responseHeaders": {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization"
      }
    }
  }
}
```

### Étape 7 : Tests et Validation

#### 7.1 Test du Workflow
```bash
# Test avec curl
curl -X POST http://localhost:5678/webhook/scholarify-chatbot \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Créer une nouvelle école",
    "context": {
      "user": {
        "id": "test-user",
        "role": "super",
        "name": "Test User",
        "email": "<EMAIL>",
        "permissions": ["create_school"]
      },
      "dashboard": {
        "type": "super-admin",
        "current_page": "/super-admin/schools",
        "available_actions": ["create_school"]
      },
      "session": {
        "id": "test-session",
        "started_at": "2024-01-01T00:00:00Z",
        "last_activity": "2024-01-01T00:00:00Z"
      }
    },
    "timestamp": "2024-01-01T00:00:00Z"
  }'
```

#### 7.2 Validation des Réponses
```javascript
// Script de test automatisé
const testCases = [
  {
    role: 'super',
    message: 'Créer une école',
    expectedActions: ['open_modal'],
    expectedLanguage: 'fr'
  },
  {
    role: 'teacher',
    message: 'Mark attendance for my class',
    expectedActions: ['navigate'],
    expectedLanguage: 'en'
  },
  {
    role: 'parent',
    message: 'Voir les notes de mon enfant',
    expectedActions: ['show_data'],
    expectedLanguage: 'fr'
  }
];

// Exécution des tests
for (const testCase of testCases) {
  const response = await testWorkflow(testCase);
  console.log(`Test ${testCase.role}: ${response.success ? 'PASS' : 'FAIL'}`);
}
```

### Étape 8 : Déploiement et Monitoring

#### 8.1 Configuration de Production
```env
# Variables de production
N8N_HOST=0.0.0.0
N8N_PORT=5678
N8N_PROTOCOL=https
N8N_ENCRYPTION_KEY=your_encryption_key

# Base de données
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=your_postgres_host
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=n8n_user
DB_POSTGRESDB_PASSWORD=your_password

# Monitoring
N8N_METRICS=true
N8N_LOG_LEVEL=info
```

#### 8.2 Monitoring et Logs
```javascript
// Node Function: Logging et monitoring
const data = $input.first().json;

// Log de l'interaction
console.log(`[${new Date().toISOString()}] Chatbot Interaction:`, {
  user_id: data.user_id,
  user_role: data.user_role,
  message_length: data.message.length,
  language: data.language,
  processing_time: data.metadata?.processing_time,
  success: !data.error
});

// Métriques pour monitoring
const metrics = {
  timestamp: new Date(),
  user_role: data.user_role,
  language: data.language,
  response_time: data.metadata?.processing_time,
  actions_count: data.actions?.length || 0,
  suggestions_count: data.suggestions?.length || 0,
  error: data.error || null
};

// Envoi des métriques (optionnel)
// await sendMetrics(metrics);

return [{ json: data }];
```

Cette implémentation complète fournit un workflow n8n fonctionnel qui :
- Reçoit les messages du chatbot frontend
- Traite les demandes avec l'IA selon le rôle utilisateur
- Maintient une mémoire conversationnelle
- Intègre avec les APIs Scholarify
- Respecte les permissions et la langue de l'utilisateur
- Fournit des réponses structurées avec actions et suggestions

## Conclusion

Cette implémentation fournit une base solide pour un chatbot multi-dashboard avec n8n, offrant :

- **Contexte automatique** selon le dashboard
- **Workflows spécialisés** par rôle utilisateur
- **Interface adaptative** et intuitive
- **Architecture évolutive** pour nouveaux dashboards
- **Monitoring complet** des interactions
- **Gestion multilingue** avec détection automatique
- **Intégration complète** avec la documentation et les APIs

Le système est conçu pour être facilement extensible et maintenir une expérience utilisateur cohérente à travers tous les dashboards.
```