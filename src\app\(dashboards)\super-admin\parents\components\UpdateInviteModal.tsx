"use client";

import React, { useRef, useState, useEffect } from "react";
import { X, ChevronDown } from "lucide-react";
import CustomInput from "@/components/inputs/CustomInput";
import CustomPhoneInput from "@/components/inputs/CustomPhoneInput";
import { getSchools } from "@/app/services/SchoolServices";
import { getStudents } from "@/app/services/StudentServices";
import { StudentSchema } from "@/app/models/StudentModel";
import { SchoolSchema } from "@/app/models/SchoolModel";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import CircularLoader from "@/components/widgets/CircularLoader";
import SignalBarLoader from "@/components/widgets/SignalLoader";
import { motion } from "framer-motion";
import { UserSchema } from "@/app/models/UserModel";
import ActionButton from "@/components/ActionButton";
import useAuth from "@/app/hooks/useAuth";
import { useTranslation } from '@/hooks/useTranslation';

interface CreateInvitationModalProps {
  onClose: () => void;
  onSave: (invitationData: UserSchema) => Promise<void>;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
  initialData?: UserSchema;
}

const UpdateInvitationModal: React.FC<CreateInvitationModalProps> = ({
  onClose,
  onSave,
  submitStatus,
  isSubmitting,
  initialData,
}) => {
  const { t } = useTranslation();

  const [formData, setFormData] = useState<UserSchema>({
    _id: "",
    user_id: "",
    role: "parent",
    email: "",
    phone: "",
    name: "",
    school_ids: [],
    student_ids: [] as string[],
    createdAt: "",
  });


  const [countryCode, setCountryCode] = useState("+237");
  const [schools, setSchools] = useState<SchoolSchema[]>([]);
  const [allChildren, setAllChildren] = useState<StudentSchema[]>([]);
  const [children, setChildren] = useState<StudentSchema[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchSchoolTerm, setSearchSchoolTerm] = useState("");
  const [showChildrenDropdown, setShowChildrenDropdown] = useState(false);
  const [showSchoolDropdown, setShowSchoolDropdown] = useState(false);
  const [loadingSchools, setLoadingSchools] = useState(true);
  const [loadingChildren, setLoadingChildren] = useState(true);


  const childrenDropdownRef = useRef<HTMLDivElement>(null);
  const schoolDropdownRef = useRef<HTMLDivElement>(null);

  const [isLoading, setIsLoading] = useState(true); // Single loading state

  useEffect(() => {
    // Start loading
    setIsLoading(true);

    // Fetch both schools and students using Promise.all to wait for both
    Promise.all([getSchools(), getStudents()])
      .then(([schoolData, studentData]) => {
        // Process schools
        setSchools(schoolData);

        // Process students
        const studentsWithId = studentData.map((student: any) => ({
          ...student,
          id: student.id || student._id,
        }));
        setAllChildren(studentsWithId);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
      })
      .finally(() => {
        // Set loading to false after both requests are completed
        setIsLoading(false);
      });
  }, []);


  useEffect(() => {
    if (initialData) {
      const fullPhone = initialData.phone || "";
      const match = fullPhone.match(/^(\+\d{1,4})(.*)/); // Extracts country code and number

      setFormData({
        _id: initialData._id,
        user_id: initialData.user_id || "",
        role: initialData.role || "parent",
        email: initialData.email || "",
        phone: match ? match[2] : fullPhone, // phone without country code
        name: initialData.name || "",
        school_ids: initialData.school_ids || [],
        student_ids: initialData.student_ids || [],
        createdAt: initialData.createdAt || new Date().toISOString(),
      });

      if (match && match[1]) {
        setCountryCode(match[1]); // country code e.g., +237
      }
    }
  }, [initialData]);

  // Update children when school selection changes
  useEffect(() => {
    if ((formData.school_ids ?? []).length === 0) {
      setChildren([]);
    } else {
      const filtered = allChildren.filter((child) =>
        (formData.school_ids ?? []).includes(child.school_id)
      );
      setChildren(filtered);
    }
  }, [formData.school_ids, allChildren]);

  // Close dropdowns on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        childrenDropdownRef.current &&
        !childrenDropdownRef.current.contains(event.target as Node)
      ) {
        setShowChildrenDropdown(false);
      }
      if (
        schoolDropdownRef.current &&
        !schoolDropdownRef.current.contains(event.target as Node)
      ) {
        setShowSchoolDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {

    const phone = formData.phone ?? "";
    const normalizedPhone = phone.startsWith("+")
      ? phone
      : `${countryCode}${phone.replace(/^0+/, "")}`;

    e.preventDefault();
    //console.log("Form Data Before Submit:", formData);

    onSave({
      ...formData,
      _id: initialData?._id || "",
      phone: normalizedPhone,
      createdAt: new Date().toISOString(),
    });
  };

  const toggleChildSelection = (childId: string, isChecked: boolean) => {
    setFormData((prev) => {
      const currentChildrenIds = Array.isArray(prev.childrenIds) ? prev.childrenIds : [];
      const updatedChildren = isChecked
        ? [...currentChildrenIds, childId]
        : currentChildrenIds.filter((id) => id !== childId);
      return {
        ...prev,
        childrenIds: Array.from(new Set(updatedChildren)),
      };
    });
  };

  const toggleSchoolSelection = (schoolId: string) => {
    setFormData((prev) => {
      const schoolIds = Array.isArray(prev.school_ids) ? prev.school_ids : [];
      const alreadySelected = schoolIds.includes(schoolId);
      const updated = alreadySelected
        ? schoolIds.filter((id) => id !== schoolId)
        : [...schoolIds, schoolId];
      return { ...prev, school_ids: updated };
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="h-[550px] bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl mx-4 sm:mx-6 md:mx-0 relative flex flex-col md:flex-row">
        {/* Left Image */}
        <div className="hidden md:block md:w-1/2 h-full">
          <img
            src="/assets/images/parent1.png"
            alt={t('parents.parent_invite')}
            className="w-full h-full object-cover rounded-lg"
            draggable={false}
          />
        </div>

        {/* Form Section */}
        <div className="w-full md:w-1/2 p-6 overflow-y-auto custom-scrollbar">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-foreground">{t('parents.update_parents')}</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={20} />
            </button>
          </div>
          {submitStatus ? (
            <SubmissionFeedback status={submitStatus}
              message={
                submitStatus === "success"
                  ? t('parents.messages.parent_updated')
                  : t('parents.messages.update_error')
              } />
          ) : (
            <form onSubmit={handleSubmit}>
              <CustomInput
                label={t('common.full_name')}
                id="name"
                name="name"
                value={formData.name || ""}
                onChange={handleChange}
                required
              />

              <CustomInput
                label={t('common.email')}
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                disabled={true}
              />

              <CustomPhoneInput
                label={t('common.phone_number')}
                id="phone"
                name="phone"
                value={formData.phone || ""}
                onChange={handleChange}
                countryCode={countryCode}
                onCountryCodeChange={(e) => setCountryCode(e.target.value)}
                required
                disabled={true} countryCodeName={""} />

              {/* School Multi-Select Dropdown */}
              <div className="mb-4 flex flex-col relative">
                <label className="text-sm font-semibold mb-1">{t('parents.schools')}</label>
                <div
                  onClick={() => setShowSchoolDropdown((prev) => !prev)}
                  className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 bg-white dark:text-white cursor-pointer flex items-center justify-between"
                >
                  <span>
                    {(formData.school_ids ?? []).length > 0
                      ? schools
                        .filter((school) => (formData.school_ids ?? []).includes(school._id))
                        .map((school) => school.name)
                        .join(", ")
                      : t('parents.select_schools')}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </div>

                {isLoading ? (
                  <div className="absolute z-10 w-full flex justify-center mt-2">
                    <SignalBarLoader size={30} color="teal" bars={3} />
                  </div>
                ) : (showSchoolDropdown && (
                  <div
                    ref={schoolDropdownRef}
                    className="absolute z-10 bg-white dark:bg-gray-700 mt-1 rounded-md border max-h-48 overflow-y-auto p-2 shadow-lg w-[calc(100%-2px)] left-0"
                  >
                    <input
                      type="text"
                      placeholder={t('parents.search_schools')}
                      value={searchSchoolTerm}
                      onChange={(e) => setSearchSchoolTerm(e.target.value)}
                      className="w-full mb-2 px-2 py-1 border rounded-md text-sm dark:bg-gray-600"
                    />
                    {schools
                      .filter((school) =>
                        school.name.toLowerCase().includes(searchSchoolTerm.toLowerCase())
                      )
                      .map((school) => (
                        <label
                          key={school._id}
                          className="flex items-center gap-2 px-2 py-1 text-sm"
                        >
                          <input
                            type="checkbox"
                            checked={(formData.school_ids ?? []).includes(school._id)}
                            onChange={() => toggleSchoolSelection(school._id)}
                          />
                          {school.name}
                        </label>
                      ))}
                  </div>
                ))}
              </div>

              {/* Children Dropdown */}
              <div className="mb-4 flex flex-col relative">
                <label className="text-sm font-semibold mb-1">{t('parents.children')}</label>
                <div
                  onClick={() => setShowChildrenDropdown((prev) => !prev)}
                  className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 bg-white dark:text-white cursor-pointer flex items-center justify-between"
                >
                  <span>
                    {Array.isArray(formData.student_ids) && formData.student_ids.length > 0
                      ? children
                        .filter((child) =>
                          Array.isArray(formData.student_ids) && formData.student_ids.includes(child.id as string)
                        )
                        .map((child) => child.name)
                        .join(", ")
                      : t('parents.select_children')}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </div>

                {showChildrenDropdown && (
                  <div
                    ref={childrenDropdownRef}
                    className="absolute z-10 bg-white dark:bg-gray-700 mt-1 rounded-md border max-h-48 overflow-y-auto p-2 shadow-lg w-[calc(100%-2px)] left-0"
                  >
                    <input
                      type="text"
                      placeholder={t('parents.search_children')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full mb-2 px-2 py-1 border rounded-md text-sm dark:bg-gray-600"
                    />
                    {children
                      .filter((child) =>
                        child.name.toLowerCase().includes(searchTerm.toLowerCase())
                      )
                      .map((child) => (
                        <label
                          key={child.id as string}
                          className="flex items-center gap-2 px-2 py-1 text-sm"
                        >
                          <input
                            type="checkbox"
                            checked={Array.isArray(formData.childrenIds) && formData.childrenIds.includes(child.id as string)}
                            onChange={(e) =>
                              toggleChildSelection(child.id as string, e.target.checked)
                            }
                          />
                          {child.name}
                        </label>
                      ))}
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <ActionButton
                  action="cancel"
                  label={t('common.cancel')}
                  onClick={onClose}
                  disabled={isSubmitting}
                />

                <ActionButton
                  action="save"
                  label={isSubmitting ? t('parents.updating') : t('parents.update_parent')}
                  type="submit"
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                />
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default UpdateInvitationModal;
