"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, User, ArrowRight, CalendarCheck2 } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";

interface SubjectAttendanceCardProps {
  subjectId: string;
  subjectName: string;
  teacherName: string;
  onClick: () => void;
}

export default function SubjectAttendanceCard({
  subjectId,
  subjectName,
  teacherName,
  onClick,
}: SubjectAttendanceCardProps) {
  const { t, tDashboard } = useTranslation();
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className="bg-widget border border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:shadow-lg transition-all duration-200"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-teal-500/10 dark:bg-teal-500/20 rounded-lg flex items-center justify-center">
            <CalendarCheck2 className="w-6 h-6 text-teal-600 dark:text-teal-400" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground text-lg">
              {subjectName}
            </h3>
            <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
              <User className="w-3 h-3" />
              <span>{teacherName}</span>
            </div>
          </div>
        </div>
        <ArrowRight className="w-5 h-5 text-gray-400" />
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button className="text-teal hover:text-teal-dark text-sm font-medium flex items-center gap-1">
          {tDashboard('school-admin', 'attendance', 'manage_attendance')}
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </motion.div>
  );
}
