"use client";

import React, { useState } from "react";
import { X } from "lucide-react";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import ActionButton from "@/components/ActionButton";
import { useTranslation } from "@/hooks/useTranslation";

interface DeleteClassModalProps {
  className: string;
  onClose: () => void;
  onDelete: (password: string) => void;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
}

const DeleteClassModal: React.FC<DeleteClassModalProps> = ({
  className,
  onClose,
  onDelete,
  submitStatus,
  isSubmitting,
}) => {
  const { t, tDashboard } = useTranslation();
  const [password, setPassword] = useState("");

  const handleDelete = (e: React.FormEvent) => {
    e.preventDefault();
    if (!password) {
      alert(t('dashboard.school-admin.pages.classes.modals.password_required'));
      return;
    }
    onDelete(password);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md mx-4 sm:mx-6 md:mx-0 p-6 relative">
        {/* Modal Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground">{t('dashboard.school-admin.pages.classes.modals.delete_class')}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>
        {submitStatus ? (
          <SubmissionFeedback status={submitStatus}
            message={
              submitStatus === "success"
                ? t('dashboard.school-admin.pages.classes.modals.class_deleted_successfully')
                : t('dashboard.school-admin.pages.classes.modals.error_deleting_class')
            } />
        ) : (<>
          {/* Confirmation Message */}
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            {t('dashboard.school-admin.classes.modals.delete_class_confirmation', { className })}
          </p>

          {/* Password Input */}
          <form onSubmit={handleDelete}>
            <div className="mb-6">
              <input
                type="password"
                placeholder={t('dashboard.school-admin.pages.classes.modals.enter_password_confirm')}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-red-300 dark:border-red-500 rounded-md text-sm text-foreground dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                required
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2">
              <ActionButton
                action="cancel"
                label={t('common.cancel')}
                onClick={onClose}
                type="button"
              />
              <ActionButton
                action="delete"
                type="submit"
                isLoading={isSubmitting}
                disabled={isSubmitting}
                label={isSubmitting ? t('dashboard.school-admin.pages.classes.modals.deleting') : t('common.delete')}
              />

            </div>
          </form>
        </>
        )}
      </div>
    </div>
  );
};

export default DeleteClassModal;
