import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import {
  CreditSchema,
  CreditCreateSchema,
  CreditUpdateSchema,
} from "../models/CreditModel";
import { getSchoolSubscription, getSubscriptionPlans } from "./SubscriptionServices";

// Get all credits
export async function getCredits(): Promise<CreditSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit/get-credits`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch credits");
    }

    const data = await response.json();
    return data.map((credit: any) => ({
      _id: credit._id,
      student_id: credit.student_id,
      school_id: credit.school_id,
      academicYear_id: credit.academicYear_id,
      amountPaid: credit.amountPaid,
      paidAt: credit.paidAt,
      createdAt: credit.createdAt,
      updatedAt: credit.updatedAt,
    })) as CreditSchema[];
  } catch (error) {
    console.error("Error fetching credits:", error);
    throw new Error("Failed to fetch credits");
  }
}

// Get single credit by ID
export async function getCreditById(id: string): Promise<CreditSchema> {
  const response = await fetch(`${BASE_API_URL}/credit/get-credit/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch credit");
  }

  return await response.json();
}

// Create a new credit record
export async function createCredit(data: CreditCreateSchema) {
  const response = await fetch(`${BASE_API_URL}/credit/create-credit`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    let message = "Failed to create credit";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Update credit by ID
export async function updateCredit(id: string, data: CreditUpdateSchema) {
  const response = await fetch(`${BASE_API_URL}/credit/update-credit/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    let message = "Failed to update credit";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Delete a credit record
export async function deleteCredit(id: string) {
  const response = await fetch(`${BASE_API_URL}/credit/delete-credit/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    let message = "Failed to delete credit";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Get credits by school ID (pour les paiements d'étudiants - Credit.js)
export async function getStudentCreditsBySchool(schoolId: string): Promise<CreditSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit/get-credits-by-school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch student credits by school");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching student credits by school:", error);
    throw error;
  }
}

// Get school credits from School.credit (pour les crédits d'école)
export async function getCreditsBySchool(schoolId: string): Promise<{
  schoolCredits: number;
  totalPurchased: number;
  totalUsed: number;
  availableCredits: number;
}> {
  try {
    const token = getTokenFromCookie("idToken");

    // Récupérer les informations de l'école
    const schoolResponse = await fetch(`${BASE_API_URL}/school/get-school_id/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!schoolResponse.ok) {
      throw new Error("Failed to fetch school data");
    }

    const schoolData = await schoolResponse.json();
    const schoolCredits = schoolData.credit || 0;

    // Récupérer la souscription pour avoir les détails des crédits
    const subscriptionResponse = await fetch(`${BASE_API_URL}/school-subscription/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    let totalPurchased = 0;
    let totalUsed = 0;
    let availableCredits = schoolCredits;

    if (subscriptionResponse.ok) {
      const subscriptionResult = await subscriptionResponse.json();
      const subscription = subscriptionResult.subscription || subscriptionResult;

      if (subscription) {
        totalPurchased = subscription.credits_purchased || 0;
        totalUsed = subscription.credits_used || 0;
        availableCredits = subscription.credits_balance || schoolCredits;
      }
    }

    return {
      schoolCredits,
      totalPurchased,
      totalUsed,
      availableCredits
    };
  } catch (error) {
    console.error("Error fetching school credits:", error);
    return {
      schoolCredits: 0,
      totalPurchased: 0,
      totalUsed: 0,
      availableCredits: 0
    };
  }
}

// Calculate available credits for a school based on school credits and purchases
export async function calculateSchoolCredits(schoolId: string): Promise<{
  totalPaid: number;
  availableCredits: number;
  creditCount: number;
  totalCreditsPurchased: number;
}> {
  try {
    // Utiliser la nouvelle API dédiée qui retourne les vraies données de CreditPurchase
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/school-subscription/${schoolId}/credits/summary`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.ok) {
      const summaryData = await response.json();

      return {
        totalPaid: summaryData.totalPaid || 0,
        availableCredits: summaryData.availableCredits || 0,
        creditCount: summaryData.creditCount || 0,
        totalCreditsPurchased: summaryData.totalCreditsPurchased || 0
      };
    } else {
      console.warn("Could not fetch credits summary, using fallback");
      // Fallback vers l'ancien système si l'API échoue
      const schoolCreditsData = await getCreditsBySchool(schoolId);
      const subscription = await getSchoolSubscription(schoolId);

      let pricePerCredit = 3000; // Valeur par défaut
      if (subscription?.plan_type) {
        const plans = await getSubscriptionPlans();
        const schoolPlan = plans.find(plan => plan.plan_name === subscription.plan_type);
        if (schoolPlan?.price_per_credit) {
          pricePerCredit = schoolPlan.price_per_credit;
        }
      }

      return {
        totalPaid: schoolCreditsData.totalPurchased * pricePerCredit,
        availableCredits: schoolCreditsData.availableCredits,
        creditCount: schoolCreditsData.totalPurchased,
        totalCreditsPurchased: schoolCreditsData.totalPurchased
      };
    }
  } catch (error) {
    console.error("Error calculating school credits:", error);
    return {
      totalPaid: 0,
      availableCredits: 0,
      creditCount: 0,
      totalCreditsPurchased: 0
    };
  }
}

// Delete multiple credits
export async function deleteMultipleCredits(ids: string[]) {
  const response = await fetch(`${BASE_API_URL}/credit/delete-credits`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify({ ids }),
  });

  if (!response.ok) {
    let message = "Failed to delete credits";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Get credits by school ID
export async function getCreditsBySchoolId(schoolId: string): Promise<CreditSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit/get-credits-by-school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch credits for the school");
    }

    const credits = await response.json();
    return credits.map((credit: any) => ({
      _id: credit._id,
      student_id: credit.student_id,
      school_id: credit.school_id,
      academicYear_id: credit.academicYear_id,
      amountPaid: credit.amountPaid,
      paidAt: credit.paidAt,
      createdAt: credit.createdAt,
      updatedAt: credit.updatedAt,
    })) as CreditSchema[];
  } catch (error) {
    console.error("Error fetching credits by school ID:", error);
    throw new Error("Failed to fetch credits by school ID");
  }
}

// Get total amount paid across all credits
export async function getTotalAmountPaid(): Promise<number> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit/total-amount-paid`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch total amount");
    }

    const data = await response.json();
    return data.totalAmountPaid as number;
  } catch (error) {
    console.error("Error fetching total amount:", error);
    throw new Error("Failed to fetch total amount");
  }
}

// Get total amount paid for current month with % change from previous month
export interface TotalAmountChange {
  totalAmount: number;
  percentageChange: number;
}

export async function getTotalAmountChange(): Promise<TotalAmountChange> {
  try {
    const token = getTokenFromCookie("idToken");

    const response = await fetch(`${BASE_API_URL}/credit/total-amount-change`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch total amount change");
    }

    const data = await response.json();
    return {
      totalAmount: data.totalAmount,
      percentageChange: data.percentageChange,
    };
  } catch (error) {
    console.error("Error fetching total amount change:", error);
    throw new Error("Failed to fetch total amount change");
  }
}
