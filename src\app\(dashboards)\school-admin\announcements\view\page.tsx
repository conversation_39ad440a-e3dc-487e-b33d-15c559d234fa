"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ArrowLeft, Calendar, Clock, Users, AlertCircle, Eye, EyeOff } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { AnnouncementSchema, getAnnouncementById } from "@/app/services/AnnouncementServices";
import NotificationCard from "@/components/NotificationCard";
import { motion } from "framer-motion";
import { useTranslation } from "@/hooks/useTranslation";

const BASE_URL = "/school-admin";

export default function ViewAnnouncementPage() {
  const { logout } = useAuth();
  const { t, tDashboard } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();
  const announcementId = searchParams.get("id");

  const navigation = {
    icon: Eye,
    baseHref: `${BASE_URL}/announcements/view`,
    title: tDashboard('school-admin', 'announcements', 'view_details')
  };

  const [announcement, setAnnouncement] = useState<AnnouncementSchema | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnnouncement = async () => {
      if (!announcementId) {
        setError(tDashboard('school-admin', 'announcements', 'no_announcement_id'));
        setLoading(false);
        return;
      }

      try {
        const announcementData = await getAnnouncementById(announcementId);
        setAnnouncement(announcementData);
      } catch (err) {
        console.error("Error fetching announcement:", err);
        setError(tDashboard('school-admin', 'announcements', 'failed_to_load'));
      } finally {
        setLoading(false);
      }
    };

    fetchAnnouncement();
  }, [announcementId]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-300';
      case 'high':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30 dark:text-orange-300';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["admin"]}>
        <SchoolLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={() => logout()}
        >
          <div className="flex justify-center items-center h-64">
            <CircularLoader size={32} color="teal" />
          </div>
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  if (error || !announcement) {
    return (
      <ProtectedRoute allowedRoles={["admin"]}>
        <SchoolLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={() => logout()}
        >
          <div className="space-y-6">
            <NotificationCard
              message={error || tDashboard('school-admin', 'announcements', 'announcement_not_found')}
              type="error"
              onClose={() => router.push(`${BASE_URL}/announcements`)}
              isVisible={true}
            />

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => router.push(`${BASE_URL}/announcements`)}
              className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
            >
              <ArrowLeft size={16} />
              <span>{tDashboard('school-admin', 'announcements', 'back_to_announcements')}</span>
            </motion.button>
          </div>
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => router.push(`${BASE_URL}/announcements`)}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-foreground rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <ArrowLeft size={16} />
              <span>{tDashboard('school-admin', 'announcements', 'back_to_announcements')}</span>
            </motion.button>

            <div className="flex items-center space-x-2">
              {announcement.is_published ? (
                <div className="flex items-center space-x-1 text-green-600">
                  <Eye size={16} />
                  <span className="text-sm font-medium">{tDashboard('school-admin', 'announcements', 'published')}</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-gray-600">
                  <EyeOff size={16} />
                  <span className="text-sm font-medium">{tDashboard('school-admin', 'announcements', 'draft')}</span>
                </div>
              )}
            </div>
          </div>

          {/* Announcement Details */}
          <div className="bg-widget rounded-lg border border-stroke p-6 space-y-6">
            {/* Title and Priority */}
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <h1 className="text-2xl font-bold text-foreground">{announcement.title}</h1>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(announcement.priority)}`}>
                  {t('dashboard.school-admin.announcements.priority_label', {
                    priority: tDashboard('school-admin', 'announcements', announcement.priority as 'urgent' | 'high' | 'medium' | 'low')
                  })}
                </span>
              </div>
            </div>

            {/* Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-foreground/60" />
                <div>
                  <p className="text-xs text-foreground/60">{tDashboard('school-admin', 'announcements', 'target_audience_label')}</p>
                  <p className="text-sm font-medium text-foreground">
                    {announcement.target_audience === 'all' ?
                      tDashboard('school-admin', 'announcements', 'everyone') :
                      tDashboard('school-admin', 'announcements', announcement.target_audience as 'teachers' | 'parents' | 'students')
                    }
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-foreground/60" />
                <div>
                  <p className="text-xs text-foreground/60">{tDashboard('school-admin', 'announcements', 'created_label')}</p>
                  <p className="text-sm font-medium text-foreground">
                    {formatDate(announcement.published_at || announcement.created_at)}
                  </p>
                </div>
              </div>

              {announcement.published_at && (
                <div className="flex items-center space-x-2">
                  <Eye className="h-4 w-4 text-foreground/60" />
                  <div>
                    <p className="text-xs text-foreground/60">{tDashboard('school-admin', 'announcements', 'published_label')}</p>
                    <p className="text-sm font-medium text-foreground">
                      {formatDate(announcement.published_at)}
                    </p>
                  </div>
                </div>
              )}

              {announcement.expires_at && (
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-foreground/60" />
                  <div>
                    <p className="text-xs text-foreground/60">{tDashboard('school-admin', 'announcements', 'expires_label')}</p>
                    <p className="text-sm font-medium text-foreground">
                      {formatDate(announcement.expires_at)}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Content */}
            <div className="space-y-3">
              <h2 className="text-lg font-semibold text-foreground">{tDashboard('school-admin', 'announcements', 'content_title')}</h2>
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <div className="whitespace-pre-wrap text-foreground bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  {announcement.content}
                </div>
              </div>
            </div>

            {/* Expiry Warning */}
            {announcement.expires_at && new Date(announcement.expires_at) < new Date() && (
              <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-sm text-red-600 dark:text-red-300">
                  {t('dashboard.school-admin.announcements.expired_warning', {
                    date: formatDate(announcement.expires_at)
                  })}
                </p>
              </div>
            )}
          </div>
        </div>
      </SchoolLayout>
  );
}
