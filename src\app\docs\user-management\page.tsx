"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
    Users,
    UserPlus,
    UserCog,
    Shield,
    Key,
    Edit3,
    Search,
    Download,
    Upload,
    Settings,
    CheckCircle,
    AlertTriangle,
    Info,
    GraduationCap,
    BookOpen,
    UserCheck,
    ArrowLeft,
} from "lucide-react";

export default function UserManagementPage() {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState("overview");
    const [showPassword, setShowPassword] = useState(false);

    const userRoles = [
        {
            name: "Super Admin",
            icon: <Shield className="w-5 h-5" />,
            description: "Full system access and control",
            permissions: [
                "All system permissions",
                "Manage schools",
                "System configuration",
                "User management",
            ],
            color: "red",
        },
        {
            name: "School Admin",
            icon: <UserCog className="w-5 h-5" />,
            description: "Complete school management access",
            permissions: [
                "Manage school data",
                "User management",
                "Academic records",
                "Reports and analytics",
            ],
            color: "blue",
        },
        {
            name: "Teacher",
            icon: <BookOpen className="w-5 h-5" />,
            description: "Classroom and student management",
            permissions: [
                "Manage classes",
                "Student grades",
                "Attendance",
                "Class resources",
            ],
            color: "green",
        },
        {
            name: "Student",
            icon: <GraduationCap className="w-5 h-5" />,
            description: "Access to personal academic information",
            permissions: [
                "View grades",
                "View schedule",
                "Access resources",
                "Submit assignments",
            ],
            color: "purple",
        },
        {
            name: "Parent",
            icon: <UserCheck className="w-5 h-5" />,
            description: "Monitor child's academic progress",
            permissions: [
                "View child's grades",
                "View attendance",
                "Communication",
                "Fee payments",
            ],
            color: "teal",
        },
    ];

    const managementSections = [
        {
            id: "overview",
            title: "Overview",
            icon: <Users className="w-5 h-5" />,
        },
        {
            id: "roles",
            title: "Roles & Permissions",
            icon: <Shield className="w-5 h-5" />,
        },
        {
            id: "adding-users",
            title: "Adding Users",
            icon: <UserPlus className="w-5 h-5" />,
        },
        {
            id: "managing-users",
            title: "Managing Users",
            icon: <UserCog className="w-5 h-5" />,
        },
        {
            id: "security",
            title: "Security",
            icon: <Key className="w-5 h-5" />,
        },
    ];

    const addUserSteps = [
        "Navigate to the Users section in your dashboard",
        "Click the 'Add New User' button",
        "Fill in the required user information (name, email, role)",
        "Set appropriate permissions based on the user's role",
        "Send invitation email or provide login credentials",
        "User completes registration and email verification",
    ];

    const securityFeatures = [
        {
            title: "Two-Factor Authentication",
            description: "Optional 2FA for enhanced account security",
            status: "Available",
        },
        {
            title: "Role-Based Access Control",
            description: "Granular permissions based on user roles",
            status: "Built-in",
        },
        {
            title: "Session Management",
            description: "Automatic logout and session timeout controls",
            status: "Configurable",
        },
        {
            title: "Password Policies",
            description: "Customizable password requirements",
            status: "Enforced",
        },
        {
            title: "Activity Logging",
            description: "Track user actions and system access",
            status: "Comprehensive",
        },
    ];

    return (
        <div className="p-8">
            {/* Back Button */}
            <div className="mb-6">
                <button
                    onClick={() => router.back()}
                    className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-300 transition-colors group"
                >
                    <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
                    <span className="text-sm font-medium">Back to Documentation</span>
                </button>
            </div>

            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    User Management Guide
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                    Learn how to effectively manage users, assign roles, and
                    configure permissions in your Scholarify system. This guide
                    covers everything from adding new users to implementing
                    security best practices.
                </p>
            </div>

            {/* Navigation Tabs */}
            <div className="mb-8">
                <div className="border-b border-gray-200 dark:border-gray-700">
                    <nav className="-mb-px flex space-x-8 overflow-x-auto">
                        {managementSections.map((section) => (
                            <button
                                key={section.id}
                                onClick={() => setActiveTab(section.id)}
                                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                                    activeTab === section.id
                                        ? "border-teal-500 text-teal-600 dark:text-teal-400"
                                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                                }`}
                            >
                                {section.icon}
                                <span>{section.title}</span>
                            </button>
                        ))}
                    </nav>
                </div>
            </div>

            {/* Content based on active tab */}
            {activeTab === "overview" && (
                <div className="space-y-8">
                    {/* Overview Stats */}
                    <div className="grid md:grid-cols-3 gap-6">
                        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                                        Total Users
                                    </p>
                                    <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                                        1,247
                                    </p>
                                </div>
                                <Users className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                            </div>
                        </div>
                        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-green-600 dark:text-green-400 text-sm font-medium">
                                        Active Users
                                    </p>
                                    <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                                        1,189
                                    </p>
                                </div>
                                <UserCheck className="w-8 h-8 text-green-600 dark:text-green-400" />
                            </div>
                        </div>
                        <div className="bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-800 rounded-lg p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-teal-600 dark:text-teal-400 text-sm font-medium">
                                        User Roles
                                    </p>
                                    <p className="text-2xl font-bold text-teal-900 dark:text-teal-100">
                                        5
                                    </p>
                                </div>
                                <Shield className="w-8 h-8 text-teal-600 dark:text-teal-400" />
                            </div>
                        </div>
                    </div>

                    {/* User Management Features */}
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                            User Management Features
                        </h2>
                        <div className="grid md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <div className="flex items-start space-x-3">
                                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white">
                                            Bulk User Import
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Import multiple users from CSV files
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start space-x-3">
                                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white">
                                            Role-Based Access
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Granular permission control for each
                                            role
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start space-x-3">
                                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white">
                                            User Profile Management
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Complete profile information and
                                            settings
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="space-y-4">
                                <div className="flex items-start space-x-3">
                                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white">
                                            Activity Monitoring
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Track user login and system usage
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start space-x-3">
                                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white">
                                            Password Management
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Reset passwords and enforce policies
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start space-x-3">
                                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white">
                                            User Communication
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Send notifications and announcements
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {activeTab === "roles" && (
                <div className="space-y-8">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                            User Roles & Permissions
                        </h2>
                        <div className="grid gap-6">
                            {userRoles.map((role, index) => (
                                <div
                                    key={index}
                                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-white dark:bg-gray-800"
                                >
                                    <div className="flex items-start justify-between mb-4">
                                        <div className="flex items-center space-x-3">
                                            <div
                                                className={`p-2 rounded-lg bg-${role.color}-100 dark:bg-${role.color}-900/20 text-${role.color}-600 dark:text-${role.color}-400`}
                                            >
                                                {role.icon}
                                            </div>
                                            <div>
                                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                    {role.name}
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-400">
                                                    {role.description}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                            Permissions:
                                        </h4>
                                        <div className="grid md:grid-cols-2 gap-2">
                                            {role.permissions.map(
                                                (permission, permIndex) => (
                                                    <div
                                                        key={permIndex}
                                                        className="flex items-center space-x-2"
                                                    >
                                                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                                            {permission}
                                                        </span>
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {activeTab === "adding-users" && (
                <div className="space-y-8">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                            Adding New Users
                        </h2>

                        {/* Step-by-step process */}
                        <div className="space-y-4 mb-8">
                            {addUserSteps.map((step, index) => (
                                <div
                                    key={index}
                                    className="flex items-start space-x-4"
                                >
                                    <div className="flex-shrink-0 w-8 h-8 bg-teal-100 dark:bg-teal-900/20 text-teal-600 dark:text-teal-400 rounded-full flex items-center justify-center text-sm font-semibold">
                                        {index + 1}
                                    </div>
                                    <p className="text-gray-700 dark:text-gray-300 mt-1">
                                        {step}
                                    </p>
                                </div>
                            ))}
                        </div>

                        {/* Methods of adding users */}
                        <div className="grid md:grid-cols-2 gap-6">
                            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                                <div className="flex items-center space-x-3 mb-4">
                                    <UserPlus className="w-6 h-6 text-teal-600 dark:text-teal-400" />
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                        Individual User Creation
                                    </h3>
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 mb-4">
                                    Add users one by one through the user
                                    interface. Best for small numbers of users
                                    or when you need to set specific
                                    permissions.
                                </p>
                                <div className="space-y-2">
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            Detailed control over each user
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            Custom permissions per user
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            Immediate activation
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                                <div className="flex items-center space-x-3 mb-4">
                                    <Upload className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                        Bulk Import
                                    </h3>
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 mb-4">
                                    Import multiple users from CSV files. Ideal
                                    for schools with large numbers of students
                                    or when migrating from another system.
                                </p>
                                <div className="space-y-2">
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            Process hundreds of users quickly
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            CSV template provided
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            Validation and error reporting
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {activeTab === "managing-users" && (
                <div className="space-y-8">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                            Managing Existing Users
                        </h2>

                        {/* User Management Actions */}
                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                            {[
                                {
                                    icon: <Edit3 className="w-6 h-6" />,
                                    title: "Edit User Information",
                                    description:
                                        "Update user profiles, contact details, and basic information",
                                    color: "blue",
                                },
                                {
                                    icon: <Settings className="w-6 h-6" />,
                                    title: "Modify Permissions",
                                    description:
                                        "Change user roles and adjust specific permissions",
                                    color: "green",
                                },
                                {
                                    icon: <Key className="w-6 h-6" />,
                                    title: "Reset Passwords",
                                    description:
                                        "Force password resets or generate temporary passwords",
                                    color: "yellow",
                                },
                                {
                                    icon: <UserCheck className="w-6 h-6" />,
                                    title: "Activate/Deactivate",
                                    description:
                                        "Enable or disable user accounts as needed",
                                    color: "purple",
                                },
                                {
                                    icon: <Search className="w-6 h-6" />,
                                    title: "Search & Filter",
                                    description:
                                        "Find users quickly using various search criteria",
                                    color: "teal",
                                },
                                {
                                    icon: <Download className="w-6 h-6" />,
                                    title: "Export User Data",
                                    description:
                                        "Generate reports and export user information",
                                    color: "indigo",
                                },
                            ].map((action, index) => (
                                <div
                                    key={index}
                                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                                >
                                    <div
                                        className={`text-${action.color}-600 dark:text-${action.color}-400 mb-3`}
                                    >
                                        {action.icon}
                                    </div>
                                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                        {action.title}
                                    </h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {action.description}
                                    </p>
                                </div>
                            ))}
                        </div>

                        {/* Best Practices */}
                        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
                                Best Practices for User Management
                            </h3>
                            <div className="grid md:grid-cols-2 gap-4">
                                <div className="space-y-3">
                                    <div className="flex items-start space-x-2">
                                        <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                                        <span className="text-sm text-blue-800 dark:text-blue-200">
                                            Regularly review and update user
                                            permissions
                                        </span>
                                    </div>
                                    <div className="flex items-start space-x-2">
                                        <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                                        <span className="text-sm text-blue-800 dark:text-blue-200">
                                            Deactivate accounts for users who
                                            leave the school
                                        </span>
                                    </div>
                                    <div className="flex items-start space-x-2">
                                        <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                                        <span className="text-sm text-blue-800 dark:text-blue-200">
                                            Use consistent naming conventions
                                            for usernames
                                        </span>
                                    </div>
                                </div>
                                <div className="space-y-3">
                                    <div className="flex items-start space-x-2">
                                        <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                                        <span className="text-sm text-blue-800 dark:text-blue-200">
                                            Keep user information up to date
                                        </span>
                                    </div>
                                    <div className="flex items-start space-x-2">
                                        <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                                        <span className="text-sm text-blue-800 dark:text-blue-200">
                                            Monitor user activity for security
                                            purposes
                                        </span>
                                    </div>
                                    <div className="flex items-start space-x-2">
                                        <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                                        <span className="text-sm text-blue-800 dark:text-blue-200">
                                            Provide training for new user roles
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {activeTab === "security" && (
                <div className="space-y-8">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                            Security & Access Control
                        </h2>

                        {/* Security Features */}
                        <div className="space-y-4 mb-8">
                            {securityFeatures.map((feature, index) => (
                                <div
                                    key={index}
                                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 flex items-center justify-between"
                                >
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white">
                                            {feature.title}
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            {feature.description}
                                        </p>
                                    </div>
                                    <span className="px-3 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 text-sm rounded-full">
                                        {feature.status}
                                    </span>
                                </div>
                            ))}
                        </div>

                        {/* Security Warnings */}
                        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                            <div className="flex items-start space-x-3">
                                <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5" />
                                <div>
                                    <h3 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                                        Security Recommendations
                                    </h3>
                                    <ul className="space-y-2 text-sm text-yellow-800 dark:text-yellow-200">
                                        <li>
                                            • Enforce strong password policies
                                            for all users
                                        </li>
                                        <li>
                                            • Enable two-factor authentication
                                            for administrative accounts
                                        </li>
                                        <li>
                                            • Regularly review user permissions
                                            and access levels
                                        </li>
                                        <li>
                                            • Monitor system access logs for
                                            suspicious activity
                                        </li>
                                        <li>
                                            • Implement regular security
                                            training for staff
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
