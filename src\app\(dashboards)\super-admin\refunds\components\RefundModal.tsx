"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  X,
  AlertTriangle,
  DollarSign,
  Phone,
  User,
  CreditCard,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { processRefund, RefundError } from '@/app/services/RefundServices';

interface Transaction {
  _id: string;
  transaction_id: string;
  purchase_id: string;
  school_id: {
    _id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  credits_purchased: number;
  total_amount: number;
  payment_status: string;
  billing_info: {
    name?: string;
    email?: string;
    phone?: string;
  };
  purchase_date: string;
}

interface RefundModalProps {
  transaction: Transaction;
  onClose: () => void;
  onSuccess: () => void;
}

export default function RefundModal({ transaction, onClose, onSuccess }: RefundModalProps) {
  const [step, setStep] = useState<'form' | 'processing' | 'success' | 'error'>('form');
  const [formData, setFormData] = useState({
    phone: transaction.billing_info.phone || transaction.school_id.phone || '',
    reason: '',
    amount: transaction.total_amount,
    medium: 'mobile money' as 'mobile money' | 'orange money'
  });
  const [error, setError] = useState<RefundError | null>(null);
  const [simpleError, setSimpleError] = useState<string | null>(null);
  const [actionType, setActionType] = useState<'refund' | 'cancellation' | null>(null);

  // Déterminer le type d'action selon le statut de la transaction
  const isRefundable = transaction.payment_status === 'completed';
  const actionLabel = isRefundable ? 'remboursement' : 'annulation';
  const actionVerb = isRefundable ? 'Rembourser' : 'Annuler';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation selon le type d'action
    if (!formData.reason) {
      setSimpleError('Veuillez indiquer la raison');
      setError(null);
      return;
    }

    // Pour les remboursements, le téléphone est obligatoire
    if (isRefundable) {
      if (!formData.phone) {
        setSimpleError('Le numéro de téléphone est requis pour un remboursement');
        setError(null);
        return;
      }

      // Validation du numéro de téléphone
      const phoneRegex = /^6[0-9]{8}$/;
      if (!phoneRegex.test(formData.phone)) {
        setSimpleError('Le numéro de téléphone doit être au format 6XXXXXXXX');
        setError(null);
        return;
      }
    }

    setStep('processing');
    setError(null);
    setSimpleError(null);

    try {
      const refundData = {
        transaction_id: transaction.transaction_id,
        phone: formData.phone,
        reason: formData.reason,
        amount: formData.amount,
        medium: formData.medium
      }
      const response = await processRefund(refundData);

      // Stocker le type d'action pour l'affichage du succès
      setActionType(response.action_type || (isRefundable ? 'refund' : 'cancellation'));

      setStep('success');
      setTimeout(() => {
        onSuccess();
      }, 2000);
    } catch (err: any) {
      console.error('Refund error:', err);

      // Si c'est une RefundError avec des détails, l'utiliser
      if (err.error_code && err.error_type) {
        setError(err as RefundError);
        setSimpleError(null);
      } else {
        // Sinon, utiliser un message d'erreur simple
        setSimpleError(err.message || 'Erreur de connexion. Veuillez réessayer.');
        setError(null);
      }

      setStep('error');
    }
  };

  const handleClose = () => {
    if (step !== 'processing') {
      onClose();
    }
  };

  // Fonction pour formater les messages d'erreur
  const getErrorMessage = (error: RefundError): string => {
    switch (error.error_code) {
      case 'VALIDATION_ERROR':
        if (error.error_type === 'missing_required_fields') {
          return `Champs manquants: ${error.details?.missing_fields?.join(', ')}`;
        }
        if (error.error_type === 'invalid_phone_format') {
          return `Format de téléphone invalide. Utilisez le format: ${error.details?.expected_format}`;
        }
        return error.message;

      case 'TRANSACTION_NOT_FOUND':
        return `Transaction non trouvée (ID: ${error.details?.transaction_id})`;

      case 'ALREADY_REFUNDED':
        return `Cette transaction a déjà été remboursée${error.details?.refund_info?.refund_date ?
          ` le ${new Date(error.details.refund_info.refund_date).toLocaleDateString()}` : ''}`;

      case 'INVALID_TRANSACTION_STATUS':
        return `Impossible de rembourser une transaction avec le statut "${error.details?.current_status}". Statut requis: ${error.details?.required_status}`;

      case 'INVALID_REFUND_AMOUNT':
        if (error.details?.minimum_amount) {
          return `Le montant minimum est de ${error.details.minimum_amount} XAF`;
        }
        if (error.details?.maximum_allowed) {
          return `Le montant ne peut pas dépasser ${error.details.maximum_allowed} XAF (montant original)`;
        }
        return error.message;

      case 'FAPSHI_PAYOUT_FAILED':
      case 'FAPSHI_VALIDATION_ERROR':
      case 'FAPSHI_AUTH_ERROR':
      case 'FAPSHI_PERMISSION_ERROR':
      case 'FAPSHI_SERVER_ERROR':
        return `Erreur du fournisseur de paiement: ${error.provider_error || error.message}`;

      case 'NETWORK_ERROR':
        return 'Erreur de connexion. Vérifiez votre connexion internet et réessayez.';

      case 'AUTH_TOKEN_MISSING':
        return 'Session expirée. Veuillez vous reconnecter.';

      default:
        return error.message || 'Une erreur inattendue s\'est produite';
    }
  };

  // Fonction pour obtenir le niveau de sévérité de l'erreur
  const getErrorSeverity = (error: RefundError): 'low' | 'medium' | 'high' => {
    switch (error.error_type) {
      case 'validation_error':
        return 'low';
      case 'business_logic_error':
        return 'medium';
      case 'payment_provider_error':
      case 'authentication_error':
      case 'authorization_error':
        return 'high';
      default:
        return 'medium';
    }
  };

  const renderForm = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Transaction Info */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">Détails de la transaction</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600 dark:text-gray-400">École:</span>
            <p className="font-medium text-gray-900 dark:text-white">{transaction.school_id.name}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Transaction ID:</span>
            <p className="font-medium text-gray-900 dark:text-white">{transaction.transaction_id}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Montant original:</span>
            <p className="font-medium text-gray-900 dark:text-white">{transaction.total_amount.toLocaleString()} XAF</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Crédits:</span>
            <p className="font-medium text-gray-900 dark:text-white">{transaction.credits_purchased}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Statut:</span>
            <p className={`font-medium ${
              transaction.payment_status === 'completed' ? 'text-green-600 dark:text-green-400' :
              transaction.payment_status === 'failed' ? 'text-red-600 dark:text-red-400' :
              'text-yellow-600 dark:text-yellow-400'
            }`}>
              {transaction.payment_status}
            </p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Action:</span>
            <p className="font-medium text-gray-900 dark:text-white">
              {actionLabel.charAt(0).toUpperCase() + actionLabel.slice(1)}
            </p>
          </div>
        </div>
      </div>

      {/* Refund Form */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Phone className="inline h-4 w-4 mr-1" />
            {isRefundable ? 'Numéro de téléphone pour le remboursement *' : 'Numéro de téléphone (optionnel)'}
          </label>
          <input
            type="text"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
            placeholder="6XXXXXXXX"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            required={isRefundable}
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Format: 6XXXXXXXX (numéro camerounais)
          </p>
        </div>

        {isRefundable && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Type de compte
              </label>
              <select
                value={formData.medium}
                onChange={(e) => setFormData({ ...formData, medium: e.target.value as 'mobile money' | 'orange money' })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="mobile money">Mobile Money</option>
                <option value="orange money">Orange Money</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Montant du remboursement (XAF)
              </label>
              <input
                type="number"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: parseInt(e.target.value) || 0 })}
                min="100"
                max={transaction.total_amount}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                required
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Maximum: {transaction.total_amount.toLocaleString()} XAF
              </p>
            </div>
          </>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Raison {isRefundable ? 'du remboursement' : 'de l\'annulation'} *
          </label>
          <textarea
            value={formData.reason}
            onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
            placeholder={`Décrivez la raison ${isRefundable ? 'du remboursement' : 'de l\'annulation'}...`}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            required
          />
        </div>
      </div>

      {(error || simpleError) && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-red-700 dark:text-red-400 font-medium">
                {error ? getErrorMessage(error) : simpleError}
              </p>
              {error && error.details && (
                <div className="mt-2 text-sm text-red-600 dark:text-red-300">
                  {error.error_code && (
                    <p className="font-mono text-xs">Code: {error.error_code}</p>
                  )}
                  {error.details.transaction_id && (
                    <p>Transaction: {error.details.transaction_id}</p>
                  )}
                  {error.details.refund_amount && (
                    <p>Montant: {error.details.refund_amount} XAF</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={handleClose}
          className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          Annuler
        </button>
        <button
          type="submit"
          className={`px-6 py-2 text-white rounded-lg transition-colors flex items-center space-x-2 ${
            isRefundable
              ? 'bg-red-600 hover:bg-red-700'
              : 'bg-orange-600 hover:bg-orange-700'
          }`}
        >
          <DollarSign className="h-4 w-4" />
          <span>{actionVerb} la transaction</span>
        </button>
      </div>
    </form>
  );

  const renderProcessing = () => (
    <div className="text-center py-8">
      <Loader2 className="h-12 w-12 text-blue-500 mx-auto mb-4 animate-spin" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Traitement {isRefundable ? 'du remboursement' : 'de l\'annulation'}...
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Veuillez patienter pendant que nous traitons votre demande {isRefundable ? 'de remboursement' : 'd\'annulation'}.
      </p>
    </div>
  );

  const renderSuccess = () => {
    const isRefundAction = actionType === 'refund';

    return (
      <div className="text-center py-8">
        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {isRefundAction ? 'Remboursement effectué avec succès !' : 'Transaction annulée avec succès !'}
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          {isRefundAction
            ? `Le remboursement a été envoyé vers le numéro ${formData.phone}.`
            : 'La transaction a été annulée et marquée comme telle dans le système.'
          }
        </p>
      </div>
    );
  };

  const renderError = () => {
    const errorMessage = error ? getErrorMessage(error) : simpleError;
    const severity = error ? getErrorSeverity(error) : 'medium';

    return (
      <div className="py-8 px-6">
        <div className="text-center mb-6">
          <AlertTriangle className={`h-12 w-12 mx-auto mb-4 ${
            severity === 'high' ? 'text-red-600' :
            severity === 'medium' ? 'text-red-500' : 'text-orange-500'
          }`} />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Erreur lors du remboursement
          </h3>
        </div>

        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-red-700 dark:text-red-400 font-medium">
                {errorMessage}
              </p>

              {error && error.details && (
                <div className="mt-3 space-y-2">
                  {error.error_code && (
                    <div className="text-sm">
                      <span className="font-medium text-red-600 dark:text-red-300">Code d'erreur:</span>
                      <span className="ml-2 font-mono text-red-700 dark:text-red-400">{error.error_code}</span>
                    </div>
                  )}

                  {error.details.transaction_id && (
                    <div className="text-sm">
                      <span className="font-medium text-red-600 dark:text-red-300">Transaction:</span>
                      <span className="ml-2 font-mono text-red-700 dark:text-red-400">{error.details.transaction_id}</span>
                    </div>
                  )}

                  {error.details.provider_status_code && (
                    <div className="text-sm">
                      <span className="font-medium text-red-600 dark:text-red-300">Code fournisseur:</span>
                      <span className="ml-2 text-red-700 dark:text-red-400">{error.details.provider_status_code}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-center space-x-3">
          <button
            onClick={() => {
              setStep('form');
              setError(null);
              setSimpleError(null);
            }}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Réessayer
          </button>
          <button
            onClick={handleClose}
            className="px-6 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
          >
            Fermer
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isRefundable ? 'Remboursement de transaction' : 'Annulation de transaction'}
          </h2>
          {step !== 'processing' && (
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="h-6 w-6" />
            </button>
          )}
        </div>

        <div className="p-6">
          {step === 'form' && renderForm()}
          {step === 'processing' && renderProcessing()}
          {step === 'success' && renderSuccess()}
          {step === 'error' && renderError()}
        </div>
      </motion.div>
    </div>
  );
}
