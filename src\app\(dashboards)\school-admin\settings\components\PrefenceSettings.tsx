"use client";

import React from "react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { Check } from "lucide-react";

interface PreferencesForm {
  enable_notifications: boolean;
  default_grading_scale: "A-F" | "Percentage" | "Pass/Fail";
  grading_system_base: 100 | 20 | 10 | 5;
}

interface PreferencesSettingsProps {
  preferencesForm: PreferencesForm;
  setPreferencesForm: React.Dispatch<React.SetStateAction<PreferencesForm>>;
  handlePreferencesSubmit: () => Promise<void>;
}

const PreferencesSettings: React.FC<PreferencesSettingsProps> = ({
  preferencesForm,
  setPreferencesForm,
  handlePreferencesSubmit,
}) => {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-foreground mb-4">Preferences</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-foreground mb-1">Enable Notifications</label>
          <input
            type="checkbox"
            checked={preferencesForm.enable_notifications}
            onChange={(e) =>
              setPreferencesForm({ ...preferencesForm, enable_notifications: e.target.checked })
            }
            className="accent-teal h-5 w-5"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-1">Default Grading Scale</label>
          <select
            value={preferencesForm.default_grading_scale}
            onChange={(e) =>
              setPreferencesForm({
                ...preferencesForm,
                default_grading_scale: e.target.value as PreferencesForm["default_grading_scale"],
              })
            }
            className="input bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
          >
            <option value="A-F">A-F</option>
            <option value="Percentage">Percentage</option>
            <option value="Pass/Fail">Pass/Fail</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-1">Grading System Base</label>
          <select
            value={preferencesForm.grading_system_base}
            onChange={(e) =>
              setPreferencesForm({
                ...preferencesForm,
                grading_system_base: Number(e.target.value) as PreferencesForm["grading_system_base"],
              })
            }
            className="input bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
          >
            {[100, 20, 10, 5].map((val) => (
              <option key={val} value={val}>{val}</option>
            ))}
          </select>
        </div>
      </div>

      <Button
        onClick={handlePreferencesSubmit}
        className="w-full py-3 bg-teal hover:opacity-90 text-white font-semibold rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <Check className="h-5 w-5" />
        <span>Save Preferences</span>
      </Button>
    </div>
  );
};

export default PreferencesSettings;
