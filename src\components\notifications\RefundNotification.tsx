"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  X,
  ExternalLink,
  RefreshCw
} from 'lucide-react';

interface RefundNotificationData {
  type: 'refund_needed' | 'refund_success' | 'refund_failed' | 'payment_stuck';
  title: string;
  message: string;
  transactionId?: string;
  amount?: number;
  credits?: number;
  schoolName?: string;
  actionLabel?: string;
  onAction?: () => void;
  autoClose?: boolean;
  duration?: number;
}

interface RefundNotificationProps {
  notifications: RefundNotificationData[];
  onRemove: (index: number) => void;
  onRetry?: (transactionId: string) => void;
  onViewDetails?: (transactionId: string) => void;
}

export default function RefundNotification({ 
  notifications, 
  onRemove, 
  onRetry, 
  onViewDetails 
}: RefundNotificationProps) {
  const [visibleNotifications, setVisibleNotifications] = useState<RefundNotificationData[]>([]);

  useEffect(() => {
    setVisibleNotifications(notifications);
    
    // Auto-close notifications
    notifications.forEach((notification, index) => {
      if (notification.autoClose !== false) {
        const duration = notification.duration || 5000;
        setTimeout(() => {
          onRemove(index);
        }, duration);
      }
    });
  }, [notifications, onRemove]);

  const getIcon = (type: string) => {
    switch (type) {
      case 'refund_needed':
        return <AlertTriangle className="h-6 w-6 text-orange-500" />;
      case 'refund_success':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'refund_failed':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'payment_stuck':
        return <Clock className="h-6 w-6 text-yellow-500" />;
      default:
        return <AlertTriangle className="h-6 w-6 text-gray-500" />;
    }
  };

  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'refund_needed':
        return 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800';
      case 'refund_success':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'refund_failed':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
      case 'payment_stuck':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
      default:
        return 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR').format(amount);
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 max-w-md">
      <AnimatePresence>
        {visibleNotifications.map((notification, index) => (
          <motion.div
            key={`${notification.type}-${index}`}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className={`${getBackgroundColor(notification.type)} border rounded-lg shadow-lg p-4 max-w-sm`}
          >
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                {getIcon(notification.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                      {notification.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      {notification.message}
                    </p>
                    
                    {/* Transaction details */}
                    {(notification.amount || notification.credits || notification.schoolName) && (
                      <div className="mt-2 space-y-1">
                        {notification.schoolName && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            École: {notification.schoolName}
                          </p>
                        )}
                        {notification.amount && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Montant: {formatCurrency(notification.amount)} XAF
                          </p>
                        )}
                        {notification.credits && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Crédits: {notification.credits}
                          </p>
                        )}
                        {notification.transactionId && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                            ID: {notification.transactionId.substring(0, 12)}...
                          </p>
                        )}
                      </div>
                    )}
                    
                    {/* Action buttons */}
                    <div className="flex items-center space-x-2 mt-3">
                      {notification.type === 'refund_needed' && notification.transactionId && (
                        <>
                          <button
                            onClick={() => onViewDetails?.(notification.transactionId!)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            Voir détails
                          </button>
                          <button
                            onClick={() => window.open('/super-admin/refunds', '_blank')}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30 rounded hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors"
                          >
                            <DollarSign className="h-3 w-3 mr-1" />
                            Rembourser
                          </button>
                        </>
                      )}
                      
                      {notification.type === 'payment_stuck' && notification.transactionId && (
                        <button
                          onClick={() => onRetry?.(notification.transactionId!)}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 rounded hover:bg-yellow-200 dark:hover:bg-yellow-900/50 transition-colors"
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          Vérifier
                        </button>
                      )}
                      
                      {notification.actionLabel && notification.onAction && (
                        <button
                          onClick={notification.onAction}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
                        >
                          {notification.actionLabel}
                        </button>
                      )}
                    </div>
                  </div>
                  
                  <button
                    onClick={() => onRemove(index)}
                    className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

// Hook pour gérer les notifications de remboursement
export function useRefundNotifications() {
  const [notifications, setNotifications] = useState<RefundNotificationData[]>([]);

  const addNotification = (notification: RefundNotificationData) => {
    setNotifications(prev => [...prev, notification]);
  };

  const removeNotification = (index: number) => {
    setNotifications(prev => prev.filter((_, i) => i !== index));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Notifications prédéfinies
  const showRefundNeeded = (data: {
    transactionId: string;
    amount: number;
    credits: number;
    schoolName: string;
    reason?: string;
  }) => {
    addNotification({
      type: 'refund_needed',
      title: '💰 Remboursement nécessaire',
      message: data.reason || 'Cette transaction nécessite un remboursement.',
      transactionId: data.transactionId,
      amount: data.amount,
      credits: data.credits,
      schoolName: data.schoolName,
      autoClose: false
    });
  };

  const showRefundSuccess = (data: {
    transactionId: string;
    amount: number;
    schoolName: string;
  }) => {
    addNotification({
      type: 'refund_success',
      title: '✅ Remboursement effectué',
      message: 'Le remboursement a été traité avec succès.',
      transactionId: data.transactionId,
      amount: data.amount,
      schoolName: data.schoolName,
      autoClose: true,
      duration: 5000
    });
  };

  const showRefundFailed = (data: {
    transactionId: string;
    amount: number;
    schoolName: string;
    error: string;
  }) => {
    addNotification({
      type: 'refund_failed',
      title: '❌ Échec du remboursement',
      message: `Erreur: ${data.error}`,
      transactionId: data.transactionId,
      amount: data.amount,
      schoolName: data.schoolName,
      autoClose: false
    });
  };

  const showPaymentStuck = (data: {
    transactionId: string;
    amount: number;
    credits: number;
    schoolName: string;
    hoursPending: number;
  }) => {
    addNotification({
      type: 'payment_stuck',
      title: '⏰ Paiement bloqué',
      message: `Paiement en attente depuis ${data.hoursPending.toFixed(1)}h.`,
      transactionId: data.transactionId,
      amount: data.amount,
      credits: data.credits,
      schoolName: data.schoolName,
      autoClose: false
    });
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showRefundNeeded,
    showRefundSuccess,
    showRefundFailed,
    showPaymentStuck
  };
}
