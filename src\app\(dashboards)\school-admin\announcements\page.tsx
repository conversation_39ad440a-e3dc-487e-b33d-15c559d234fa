"use client";

import { Megaphone, Plus, Search, Filter, Grid, List, Trash2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import CreateAnnouncementModal from "./components/CreateAnnouncementModal";
import DeleteAnnouncementModal from "./components/DeleteAnnouncementModal";
import BulkDeleteModal from "@/components/modals/BulkDeleteModal";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import AnnouncementCard from "@/components/cards/AnnouncementCard";
import { useTranslation } from "@/hooks/useTranslation";
import { 
  AnnouncementSchema, 
  AnnouncementCreateSchema,
  getAnnouncementsBySchool,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
  deleteMultipleAnnouncements
} from "@/app/services/AnnouncementServices";
import { verifyPassword } from "@/app/services/UserServices";
import NotificationCard from "@/components/NotificationCard";
import { motion } from "framer-motion";

const BASE_URL = "/school-admin";

export default function Page() {
  const { logout, user } = useAuth();
  const { t, tDashboard } = useTranslation();
  const router = useRouter();

  const navigation = {
    icon: Megaphone,
    baseHref: `${BASE_URL}/announcements`,
    title: t('navigation.announcements')
  };

  // State management
  const [announcements, setAnnouncements] = useState<AnnouncementSchema[]>([]);
  const [filteredAnnouncements, setFilteredAnnouncements] = useState<AnnouncementSchema[]>([]);
  const [selectedAnnouncements, setSelectedAnnouncements] = useState<AnnouncementSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  const [announcementToDelete, setAnnouncementToDelete] = useState<AnnouncementSchema | null>(null);
  const [announcementToEdit, setAnnouncementToEdit] = useState<AnnouncementSchema | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
  const [bulkDeleteType, setBulkDeleteType] = useState<"selected" | "all">("selected");
  const [tableKey, setTableKey] = useState(0);

  // Notification state
  const [isNotificationCard, setIsNotificationCard] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState<"success" | "error">("success");

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [targetFilter, setTargetFilter] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);

  // Fetch announcements data
  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        if (user && user.school_ids && user.school_ids.length > 0) {
          const schoolId = user.school_ids[0];
          const announcementsData = await getAnnouncementsBySchool(schoolId);
          setAnnouncements(announcementsData);
          setFilteredAnnouncements(announcementsData);
        }
      } catch (error) {
        console.error("Error fetching announcements:", error);
        setNotificationMessage("Failed to load announcements");
        setNotificationType("error");
        setIsNotificationCard(true);
      } finally {
        setLoadingData(false);
      }
    };

    if (user) {
      fetchAnnouncements();
    }
  }, [user]);

  // Filter announcements
  useEffect(() => {
    let filtered = announcements;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(ann =>
        ann.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ann.content.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Priority filter
    if (priorityFilter !== "all") {
      filtered = filtered.filter(ann => ann.priority === priorityFilter);
    }

    // Status filter
    if (statusFilter !== "all") {
      const isPublished = statusFilter === "published";
      filtered = filtered.filter(ann => ann.is_published === isPublished);
    }

    // Target audience filter
    if (targetFilter !== "all") {
      filtered = filtered.filter(ann => ann.target_audience === targetFilter);
    }

    setFilteredAnnouncements(filtered);
  }, [announcements, searchTerm, priorityFilter, statusFilter, targetFilter]);

  // Handle create/edit announcement
  const handleSaveAnnouncement = async (announcementData: AnnouncementCreateSchema) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      if (user && user.school_ids && user.school_ids.length > 0) {
        const dataWithSchool = {
          ...announcementData,
          school_id: user.school_ids[0],
          author_id: user._id
        };

        if (announcementToEdit) {
          // Update existing announcement
          const updatedAnnouncement = await updateAnnouncement(announcementToEdit.announcement_id, dataWithSchool);
          setAnnouncements(prev => 
            prev.map(ann => ann._id === announcementToEdit._id ? updatedAnnouncement : ann)
          );
          setNotificationMessage("Announcement updated successfully!");
        } else {
          // Create new announcement
          const newAnnouncement = await createAnnouncement(dataWithSchool);
          setAnnouncements(prev => [newAnnouncement, ...prev]);
          setNotificationMessage("Announcement created successfully!");
        }

        setNotificationType("success");
        setIsNotificationCard(true);
        setSubmitStatus("success");

        // Close modal after success
        setTimeout(() => {
          setIsModalOpen(false);
          setAnnouncementToEdit(null);
          setSubmitStatus(null);
        }, 2000);
      }
    } catch (error) {
      console.error("Error saving announcement:", error);
      setNotificationMessage("Failed to save announcement");
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete single announcement
  const handleDelete = async (password: string) => {
    if (!announcementToDelete) return;

    setIsSubmitting(true);
    setSubmitStatus(null);

    // Verify password
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    if (!passwordVerified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      await deleteAnnouncement(announcementToDelete.announcement_id);
      setAnnouncements(prev => prev.filter(ann => ann._id !== announcementToDelete._id));
      setNotificationMessage("Announcement deleted successfully!");
      setNotificationType("success");
      setIsNotificationCard(true);
      setSubmitStatus("success");

      // Close modal after success
      setTimeout(() => {
        setIsDeleteModalOpen(false);
        setAnnouncementToDelete(null);
        setSubmitStatus(null);
      }, 2000);
    } catch (error) {
      console.error("Error deleting announcement:", error);
      setNotificationMessage("Failed to delete announcement");
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle bulk delete
  const handleDeleteMultiple = async (selectedIds: string[]) => {
    if (selectedIds.length === 0) {
      setNotificationMessage("No announcements selected for deletion.");
      setNotificationType("error");
      setIsNotificationCard(true);
      return;
    }
    setBulkDeleteType("selected");
    setIsBulkDeleteModalOpen(true);
  };

  // Handle delete all
  const handleDeleteAll = async () => {
    if (announcements.length === 0) {
      setNotificationMessage("No announcements to delete.");
      setNotificationType("error");
      setIsNotificationCard(true);
      return;
    }
    setBulkDeleteType("all");
    setIsBulkDeleteModalOpen(true);
  };

  // Handle bulk delete confirmation
  const handleBulkDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    // Verify password
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    if (!passwordVerified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      if (bulkDeleteType === "all") {
        // Delete all announcements for this school
        const announcementIds = announcements.map(ann => ann.announcement_id);
        await deleteMultipleAnnouncements(announcementIds);
        setAnnouncements([]);
        setNotificationMessage("All announcements deleted successfully!");
      } else {
        // Delete selected announcements
        const selectedIds = selectedAnnouncements.map(ann => ann.announcement_id);
        await deleteMultipleAnnouncements(selectedIds);
        setAnnouncements(prev => prev.filter(ann => !selectedIds.includes(ann.announcement_id)));
        setNotificationMessage(`${selectedAnnouncements.length} announcement(s) deleted successfully!`);
      }

      setNotificationType("success");
      setIsNotificationCard(true);
      setSubmitStatus("success");
      setSelectedAnnouncements([]);
      setTableKey(prev => prev + 1);

      // Close modal after success
      setTimeout(() => {
        setIsBulkDeleteModalOpen(false);
        setSubmitStatus(null);
      }, 2000);
    } catch (error) {
      console.error("Error in bulk delete:", error);
      setNotificationMessage("Failed to delete announcements");
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get priority badge color
  const getPriorityBadge = (priority: string) => {
    const colors = {
      urgent: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      high: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      low: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
    };
    return colors[priority as keyof typeof colors] || colors.medium;
  };

  // Get status badge
  const getStatusBadge = (isPublished: boolean) => {
    return isPublished
      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
      : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
  };

  // Card action handlers
  const handleView = (announcement: AnnouncementSchema) => {
    router.push(`${BASE_URL}/announcements/view?id=${announcement.announcement_id}`);
  };

  const handleEdit = (announcement: AnnouncementSchema) => {
    setAnnouncementToEdit(announcement);
    setIsModalOpen(true);
  };

  const handleDeleteCard = (announcement: AnnouncementSchema) => {
    setAnnouncementToDelete(announcement);
    setIsDeleteModalOpen(true);
  };

  // Toggle selection
  const toggleSelection = (announcement: AnnouncementSchema) => {
    setSelectedAnnouncements(prev => {
      const isSelected = prev.some(ann => ann._id === announcement._id);
      if (isSelected) {
        return prev.filter(ann => ann._id !== announcement._id);
      } else {
        return [...prev, announcement];
      }
    });
  };

  const clearFilters = () => {
    setSearchTerm("");
    setPriorityFilter("all");
    setStatusFilter("all");
    setTargetFilter("all");
  };



  return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <Suspense fallback={<CircularLoader />}>
          <div className="space-y-6">
            {/* Notification */}
            {isNotificationCard && (
              <NotificationCard
                message={notificationMessage}
                type={notificationType}
                onClose={() => setIsNotificationCard(false)}
                isVisible={isNotificationCard}
                isFixed={true}
              />
            )}

            {/* Header with Add Button */}
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-foreground">{tDashboard('school-admin', 'announcements', 'page_title')}</h1>
                <p className="text-foreground/60">{tDashboard('school-admin', 'announcements', 'page_subtitle')}</p>
              </div>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 300 }}
                onClick={() => setIsModalOpen(true)}
                className="flex items-center gap-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus className="h-4 w-4" />
                {tDashboard('school-admin', 'announcements', 'add_new_announcement')}
              </motion.button>
            </div>

            {/* Search and Filters */}
            <div className="bg-widget rounded-lg border border-stroke p-4">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Search */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/60" />
                    <input
                      type="text"
                      placeholder={tDashboard('school-admin', 'announcements', 'search_placeholder')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-stroke rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-teal"
                    />
                  </div>
                </div>

                {/* Filter Toggle */}
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 px-4 py-2 border border-stroke rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <Filter className="h-4 w-4" />
                  {tDashboard('school-admin', 'announcements', 'filters')}
                </button>

                {/* Bulk Actions */}
                {selectedAnnouncements.length > 0 && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-foreground/60">
                      {selectedAnnouncements.length} {tDashboard('school-admin', 'announcements', 'selected')}
                    </span>
                    <button
                      onClick={() => handleDeleteMultiple(selectedAnnouncements.map(ann => ann.announcement_id))}
                      className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                      {tDashboard('school-admin', 'announcements', 'delete_selected')}
                    </button>
                  </div>
                )}
              </div>

              {/* Expanded Filters */}
              {showFilters && (
                <div className="mt-4 pt-4 border-t border-stroke">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">{tDashboard('school-admin', 'announcements', 'priority')}</label>
                      <select
                        value={priorityFilter}
                        onChange={(e) => setPriorityFilter(e.target.value)}
                        className="w-full px-3 py-2 border border-stroke rounded-md bg-background text-foreground"
                      >
                        <option value="all">{tDashboard('school-admin', 'announcements', 'all_priorities')}</option>
                        <option value="urgent">{tDashboard('school-admin', 'announcements', 'urgent')}</option>
                        <option value="high">{tDashboard('school-admin', 'announcements', 'high')}</option>
                        <option value="medium">{tDashboard('school-admin', 'announcements', 'medium')}</option>
                        <option value="low">{tDashboard('school-admin', 'announcements', 'low')}</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">{tDashboard('school-admin', 'announcements', 'status')}</label>
                      <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        className="w-full px-3 py-2 border border-stroke rounded-md bg-background text-foreground"
                      >
                        <option value="all">{tDashboard('school-admin', 'announcements', 'all_status')}</option>
                        <option value="published">{tDashboard('school-admin', 'announcements', 'published')}</option>
                        <option value="draft">{tDashboard('school-admin', 'announcements', 'draft')}</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">{tDashboard('school-admin', 'announcements', 'target_audience')}</label>
                      <select
                        value={targetFilter}
                        onChange={(e) => setTargetFilter(e.target.value)}
                        className="w-full px-3 py-2 border border-stroke rounded-md bg-background text-foreground"
                      >
                        <option value="all">{tDashboard('school-admin', 'announcements', 'all_audiences')}</option>
                        <option value="all">{tDashboard('school-admin', 'announcements', 'everyone')}</option>
                        <option value="teachers">{tDashboard('school-admin', 'announcements', 'teachers')}</option>
                        <option value="parents">{tDashboard('school-admin', 'announcements', 'parents')}</option>
                        <option value="students">{tDashboard('school-admin', 'announcements', 'students')}</option>
                      </select>
                    </div>

                    <div className="flex items-end">
                      <button
                        onClick={clearFilters}
                        className="px-4 py-2 text-sm bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-foreground rounded-md transition-colors"
                      >
                        {tDashboard('school-admin', 'announcements', 'clear_filters')}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Modals */}
            {isModalOpen && (
              <CreateAnnouncementModal
                onClose={() => {
                  setIsModalOpen(false);
                  setAnnouncementToEdit(null);
                  setSubmitStatus(null);
                }}
                onSave={handleSaveAnnouncement}
                initialData={announcementToEdit}
                submitStatus={submitStatus}
                isSubmitting={isSubmitting}
              />
            )}

            {isDeleteModalOpen && announcementToDelete && (
              <DeleteAnnouncementModal
                announcementTitle={announcementToDelete.title}
                onClose={() => {
                  setIsDeleteModalOpen(false);
                  setAnnouncementToDelete(null);
                  setSubmitStatus(null);
                }}
                onDelete={handleDelete}
                submitStatus={submitStatus}
                isSubmitting={isSubmitting}
              />
            )}

            {isBulkDeleteModalOpen && (
              <BulkDeleteModal
                isOpen={isBulkDeleteModalOpen}
                onClose={() => {
                  setIsBulkDeleteModalOpen(false);
                  setSubmitStatus(null);
                }}
                onConfirm={handleBulkDeleteConfirm}
                title={bulkDeleteType === "all" ? tDashboard('school-admin', 'announcements', 'delete_all_title') : tDashboard('school-admin', 'announcements', 'delete_selected_title')}
                message={
                  bulkDeleteType === "all"
                    ? tDashboard('school-admin', 'announcements', 'delete_all_message')
                    : tDashboard('school-admin', 'announcements', 'delete_selected_message')
                }
                itemCount={bulkDeleteType === "all" ? announcements.length : selectedAnnouncements.length}
                itemType="announcements"
                isDeleteAll={bulkDeleteType === "all"}
                submitStatus={submitStatus}
                isSubmitting={isSubmitting}
                requirePassword={true}
              />
            )}

            {/* Announcements Grid */}
            <div className="bg-widget rounded-lg border border-stroke p-6">
              {loadingData ? (
                <div className="flex items-center justify-center py-12">
                  <CircularLoader size={32} color="teal" />
                </div>
              ) : filteredAnnouncements.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <Megaphone className="h-12 w-12 text-foreground/30 mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">{tDashboard('school-admin', 'announcements', 'no_announcements_found')}</h3>
                  <p className="text-foreground/60 text-center mb-4">
                    {announcements.length === 0
                      ? tDashboard('school-admin', 'announcements', 'get_started_message')
                      : tDashboard('school-admin', 'announcements', 'adjust_filters_message')
                    }
                  </p>
                  {announcements.length === 0 && (
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setIsModalOpen(true)}
                      className="flex items-center gap-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
                    >
                      <Plus className="h-4 w-4" />
                      {tDashboard('school-admin', 'announcements', 'create_first_announcement')}
                    </motion.button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-foreground/60">
                      {t('common.showing_count', {
                        filtered: filteredAnnouncements.length,
                        total: announcements.length,
                        type: tDashboard('school-admin', 'announcements', 'title').toLowerCase()
                      })}
                    </p>
                    {announcements.length > 0 && (
                      <button
                        onClick={handleDeleteAll}
                        className="text-sm text-red-600 hover:text-red-700 font-medium"
                      >
                        {tDashboard('school-admin', 'announcements', 'delete_all')}
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                    {filteredAnnouncements.map((announcement, index) => (
                      <motion.div
                        key={announcement._id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="relative"
                      >
                        {/* Selection Checkbox */}
                        <div className="absolute top-2 left-2 z-10">
                          <input
                            type="checkbox"
                            checked={selectedAnnouncements.some(ann => ann._id === announcement._id)}
                            onChange={() => toggleSelection(announcement)}
                            className="w-4 h-4 text-teal bg-white border-gray-300 rounded focus:ring-teal focus:ring-2"
                          />
                        </div>

                        <AnnouncementCard
                          announcement={announcement}
                          onView={handleView}
                          onEdit={handleEdit}
                          onDelete={handleDeleteCard}
                          showActions={{ view: true, edit: true, delete: true }}
                        />
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </Suspense>
      </SchoolLayout>
  );
}
