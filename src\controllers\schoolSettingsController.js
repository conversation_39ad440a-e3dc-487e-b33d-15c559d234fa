const SchoolSettings = require('../models/SchoolSettings');

const getSchoolSettings = async (req, res) => {
  try {
    const { school_id } = req.params;

    const settings = await SchoolSettings.findOne({ school_id });

    if (!settings) {
      return res.status(404).json({ message: 'Settings not found for this school' });
    }

    return res.status(200).json(settings);
  } catch (err) {
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};

const upsertSchoolSettings = async (req, res) => {
  try {
    const { school_id } = req.body;

    if (!school_id) {
      return res.status(400).json({ message: 'school_id is required' });
    }

    const settings = await SchoolSettings.findOneAndUpdate(
      { school_id },
      { $set: req.body },
      { new: true, upsert: true, runValidators: true }
    );

    return res.status(200).json(settings);
  } catch (err) {
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};

const deleteSchoolSettings = async (req, res) => {
  try {
    const { school_id } = req.params;

    const deleted = await SchoolSettings.findOneAndDelete({ school_id });

    if (!deleted) {
      return res.status(404).json({ message: 'Settings not found or already deleted' });
    }

    return res.status(200).json({ message: 'Settings deleted successfully' });
  } catch (err) {
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};

module.exports = {
  getSchoolSettings,
  upsertSchoolSettings,
  deleteSchoolSettings
};
