"use client";

import React, { useState } from "react";
import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import { Settings, User, CreditCard, SlidersHorizontal } from "lucide-react";
import NotificationCard from "@/components/NotificationCard";
import { useTranslation } from "@/hooks/useTranslation";
import ProfileSettings from "./components/ProfileSettings"; // Assuming path
import CreditSettings from "./components/CreditSettings"; // Assuming path
import GeneralSettings from "./components/GeneralSettings"; // Assuming path

// Import the SettingsProvider and useSettings hook
import { useSettings } from "@/context/SuperAdminSettingsContext";
import CircularLoader from "@/components/widgets/CircularLoader";

// Inner component that uses the settings context
const SettingsContent: React.FC = () => {
    const { tDashboard } = useTranslation();

    // Consume the settings context
    const {
        user,
        settings,
        isLoading,
        profileForm,
        setProfileForm,
        newAvatarFile, // Not directly used here, but part of context
        setNewAvatarFile, // Not directly used here, but part of context
        avatarPreviewUrl,
        setAvatarPreviewUrl,
        creditForm,
        setCreditForm,
        generalForm,
        setGeneralForm,
        handleAvatarChange,
        handleProfileSubmit,
        handleCreditSubmit,
        handleGeneralSubmit,
        notificationMessage,
        notificationType,
        isNotificationCard,
        setNotification,
        setIsNotificationCard,
    } = useSettings();

    const [activeTab, setActiveTab] = useState<"profile" | "credit" | "general">(
        "profile"
    );

    if (isLoading)
        return <div>
            <CircularLoader/>
        </div>;

    return (
        <>
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    icon={
                        notificationType === "success" ? (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path
                                    d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                                    stroke="#15803d"
                                    strokeWidth="1.5"
                                />
                                <path
                                    d="M7.75 11.9999L10.58 14.8299L16.25 9.16992"
                                    stroke="#15803d"
                                    strokeWidth="1.5"
                                />
                            </svg>
                        ) : (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="10" stroke="#dc2626" strokeWidth="2" />
                                <path
                                    d="M8 8L16 16M16 8L8 16"
                                    stroke="#dc2626"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                />
                            </svg>
                        )
                    }
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                />
            )}

            <div className="flex flex-col md:flex-row gap-8 p-6 w-full max-w-6xl mx-auto ">
                <div className="flex flex-col space-y-2 w-full md:w-1/4 ">
                    <button
                        className={`w-full text-left px-4 py-3 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${activeTab === "profile"
                                ? "bg-teal text-white shadow-md"
                                : "bg-background text-foreground hover:bg-background-darker"
                            }`}
                        onClick={() => setActiveTab("profile")}
                    >
                        <User className="h-5 w-5" />
                        <span>{tDashboard("super-admin", "settings", "profile_tab")}</span>
                    </button>
                    <button
                        className={`w-full text-left px-4 py-3 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${activeTab === "credit"
                                ? "bg-teal text-white shadow-md"
                                : "bg-background text-foreground hover:bg-background-darker"
                            }`}
                        onClick={() => setActiveTab("credit")}
                    >
                        <CreditCard className="h-5 w-5" />
                        <span>{tDashboard("super-admin", "settings", "credit_tab")}</span>
                    </button>
                    <button
                        className={`w-full text-left px-4 py-3 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${activeTab === "general"
                                ? "bg-teal text-white shadow-md"
                                : "bg-background text-foreground hover:bg-background-darker"
                            }`}
                        onClick={() => setActiveTab("general")}
                    >
                        <SlidersHorizontal className="h-5 w-5" />
                        <span>{tDashboard("super-admin", "settings", "general_tab")}</span>
                    </button>
                </div>

                <div className="flex-1 w-full md:w-3/4 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
                    {activeTab === "profile" && (
                        <ProfileSettings
                            user={user}
                            profileForm={profileForm}
                            setProfileForm={setProfileForm}
                            avatarPreviewUrl={avatarPreviewUrl}
                            handleAvatarChange={handleAvatarChange}
                            handleProfileSubmit={handleProfileSubmit}
                        />
                    )}

                    {activeTab === "credit" && (
                        <CreditSettings
                            creditForm={creditForm}
                            setCreditForm={setCreditForm}
                            handleCreditSubmit={handleCreditSubmit}
                        />
                    )}

                    {activeTab === "general" && (
                        <GeneralSettings
                            generalForm={generalForm}
                            setGeneralForm={setGeneralForm}
                            handleGeneralSubmit={handleGeneralSubmit}
                        />
                    )}
                </div>
            </div>
        </>
    );
};

// Main SettingsPage component that provides the layout
const SettingsPageContent: React.FC = () => {
    const { tDashboard } = useTranslation();
    const BASE_URL = "/super-admin";
    const navigation = {
        icon: Settings,
        baseHref: `${BASE_URL}/settings`,
        title: tDashboard("super-admin", "settings", "title"),
    };

    return (
        <SuperLayout
            navigation={navigation}
            showGoPro={true}
            onLogout={() => console.log("Logged out")} // Placeholder for logout
        >
            <SettingsContent />
        </SuperLayout>
    );
};

export default SettingsPageContent;
